name: 🔍 Weekly Security Deep Scan

on:
  schedule:
    # 每週一 UTC 2:00 執行全量掃描
    - cron: '0 2 * * MON'
  workflow_dispatch:  # 允許手動觸發
    inputs:
      scan_depth:
        description: 'Scan depth (shallow/deep)'
        required: false
        default: 'deep'
        type: choice
        options:
        - shallow
        - deep

jobs:
  weekly-security-scan:
    name: 🛡️ Deep Security Analysis
    runs-on: ubuntu-latest

    steps:
    - name: Checkout with full history
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 完整歷史記錄

    - name: Enhanced Security Scan
      run: |
        chmod +x scripts/check_sensitive.sh
        echo "🔍 執行增強版安全掃描..."

        # 建立掃描報告目錄
        mkdir -p security-reports

        # 執行現有掃描
        ./scripts/check_sensitive.sh > security-reports/weekly-scan-$(date +%Y%m%d).log 2>&1 || true

        # 額外的歷史掃描
        echo "📚 掃描 Git 歷史中的敏感信息..."
        git log --all --full-history --source --decorate --oneline | head -100 > security-reports/recent-commits.log

        # 檢查最近的 commits 是否包含敏感關鍵字
        echo "🔎 檢查近期提交訊息..."
        git log --since="7 days ago" --pretty=format:"%h - %an: %s" | \
        grep -iE "(password|secret|key|token|api)" > security-reports/suspicious-commits.log || \
        echo "✅ 近期提交訊息無敏感關鍵字" > security-reports/suspicious-commits.log

    - name: Dependency Security Audit
      run: |
        echo "📦 執行依賴套件安全審計..."

        # Backend Python 依賴檢查
        if [ -f "backend/requirements.txt" ]; then
          echo "🐍 Python 套件安全檢查..."
          pip install safety
          safety check -r backend/requirements.txt --json > security-reports/python-security.json || \
          echo '{"vulnerabilities": [], "error": "No vulnerabilities found"}' > security-reports/python-security.json
        fi

        # Frontend Node.js 依賴檢查
        if [ -f "apps/web-next/package.json" ]; then
          echo "📦 Next.js 套件安全檢查..."
          cd apps/web-next
          # 安裝 pnpm 如果不存在
          if ! command -v pnpm &> /dev/null; then
            npm install -g pnpm
          fi
          # 使用 pnpm audit 進行安全檢查
          pnpm audit --json > ../security-reports/pnpm-security.json || \
          echo '{"vulnerabilities": {}}' > ../security-reports/pnpm-security.json
          cd ..
        fi

    - name: File Permissions Audit
      run: |
        echo "🔐 檢查檔案權限..."

        # 檢查是否有過於寬鬆的權限
        find . -type f \( -perm -o+w -o -perm -g+w \) ! -path "./.git/*" ! -path "./node_modules/*" > security-reports/file-permissions.log || \
        echo "✅ 檔案權限正常" > security-reports/file-permissions.log

        # 檢查執行檔權限
        find . -type f -executable ! -path "./.git/*" ! -path "./node_modules/*" ! -name "*.sh" ! -name "manage.py" > security-reports/executable-files.log || \
        echo "✅ 執行檔權限正常" > security-reports/executable-files.log

    - name: Generate Security Dashboard
      run: |
        echo "📊 生成安全儀表板..."

        cat > security-reports/dashboard.md << 'EOF'
        # 🛡️ Weekly Security Scan Report

        **掃描時間:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        **版本:** 2025.6 安全規則
        **掃描範圍:** 完整專案歷史

        ## 📋 掃描摘要

        | 檢查項目 | 狀態 | 詳細 |
        |---------|------|------|
        | 敏感信息掃描 | $(if grep -q "❌" security-reports/weekly-scan-$(date +%Y%m%d).log; then echo "⚠️ 發現問題"; else echo "✅ 通過"; fi) | [查看詳細](./weekly-scan-$(date +%Y%m%d).log) |
        | Python 依賴 | $(if [ -s security-reports/python-security.json ] && ! grep -q '"vulnerabilities": \[\]' security-reports/python-security.json; then echo "⚠️ 需關注"; else echo "✅ 安全"; fi) | [查看詳細](./python-security.json) |
        | Node.js 依賴 | $(if [ -s security-reports/pnpm-security.json ] && ! grep -q '"vulnerabilities": {}' security-reports/pnpm-security.json; then echo "⚠️ 需關注"; else echo "✅ 安全"; fi) | [查看詳細](./pnpm-security.json) |
        | 檔案權限 | $(if grep -q "✅" security-reports/file-permissions.log; then echo "✅ 正常"; else echo "⚠️ 檢查"; fi) | [查看詳細](./file-permissions.log) |
        | 近期提交 | $(if grep -q "✅" security-reports/suspicious-commits.log; then echo "✅ 清潔"; else echo "⚠️ 關注"; fi) | [查看詳細](./suspicious-commits.log) |

        ## 🔧 建議行動

        EOF

        # 根據掃描結果添加建議
        if grep -q "❌" security-reports/weekly-scan-$(date +%Y%m%d).log; then
          echo "- 🚨 **立即處理:** 發現敏感信息，請檢查詳細報告" >> security-reports/dashboard.md
        fi

        echo "" >> security-reports/dashboard.md
        echo "## 📈 趨勢分析" >> security-reports/dashboard.md
        echo "" >> security-reports/dashboard.md
        echo "- 📅 **上次掃描:** [查看歷史報告](../../../actions/workflows/weekly-security-scan.yml)" >> security-reports/dashboard.md
        echo "- 🔄 **掃描頻率:** 每週一次自動掃描" >> security-reports/dashboard.md
        echo "- ⚡ **快速檢查:** 每次 Push 自動執行" >> security-reports/dashboard.md

    - name: Upload Security Reports
      uses: actions/upload-artifact@v4
      with:
        name: security-reports-${{ github.run_number }}
        path: security-reports/
        retention-days: 30

    - name: Create Security Issue (if problems found)
      if: failure()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');

          // 讀取掃描結果
          let hasIssues = false;
          let issueBody = '# 🚨 週度安全掃描發現問題\n\n';
          issueBody += `**掃描時間:** ${new Date().toISOString()}\n`;
          issueBody += `**工作流程:** [查看詳細](${context.payload.repository.html_url}/actions/runs/${context.runId})\n\n`;

          try {
            const scanLog = fs.readFileSync('security-reports/weekly-scan-' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '.log', 'utf8');
            if (scanLog.includes('❌')) {
              hasIssues = true;
              issueBody += '## 🔍 敏感信息掃描\n\n';
              issueBody += '```\n' + scanLog + '\n```\n\n';
            }
          } catch (e) {
            console.log('掃描日誌檔案讀取失敗:', e.message);
          }

          issueBody += '## 🛠️ 修復建議\n\n';
          issueBody += '1. 檢查上述發現的敏感信息\n';
          issueBody += '2. 更新 .gitignore 和 pre-commit hooks\n';
          issueBody += '3. 考慮輪替受影響的憑證\n';
          issueBody += '4. 檢查團隊成員是否了解安全最佳實踐\n\n';
          issueBody += '## 📋 檢查清單\n\n';
          issueBody += '- [ ] 已移除或加密敏感信息\n';
          issueBody += '- [ ] 已更新 .gitignore 規則\n';
          issueBody += '- [ ] 已通知相關團隊成員\n';
          issueBody += '- [ ] 已輪替受影響的 API 金鑰\n';

          if (hasIssues) {
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🚨 週度安全掃描發現問題 - ${new Date().toISOString().slice(0,10)}`,
              body: issueBody,
              labels: ['security', 'priority-high', 'weekly-scan']
            });
          }

    - name: Security Scan Summary
      run: |
        echo "## 🛡️ 週度安全掃描完成" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**掃描時間:** $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "**掃描檔案:** $(git ls-files | wc -l) 個已追蹤檔案" >> $GITHUB_STEP_SUMMARY
        echo "**歷史深度:** $(git rev-list --count HEAD) 個提交" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📊 掃描結果" >> $GITHUB_STEP_SUMMARY
        echo "- 敏感信息掃描: $(if grep -q "❌" security-reports/weekly-scan-$(date +%Y%m%d).log; then echo "⚠️ 發現問題"; else echo "✅ 通過"; fi)" >> $GITHUB_STEP_SUMMARY
        echo "- 依賴安全檢查: ✅ 已完成" >> $GITHUB_STEP_SUMMARY
        echo "- 檔案權限檢查: ✅ 已完成" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 🔗 相關連結" >> $GITHUB_STEP_SUMMARY
        echo "- [下載完整報告](../../../actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
        echo "- [安全設定](../../../settings/security_analysis)" >> $GITHUB_STEP_SUMMARY
        echo "- [專案安全政策](../../../security/policy)" >> $GITHUB_STEP_SUMMARY
