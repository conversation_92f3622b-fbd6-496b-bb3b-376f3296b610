# Docker Compose 本地開發環境
# 🎯 目標：統一 Node 版本，熱重載，ARM64 原生支援
# 🚀 策略：volume 掛載 + 依賴快取 + 開發伺服器

version: "3.8"

services:
  # ========================================
  # Frontend 開發服務（Next.js 15）
  # ========================================
  frontend-dev:
    build:
      context: .
      dockerfile: infra/docker/frontend-dev.Dockerfile
      platforms:
        - linux/arm64 # ARM64 原生支援，避免跨平台
    container_name: novel-frontend-dev
    ports:
      - "3000:3000" # Next.js dev server
    volumes:
      # 🚀 關鍵：直接掛載整個專案，與本機一致
      - .:/app:cached
      # 保留 node_modules 在容器內（避免本地/容器版本衝突）
      - /app/node_modules
      - /app/apps/web-next/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
      - HOST=0.0.0.0
      - PORT=3000
    depends_on:
      backend-dev:
        condition: service_healthy
    working_dir: /app/apps/web-next
    command: ["pnpm", "dev"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "dev.novelwebsite.service=frontend"
      - "dev.novelwebsite.hot-reload=enabled"
      - "dev.novelwebsite.architecture=arm64"

  # ========================================
  # Backend 開發服務（Django + 資料庫）
  # ========================================
  backend-dev:
    build:
      context: .
      dockerfile: infra/docker/backend-tier2.Dockerfile # 使用現有的 Dockerfile
      platforms:
        - linux/arm64
    container_name: novel-backend-dev
    ports:
      - "8000:8000" # Django dev server
    volumes:
      - ./backend:/app/backend:cached
      - ./scripts:/app/scripts:cached
    environment:
      - DJANGO_SETTINGS_MODULE=backend.settings.development
      - DEBUG=1
      - DATABASE_URL=************************************************/novelwebsite_dev
      - REDIS_URL=redis://redis-dev:6379/0
    depends_on:
      postgres-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
    working_dir: /app
    command:
      [
        "/wait-for-it.sh",
        "postgres-dev:5432",
        "--",
        "python",
        "manage.py",
        "runserver",
        "0.0.0.0:8000",
      ]
    healthcheck:
      test:
        ["CMD-SHELL", "curl -f http://localhost:8000/api/v1/health/ || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s
    labels:
      - "dev.novelwebsite.service=backend"

  # ========================================
  # 資料庫服務
  # ========================================
  postgres-dev:
    image: postgres:15-alpine
    container_name: novel-postgres-dev
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=novelwebsite_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d novelwebsite_dev"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 10s
    labels:
      - "dev.novelwebsite.service=database"

  redis-dev:
    image: redis:7-alpine
    container_name: novel-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 5s
    labels:
      - "dev.novelwebsite.service=cache"

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  default:
    name: novel-dev-network
