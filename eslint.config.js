// ESLint 9 Flat Config for NovelWebsite monorepo
import js from "@eslint/js";
import tsParser from "@typescript-eslint/parser";

export default [
  // Global ignores (必須在最前面)
  {
    ignores: [
      "**/node_modules/**",
      "**/.next/**",
      "**/out/**",
      "**/coverage/**",
      "**/dist/**",
      "**/build/**",
      "**/.turbo/**",
      "**/e2e/**", // 排除 Playwright E2E 測試
      "backend/**", // 排除 Python 後端
      "scripts/**/*.cjs", // 排除 CommonJS 腳本
      "playwright-report/**", // 排除 Playwright 報告
    ],
  },

  // TypeScript files in Next.js app
  {
    files: ["apps/web-next/**/*.{ts,tsx}"],
    languageOptions: {
      parser: tsParser,
      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        // Browser globals for React/Next.js
        window: "readonly",
        document: "readonly",
        console: "readonly",
        setTimeout: "readonly",
        clearTimeout: "readonly",
        setInterval: "readonly",
        clearInterval: "readonly",
        fetch: "readonly",
        // Node.js globals for Next.js server-side
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        global: "readonly",
        // React globals
        React: "readonly",
        JSX: "readonly",
      },
    },
    rules: {
      "no-unused-vars": "off", // TypeScript會處理這個
      "no-console": "off", // 允許在開發中使用console
      "no-undef": "off", // TypeScript會處理這個
    },
  },

  // JavaScript files in Next.js app
  {
    files: ["apps/web-next/**/*.{js,jsx}"],
    ...js.configs.recommended,
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        // Browser globals for React/Next.js
        window: "readonly",
        document: "readonly",
        console: "readonly",
        setTimeout: "readonly",
        clearTimeout: "readonly",
        setInterval: "readonly",
        clearInterval: "readonly",
        fetch: "readonly",
        // Node.js globals for Next.js server-side
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        global: "readonly",
        // React globals
        React: "readonly",
        JSX: "readonly",
      },
    },
    rules: {
      "no-unused-vars": "warn",
      "no-console": "off", // 允許在開發中使用console
      "no-undef": "off", // TypeScript會處理這個
    },
  },

  // TypeScript files in packages
  {
    files: ["packages/**/*.{ts,tsx}"],
    languageOptions: {
      parser: tsParser,
      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        // Node.js globals for packages
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        global: "readonly",
        console: "readonly",
      },
    },
    rules: {
      "no-unused-vars": "off", // TypeScript會處理這個
      "no-console": "warn",
      "no-undef": "off", // TypeScript會處理這個
    },
  },

  // JavaScript files in packages
  {
    files: ["packages/**/*.{js,jsx}"],
    ...js.configs.recommended,
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        // Node.js globals for packages
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        global: "readonly",
        console: "readonly",
      },
    },
    rules: {
      "no-unused-vars": "warn",
      "no-console": "warn",
      "no-undef": "off",
    },
  },

  // Root level JavaScript files
  {
    files: ["*.{js,mjs}"],
    ...js.configs.recommended,
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      globals: {
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        global: "readonly",
        console: "readonly",
      },
    },
    rules: {
      "no-unused-vars": "warn",
      "no-console": "off",
      "no-undef": "off",
    },
  },
];
