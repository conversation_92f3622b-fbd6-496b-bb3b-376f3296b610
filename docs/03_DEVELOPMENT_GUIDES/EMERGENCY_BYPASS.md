# Git Hooks 緊急跳過指南

本文件說明在緊急情況下如何安全地跳過 Git hooks 檢查。

## ⚠️ 重要警告

**僅在緊急情況下使用這些跳過選項**，並且必須確保：
1. 有適當的手動審查
2. CI/CD 流程會進行完整檢查
3. 在下一次正常提交時恢復完整檢查

## 🚫 可用的跳過選項

### 1. 跳過 Docker 驗證

```bash
# 跳過 Docker 建置驗證（pre-push）
export SKIP_DOCKER_VERIFY=true
git push
```

**何時使用**：
- Docker daemon 故障但程式碼變更緊急
- 網路問題導致 ECR 無法訪問
- 本地環境問題但 CI 正常

**安全措施**：
- 本地環境需要手動確認（輸入 `BYPASS_DOCKER_VERIFY`）
- CI 環境自動跳過確認但記錄警告
- 建議在 PR 中明確標註使用了跳過選項

### 2. 跳過敏感信息檢查

```bash
# 跳過敏感信息檢查（pre-commit）
export SKIP_SENSITIVE_CHECK=true
git commit -m "emergency commit"
```

**何時使用**：
- 敏感檢查腳本誤報且無法快速修復
- 緊急修復但檢查工具故障

**安全措施**：
- 必須手動審查所有變更檔案
- 確保沒有真正的敏感信息
- 在下次提交時修復檢查問題

## 🔐 最佳實踐

### 使用前檢查清單

- [ ] 確認這是真正的緊急情況
- [ ] 已嘗試修復根本問題但無法快速解決
- [ ] 有其他同事可以進行 code review
- [ ] CI/CD 流程會進行完整檢查
- [ ] 已在 PR/commit message 中記錄跳過原因

### 使用後處理

1. **立即文檔化**：在 PR 或 commit message 中記錄：
   - 跳過的檢查類型
   - 跳過的原因
   - 後續修復計畫

2. **儘快修復**：
   - 修復導致需要跳過的根本問題
   - 驗證修復後的檢查正常工作
   - 考慮是否需要改進檢查流程

3. **通知團隊**：
   - 在團隊聊天中通知使用了跳過選項
   - 分享學習到的經驗
   - 討論是否需要流程改進

## 📝 範例使用情境

### 情境 1：Docker daemon 故障

```bash
# 問題：本地 Docker 無法啟動，但需要緊急修復線上問題
export SKIP_DOCKER_VERIFY=true
git push origin hotfix/critical-bug

# Commit message 範例：
# fix: resolve critical API timeout issue
# 
# EMERGENCY: Used SKIP_DOCKER_VERIFY=true due to local Docker daemon failure
# Docker verification will be handled by CI pipeline
# TODO: Fix local Docker setup before next development cycle
```

### 情境 2：敏感檢查誤報

```bash
# 問題：敏感檢查誤報 .env.example 為敏感檔案
export SKIP_SENSITIVE_CHECK=true
git commit -m "fix: update environment variables example"

# 後續處理：
# 1. 修復敏感檢查規則
# 2. 驗證修復效果
# 3. 提交修復的檢查規則
```

## 🛡️ 安全注意事項

1. **永不跳過在 main 分支**：
   - main 分支應該始終保持最高品質
   - 使用 feature branch + PR 流程

2. **記錄所有跳過操作**：
   - 使用 git commit message
   - 在 PR description 中說明
   - 考慮建立 incident log

3. **定期審查跳過使用**：
   - 每月檢查跳過使用頻率
   - 分析是否有流程改進機會
   - 確保團隊理解正確使用方式

## 🔧 故障排除

### 常見問題

**Q: 為什麼本地需要手動確認？**
A: 防止意外跳過檢查，確保開發者有意識地做出決定。

**Q: CI 環境為什麼不需要確認？**
A: CI 環境通常是自動化的，但會記錄詳細日誌供後續審查。

**Q: 如何知道 CI 是否正常檢查？**
A: 檢查 GitHub Actions 日誌，確保 Docker 和敏感檢查步驟正常執行。

### 緊急聯絡

如果遇到跳過相關問題：
1. 檢查本文件的故障排除區段
2. 查看專案 Issues 是否有相關討論
3. 聯絡 DevOps 團隊成員

---

⚠️ **記住**：這些跳過選項是安全網，不是常規工具。優先修復根本問題而不是依賴跳過。

📅 最後更新：2025-06-30  
👥 負責人：DevOps Team