# Husky Git Hooks 快速參考

本文件提供 NovelWebsite 專案 Husky Git Hooks 的快速參考指南。

## 🎯 設計理念

**不是在「偷懶」，而是智慧優化**：

- **Pre-commit**：快速增量檢查，確保基本品質
- **Pre-push**：分支條件檢查，平衡效率與品質
- **Main 分支**：完整檢查，確保生產品質
- **Feature 分支**：快速回饋，提升開發效率

## 🔧 Hook 配置概覽

### Pre-commit Hook (`.husky/pre-commit`)

```bash
# 檢查項目（按執行順序）
1. 🔍 敏感信息檢查          # 掃描硬編碼秘密
2. 🚫 Backend 導入檢查      # 防止 backend.* 導入
3. 🔍 遺留路徑掃描          # 檢查 novel.* 遺留引用
4. 🔍 Lint-staged          # 增量格式化和檢查
```

**執行時間**：~10-30 秒（取決於變更檔案數量）

### Pre-push Hook (`.husky/pre-push`)

```bash
# 檢查項目（按執行順序）
1. 🧹 清理構建產物          # 確保乾淨的構建環境
2. 🏗️ Turbo Build         # 完整建置檢查
3. 🛡️ TypeScript 檢查      # 型別安全驗證
4. 🐳 Docker 驗證          # 分支條件檢查
```

**執行時間**：
- Feature 分支：~1-2 分鐘（快速 Docker 檢查）
- Main 分支：~3-5 分鐘（完整 Docker 驗證）

## 🎛️ 配置檔案

### 主要配置檔案

| 檔案 | 用途 | 說明 |
|------|------|------|
| `package.json` | Husky 腳本定義 | 定義可執行的檢查命令 |
| `scripts/ci/legacy-path-scanner.config.json` | 掃描規則 | 統一管理排除規則和模式 |
| `.husky/pre-commit` | Pre-commit 流程 | Git commit 時執行 |
| `.husky/pre-push` | Pre-push 流程 | Git push 時執行 |

### 關鍵腳本

| 腳本 | 目的 | 何時使用 |
|------|------|----------|
| `scripts/sensitive-check.js` | 敏感信息檢查 | 每次 commit |
| `scripts/ci/legacy-path-scanner.sh` | 遺留路徑檢查 | 每次 commit |
| `scripts/docker-verify-quick.sh` | 快速 Docker 驗證 | Feature 分支 push |
| `scripts/local-build-test.sh` | 完整 Docker 驗證 | Main 分支 push |

## 🚀 日常使用指南

### 正常開發流程

```bash
# 1. 開發功能
git add .
git commit -m "feat: add new feature"  # 觸發 pre-commit

# 2. 推送變更
git push origin feature/new-feature    # 觸發 pre-push (快速檢查)
```

### 合併到 Main

```bash
# 切換到 main 分支
git checkout main
git merge feature/new-feature
git push origin main                   # 觸發 pre-push (完整檢查)
```

## ⚡ 效能優化特性

### 分支條件檢查

| 分支類型 | Docker 檢查類型 | 執行時間 | 說明 |
|----------|----------------|----------|------|
| Feature 分支 | 快速語法檢查 | ~30 秒 | `docker buildx build --check` |
| Main 分支 | 完整建置驗證 | ~3-5 分鐘 | 完整 Docker 建置 + 快取測試 |

### 增量檢查

- **Lint-staged**：只檢查 `git add` 的檔案
- **敏感檢查**：支援檔案列表參數，避免全專案掃描
- **TypeScript**：利用 Turbo 增量建置快取

### 配置外部化

```json
// scripts/ci/legacy-path-scanner.config.json
{
  "excludeFiles": ["removed-dirs/", "legacy-files/"],
  "legacyPatterns": ["novel\\.settings", "novel\\.views[^.]"]
}
```

**優勢**：
- 團隊成員只需修改 JSON，無需碰 shell script
- Git 追蹤配置變更歷史
- 降低出錯風險

## 🚨 緊急情況處理

### 快速跳過選項

```bash
# 跳過 Docker 驗證（需手動確認）
export SKIP_DOCKER_VERIFY=true
git push

# 跳過敏感檢查（不建議）
export SKIP_SENSITIVE_CHECK=true
git commit -m "emergency fix"

# 完全跳過所有檢查（最後手段）
git push --no-verify
```

### 安全措施

1. **本地確認**：非 CI 環境需要輸入確認字串
2. **記錄警告**：所有跳過操作都會記錄警告信息
3. **文檔要求**：需在 commit message 或 PR 中說明跳過原因

## 🔍 故障排除

### 常見問題快速修復

| 問題 | 解決方案 | 相關文檔 |
|------|----------|----------|
| 敏感檢查誤報 | 修改 `scripts/sensitive-check.js` 規則 | [Emergency Bypass](./EMERGENCY_BYPASS.md) |
| Docker 檢查失敗 | 檢查 Docker daemon，考慮使用 `SKIP_DOCKER_VERIFY` | [Git Hooks Migration](./GIT_HOOKS_MIGRATION.md) |
| 遺留路徑誤報 | 更新 `legacy-path-scanner.config.json` | 本文件 |
| 權限問題 | `chmod +x .husky/*` | [Git Hooks Migration](./GIT_HOOKS_MIGRATION.md) |

### 檢查 Hook 狀態

```bash
# 檢查 Husky 是否正確安裝
ls -la .husky/

# 測試 pre-commit hook
.husky/pre-commit

# 測試快速 Docker 驗證
pnpm run docker-verify-quick

# 檢查配置檔案
cat scripts/ci/legacy-path-scanner.config.json | jq '.'
```

## 📚 延伸閱讀

- [Git Hooks 遷移指南](./GIT_HOOKS_MIGRATION.md) - 完整遷移過程和技術細節
- [緊急跳過指南](./EMERGENCY_BYPASS.md) - 緊急情況處理流程
- [專案開發原則](../../CLAUDE.md) - 整體開發規範

## ❓ 常見問答

**Q: 為什麼 Feature 分支不做完整 Docker 檢查？**
A: 平衡開發效率與品質控制。完整檢查在 main 分支和 CI 中進行，確保生產品質。

**Q: 配置檔案可以放在其他位置嗎？**
A: 可以，使用 `--config=/path/to/config.json` 參數指定自訂位置。

**Q: 如何添加新的掃描模式？**
A: 編輯 `scripts/ci/legacy-path-scanner.config.json` 中的 `legacyPatterns` 陣列。

**Q: Hook 檢查失敗了，如何找到具體問題？**
A: 查看終端輸出的詳細錯誤信息，通常會指出具體的檔案和行號。

---

📅 最後更新：2025-06-30  
🎯 目標：提供清晰的 Git Hooks 使用指南，確保團隊理解設計理念  
💡 記住：這些優化是為了提升效率，不是為了「偷懶」