{"$schema": "https://turbo.build/schema.json", "ui": "tui", "remoteCache": {"signature": true}, "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "!**/*.stories.*", "!**/*.test.*", "!**/tests/**", "!**/coverage/**"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**", "storybook-static/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "NEXT_PUBLIC_*", "REACT_APP_*"]}, "lint": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "eslint.config.*"], "outputs": []}, "test": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "jest.config.*", "vitest.config.*"], "outputs": ["coverage/**"]}, "type-check": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "tsconfig*.json"], "outputs": []}, "test:e2e": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "playwright.config.*", "e2e/**"], "outputs": ["test-results/**", "playwright-report/**"], "env": ["CI"]}, "clean": {"cache": false}, "storybook": {"cache": false, "persistent": true}, "build-storybook": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".storybook/**"], "outputs": ["storybook-static/**"]}}, "globalDependencies": ["package.json", "pnpm-workspace.yaml", "turbo.json", "packages/*/package.json"]}