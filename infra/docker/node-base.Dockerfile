# Private Node.js Base Image
# 🎯 目標：創建私有的 Node.js base image，避免 Docker Hub rate limit
# 🚀 策略：Pull 一次 node:20-alpine，推送到私有 ECR，後續使用私有版本

FROM node:20-alpine

LABEL maintainer="NovelWebsite DevOps Team"
LABEL description="Private Node.js base image to avoid Docker Hub rate limits"
LABEL version="20-alpine-private"
LABEL registry="ECR-private"
LABEL purpose="rate-limit-avoidance"

# 驗證 Node.js 和基礎工具
RUN node --version && npm --version && apk --version

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node --version || exit 1

# 預設命令
CMD ["node", "--version"]