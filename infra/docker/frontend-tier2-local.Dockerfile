# Frontend Dockerfile - 本地測試版本
# 解決 CI 中 ECR base image 依賴問題

# 🚀 使用私有 ECR 中的 Node Base Image，避免 Docker Hub rate limit
FROM 509399605447.dkr.ecr.ap-northeast-1.amazonaws.com/novel-node-base:20-alpine AS base

LABEL stage="base-environment"
LABEL tier="2.3-local-test"

# 安裝基礎系統依賴
RUN apk add --no-cache \
    git \
    openssh-client \
    && corepack enable \
    && corepack prepare pnpm@9.4.0 --activate \
    && echo "✅ 基礎環境準備完成"

WORKDIR /workspace

# ====================================
# Stage 2: 依賴安裝
# ====================================
FROM base AS dependencies

LABEL stage="dependencies-local"

# 複製 package 管理檔案
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY turbo.json ./

# 建立工作空間目錄結構
RUN mkdir -p ./apps/web-next ./packages/tailwind-config ./packages/typescript-config ./packages/api ./packages/ui

# 複製各個 package.json
COPY apps/web-next/package.json ./apps/web-next/
COPY packages/ui/package.json ./packages/ui/
COPY packages/api/package.json ./packages/api/
COPY packages/tailwind-config/package.json ./packages/tailwind-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/

# 安裝依賴
RUN echo "📦 安裝依賴..." && \
    pnpm install --frozen-lockfile && \
    echo "✅ 依賴安裝完成"

# ====================================
# Stage 3: 應用構建層
# ====================================
FROM dependencies AS builder

LABEL stage="application-builder"

# 複製所有必要的源碼
COPY apps/web-next/ ./apps/web-next/
COPY packages/ ./packages/

# 切換到 Next.js 應用目錄並執行建置
WORKDIR /workspace/apps/web-next
RUN echo "🏗️ 開始建置應用程式..." && \
    echo "📊 確認 node_modules 存在: $(ls -la ../../node_modules/.bin | wc -l) 個可執行檔" && \
    echo "📊 檢查工作空間依賴:" && \
    cat ../../pnpm-workspace.yaml && \
    echo "📊 列出工作空間依賴:" && \
    pnpm list --depth=0 || echo "依賴列表檢查失敗" && \
    echo "🏗️ 開始 Next.js 構建..." && \
    NODE_ENV=production pnpm build && \
    echo "✅ 應用程式建置完成"

# ====================================
# Stage 4: 生產運行環境
# ====================================
FROM base AS production

LABEL tier="2.3-local-optimized"
LABEL maintainer="NovelWebsite DevOps Team"

WORKDIR /workspace

# 創建用戶
RUN adduser -D -s /bin/sh ci-user

# 複製運行時檔案
COPY --chown=ci-user:ci-user --from=dependencies /workspace/node_modules ./node_modules
COPY --chown=ci-user:ci-user --from=builder /workspace/apps/web-next/.next ./.next
COPY --chown=ci-user:ci-user --from=builder /workspace/apps/web-next/public ./public
COPY --chown=ci-user:ci-user --from=builder /workspace/apps/web-next/package.json ./
COPY --chown=ci-user:ci-user --from=builder /workspace/apps/web-next/next.config.js ./

# 設置環境變數
ENV NODE_ENV=production
ENV CI=true
ENV PATH="/workspace/node_modules/.bin:$PATH"

# 切換到非 root 用戶
USER ci-user

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node --version || exit 1

# 預設命令
CMD ["sh"]