# Monorepo Frontend CI Dockerfile v2 - Next.js 15
# 設計目標：Next.js 15 App Router + 智能緩存優化
# 取代舊版 CRA 構建，專注於 apps/web-next

# ========================================
# Stage 1: Base Node.js Environment
# ========================================
# 🚀 使用私有 ECR 中的 Node Base Image，避免 Docker Hub rate limit
FROM 509399605447.dkr.ecr.ap-northeast-1.amazonaws.com/novel-node-base:20-alpine AS base

# 安裝 pnpm 全局
RUN npm install -g pnpm@latest

# 設置工作目錄
WORKDIR /workspace

# 安裝系統依賴
RUN apk add --no-cache \
    git \
    curl \
    bash

# ========================================
# Stage 2: Install Dependencies
# ========================================
FROM base AS dependencies

# 複製 Monorepo 配置文件
COPY package.json ./
COPY pnpm-workspace.yaml ./
COPY pnpm-lock.yaml ./
COPY turbo.json ./

# 複製 Next.js 15 應用的 package.json（優先緩存）
COPY apps/web-next/package.json ./apps/web-next/

# 複製共享套件的 package.json
COPY packages/*/package.json ./packages/*/

# 安裝依賴（這一層在 package.json 和 pnpm-lock.yaml 不變時會被緩存）
RUN pnpm install --frozen-lockfile

# ========================================
# Stage 3: Application Build
# ========================================
FROM dependencies AS builder

# 複製 Next.js 源代碼
COPY apps/web-next/ ./apps/web-next/

# 複製共享套件源代碼
COPY packages/ ./packages/

# 構建 Next.js 應用
WORKDIR /workspace/apps/web-next
RUN pnpm build

# ========================================
# Stage 4: Production Runtime
# ========================================
# 🚀 使用私有 ECR 中的 Node Base Image，避免 Docker Hub rate limit
FROM 509399605447.dkr.ecr.ap-northeast-1.amazonaws.com/novel-node-base:20-alpine AS production

# 安裝 pnpm
RUN npm install -g pnpm@latest

# 設置工作目錄
WORKDIR /workspace

# 複製構建產物和必要文件
COPY --from=builder /workspace/apps/web-next/.next ./apps/web-next/.next
COPY --from=builder /workspace/apps/web-next/public ./apps/web-next/public
COPY --from=builder /workspace/apps/web-next/package.json ./apps/web-next/
COPY --from=builder /workspace/apps/web-next/next.config.js ./apps/web-next/

# 複製 node_modules (生產依賴)
COPY --from=builder /workspace/node_modules ./node_modules
COPY --from=builder /workspace/apps/web-next/node_modules ./apps/web-next/node_modules

# 複製 Monorepo 配置
COPY --from=builder /workspace/package.json ./
COPY --from=builder /workspace/pnpm-workspace.yaml ./

# 設置環境變數
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 暴露端口
EXPOSE 3000

# 工作目錄切換到 Next.js 應用
WORKDIR /workspace/apps/web-next

# 啟動 Next.js 應用
CMD ["pnpm", "start"]
