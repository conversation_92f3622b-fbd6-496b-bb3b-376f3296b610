# Frontend Base Image - Dependencies Cache Foundation
# 🎯 目標：建立純依賴層 Base Image，作為所有 frontend Dockerfile 的 --cache-from 基底
# 🚀 策略：僅安裝依賴，不包含應用程式碼

# 🚀 使用私有 ECR 中的 Node Base Image，避免 Docker Hub rate limit
FROM 509399605447.dkr.ecr.ap-northeast-1.amazonaws.com/novel-node-base:20-alpine AS base

LABEL stage="frontend-base-dependencies"
LABEL tier="cache-from-foundation"
LABEL description="Pure dependencies base image for cache optimization"
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="3.0-base-image"
LABEL registry-purpose="ECR-cache-foundation"

# 安裝系統依賴和 pnpm
RUN apk add --no-cache \
    git \
    openssh-client \
    && corepack enable \
    && corepack prepare pnpm@9.4.0 --activate \
    && echo "✅ 基礎環境準備完成"

# 🔑 統一工作目錄為 /workspace（所有 frontend builds 必須使用相同路徑）
WORKDIR /workspace

# 🚀 關鍵優化：僅複製依賴定義檔，最大化 Docker 層快取
# Monorepo 完整配置：根目錄有 pnpm-lock.yaml，子包有 package.json
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY turbo.json ./

# 複製子包的 package.json（維持目錄結構以支援 workspace）
# 先建立目錄結構
RUN mkdir -p ./apps/web-next ./packages/tailwind-config ./packages/typescript-config ./packages/api ./packages/ui

COPY apps/web-next/package.json ./apps/web-next/
COPY packages/tailwind-config/package.json ./packages/tailwind-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/api/package.json ./packages/api/
COPY packages/ui/package.json ./packages/ui/

# 🚀 Base Image 依賴安裝：BuildKit cache mount + pnpm store 持久化
# 產出：純依賴層，不包含應用程式碼
RUN --mount=type=cache,target=/root/.pnpm/store,id=pnpm-store \
    --mount=type=cache,target=/tmp/.pnpm-cache,id=pnpm-temp \
    echo "📦 開始安裝 Base Image 依賴..." && \
    pnpm install --frozen-lockfile && \
    pnpm store prune && \
    echo "✅ Base Image 依賴安裝完成" && \
    echo "📊 node_modules 大小: $(du -sh node_modules | cut -f1)" && \
    echo "🎯 此 Base Image 將被推送至 ECR 作為快取基底"

# 設置 Base Image 環境變數
ENV NODE_ENV=production
ENV PNPM_HOME="/workspace/node_modules/.bin"
ENV PATH="$PNPM_HOME:$PATH"

# 健康檢查：確保 Base Image 依賴完整
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD pnpm --version && turbo --version && test -d /workspace/node_modules || exit 1

# Base Image 性能標記
LABEL cache-strategy="base-image-foundation"
LABEL turbo-support="enabled"
LABEL workspace-support="pnpm-monorepo"
LABEL usage-pattern="FROM base-image AS deps"
LABEL ecr-purpose="cache-from-registry"
LABEL dependencies-only="true"

# 預設命令：顯示 Base Image 狀態
CMD ["sh", "-c", "echo 'Frontend Base Image ready - Dependencies cached at /workspace/node_modules'; ls -la /workspace/node_modules/.bin | head -5"]
