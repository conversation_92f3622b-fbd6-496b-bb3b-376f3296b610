/** @type {import('next').NextConfig} */
const nextConfig = {
  // 基本配置
  reactStrictMode: true,

  // Monorepo 支援
  transpilePackages: [
    "@novelwebsite/tailwind-config",
    "@novelwebsite/typescript-config",
    "@novelwebsite/api",
    "@novelwebsite/ui",
  ],

  // Turbopack 配置 (Next.js 15 穩定版)
  turbopack: {
    rules: {
      "*.svg": {
        loaders: ["@svgr/webpack"],
        as: "*.js",
      },
    },
  },

  // 環境變數配置
  env: {
    NEXT_PUBLIC_APP_NAME: "NovelWebsite Next.js",
    NEXT_PUBLIC_APP_VERSION: "1.0.0",
    NEXT_PUBLIC_BUILD_TIME: new Date().toISOString(),
  },

  // API 路由配置
  async rewrites() {
    return [
      {
        source: "/api/v1/:path*",
        destination: `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"}/api/v1/:path*`,
      },
      {
        source: "/media/:path*",
        destination: `${process.env.NEXT_PUBLIC_MEDIA_URL || "http://localhost:8000"}/media/:path*`,
      },
    ];
  },

  // 圖片優化配置
  images: {
    domains: ["localhost"],
    formats: ["image/webp", "image/avif"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 輸出配置
  output: "standalone",

  // 壓縮配置
  compress: true,

  // 頁面擴展名
  pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],

  // Webpack 配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 支援 SVG 作為 React 組件
    config.module.rules.push({
      test: /\.svg$/i,
      issuer: /\.[jt]sx?$/,
      use: ["@svgr/webpack"],
    });

    // 支援 Monorepo 中的模組解析
    config.resolve.alias = {
      ...config.resolve.alias,
      "@": require("path").resolve(__dirname, "./"),
      "@/components": require("path").resolve(__dirname, "./components"),
      "@/lib": require("path").resolve(__dirname, "./lib"),
      "@/app": require("path").resolve(__dirname, "./app"),
    };

    // 優化 bundle 分析
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: "all",
        cacheGroups: {
          default: false,
          vendors: false,
          // 第三方庫
          vendor: {
            name: "vendor",
            chunks: "all",
            test: /node_modules/,
            priority: 20,
          },
          // 共享組件
          common: {
            name: "common",
            minChunks: 2,
            chunks: "all",
            priority: 10,
            reuseExistingChunk: true,
            enforce: true,
          },
        },
      };
    }

    return config;
  },

  // 標頭配置
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
        ],
      },
    ];
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: "/home",
        destination: "/",
        permanent: true,
      },
    ];
  },

  // 開發伺服器配置
  ...(process.env.NODE_ENV === "development" && {
    devIndicators: {
      buildActivity: true,
      buildActivityPosition: "bottom-right",
    },
  }),
};

module.exports = nextConfig;
