/**
 * E2E 測試配置
 * 
 * 根據環境判斷測試行為
 */

import { test as base, type TestType } from '@playwright/test'

export const testConfig = {
  // 判斷是否在 CI 環境
  isCI: process.env.CI === 'true',
  
  // 判斷 Backend 是否可用
  isBackendAvailable: process.env.BACKEND_AVAILABLE === 'true',
  
  // 測試超時時間
  timeout: process.env.CI === 'true' ? 30000 : 60000,
  
  // 跳過需要 Backend 的測試
  skipBackendTests: process.env.CI === 'true' && process.env.BACKEND_AVAILABLE !== 'true',
}

// 條件跳過測試的 helper
export function skipIfNoBackend(test: TestType<any, any>) {
  return testConfig.skipBackendTests 
    ? test.skip 
    : test
}