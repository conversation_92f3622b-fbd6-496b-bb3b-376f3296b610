import { test, expect, type Page } from '@playwright/test'

/**
 * 小說路由 E2E 測試
 * 
 * 測試小說詳情和章節閱讀的路由功能
 * 注意：這些測試假設 Django backend 沒有運行，會測試錯誤處理
 */

test.describe('小說路由測試', () => {
  test('小說詳情頁 - 錯誤處理', async ({ page }: { page: Page }) => {
    // 訪問一個模擬的小說詳情頁
    await page.goto('/novels/test-novel-123')
    
    // 由於沒有 Django backend，應該顯示錯誤或 404
    const hasError = await page.locator('text=載入小說時發生錯誤').isVisible()
    const hasNotFound = await page.locator('text=小說未找到').isVisible()
    const hasLoading = await page.locator('[role="status"]').isVisible()
    
    // 應該顯示錯誤、404 或載入狀態之一
    expect(hasError || hasNotFound || hasLoading).toBeTruthy()
    
    // 檢查頁面結構
    await expect(page.locator('main')).toBeVisible()
  })

  test('章節閱讀頁 - 錯誤處理', async ({ page }: { page: Page }) => {
    // 訪問一個模擬的章節頁面
    await page.goto('/novels/test-novel-123/chapters/1')
    
    // 由於沒有 Django backend，應該顯示錯誤或 404
    const hasError = await page.locator('text=載入章節時發生錯誤').isVisible()
    const hasNotFound = await page.locator('text=章節未找到').isVisible()
    const hasLoading = await page.locator('[role="status"]').isVisible()
    
    // 應該顯示錯誤、404 或載入狀態之一
    expect(hasError || hasNotFound || hasLoading).toBeTruthy()
  })

  test('小說詳情頁 - not-found 處理', async ({ page }: { page: Page }) => {
    // 訪問明確不存在的小說
    await page.goto('/novels/definitely-not-exist-999999')
    
    // 等待頁面載入
    await page.waitForTimeout(3000)
    
    // 檢查是否顯示適當的錯誤訊息
    const hasNotFound = await page.locator('text=小說未找到').isVisible()
    const hasError = await page.locator('text=載入小說時發生錯誤').isVisible()
    
    expect(hasNotFound || hasError).toBeTruthy()
    
    // 檢查是否有返回首頁的連結
    const homeLink = page.locator('a[href="/"]')
    if (await homeLink.count() > 0) {
      await expect(homeLink.first()).toBeVisible()
    }
  })

  test('章節頁面 - not-found 處理', async ({ page }: { page: Page }) => {
    // 訪問明確不存在的章節
    await page.goto('/novels/test-novel/chapters/999999')
    
    // 等待頁面載入
    await page.waitForTimeout(3000)
    
    // 檢查是否顯示適當的錯誤訊息
    const hasNotFound = await page.locator('text=章節未找到').isVisible()
    const hasError = await page.locator('text=載入章節時發生錯誤').isVisible()
    
    expect(hasNotFound || hasError).toBeTruthy()
  })

  test('路由參數格式驗證', async ({ page }: { page: Page }) => {
    // 測試各種 slug 格式
    const testSlugs = [
      'test-novel-123',
      '測試小說-456',
      'novel-with-special-chars-789',
      '123' // 純數字 ID
    ]

    for (const slug of testSlugs) {
      await page.goto(`/novels/${slug}`)
      
      // 等待頁面載入
      await page.waitForTimeout(1000)
      
      // 檢查頁面是否正常載入（即使顯示錯誤也算正常）
      await expect(page.locator('main')).toBeVisible()
      
      // 檢查 URL 是否正確
      expect(page.url()).toContain(`/novels/${slug}`)
    }
  })

  test('SEO meta 標籤測試', async ({ page }: { page: Page }) => {
    // 訪問小說詳情頁
    await page.goto('/novels/test-novel-123')
    
    // 等待頁面載入
    await page.waitForTimeout(2000)
    
    // 檢查基本 meta 標籤
    const title = await page.title()
    expect(title).toBeTruthy()
    expect(title.length).toBeGreaterThan(0)
    
    // 檢查 meta description
    const metaDescription = page.locator('meta[name="description"]')
    if (await metaDescription.count() > 0) {
      const content = await metaDescription.getAttribute('content')
      expect(content).toBeTruthy()
    }
    
    // 檢查 Open Graph 標籤
    const ogTitle = page.locator('meta[property="og:title"]')
    if (await ogTitle.count() > 0) {
      const content = await ogTitle.getAttribute('content')
      expect(content).toBeTruthy()
    }
  })

  test('載入狀態和骨架屏', async ({ page }: { page: Page }) => {
    // 模擬慢速網路
    await page.route('**/*', route => {
      setTimeout(() => route.continue(), 100)
    })
    
    // 訪問小說詳情頁
    await page.goto('/novels/test-novel-123')
    
    // 檢查是否顯示載入狀態
    const loadingElements = page.locator('[role="status"], .animate-pulse')
    const hasLoading = await loadingElements.count() > 0
    
    // 應該有載入指示器或骨架屏
    if (hasLoading) {
      await expect(loadingElements.first()).toBeVisible()
    }
    
    // 等待載入完成
    await page.waitForTimeout(3000)
  })

  test('錯誤邊界功能', async ({ page }: { page: Page }) => {
    // 訪問可能觸發錯誤的頁面
    await page.goto('/novels/test-novel-123')
    
    // 等待頁面載入
    await page.waitForTimeout(3000)
    
    // 檢查是否有錯誤邊界組件
    const errorBoundary = page.locator('text=載入小說時發生錯誤')
    if (await errorBoundary.isVisible()) {
      // 檢查是否有重試按鈕
      const retryButton = page.locator('text=重試載入')
      if (await retryButton.isVisible()) {
        await retryButton.click()
        // 等待重試完成
        await page.waitForTimeout(2000)
      }
      
      // 檢查是否有返回首頁按鈕
      const homeButton = page.locator('text=返回首頁')
      if (await homeButton.isVisible()) {
        await expect(homeButton).toBeVisible()
      }
    }
  })
})
