import { test, expect } from '@playwright/test'

/**
 * 核心路由 E2E 測試
 * 
 * 測試完整的用戶流程和 SSR/CSR 切換
 */

test.describe('核心路由測試', () => {
  test('首頁載入和 SSR 渲染', async ({ page }) => {
    // 訪問首頁
    await page.goto('/')
    
    // 檢查頁面標題
    await expect(page).toHaveTitle(/小說閱讀平台/)
    
    // 檢查主要元素是否存在
    await expect(page.locator('h1')).toContainText('小說閱讀平台')
    
    // 檢查導航是否存在
    await expect(page.locator('nav')).toBeVisible()
    
    // 檢查是否有小說列表或載入狀態
    const novelSection = page.locator('[id="latest-novels"]')
    await expect(novelSection).toBeVisible()
    
    // 等待可能的載入完成
    await page.waitForTimeout(2000)
    
    // 檢查是否有小說卡片或空狀態
    const hasNovels = await page.locator('[data-testid="novel-card"]').count() > 0
    const hasEmptyState = await page.locator('text=暫無小說內容').isVisible()
    
    expect(hasNovels || hasEmptyState).toBeTruthy()
  })

  test('UI 測試頁面功能', async ({ page }) => {
    // 訪問 UI 測試頁面
    await page.goto('/ui-test')
    
    // 檢查頁面標題
    await expect(page.locator('h1')).toContainText('UI 組件測試頁面')
    
    // 檢查 LoadingSpinner 組件
    await expect(page.locator('text=LoadingSpinner 組件')).toBeVisible()
    
    // 檢查 NovelCard 組件
    await expect(page.locator('text=NovelCard 組件')).toBeVisible()
    
    // 檢查 ChapterCard 組件
    await expect(page.locator('text=ChapterCard 組件')).toBeVisible()
    
    // 檢查 Context API 測試
    await expect(page.locator('text=Context API 測試')).toBeVisible()
    
    // 測試載入按鈕
    const loadingButton = page.locator('text=開始載入')
    await loadingButton.click()
    await expect(page.locator('text=停止載入')).toBeVisible()
    await page.locator('text=停止載入').click()
    
    // 測試認證功能
    const loginButton = page.locator('text=測試登入')
    if (await loginButton.isVisible()) {
      await loginButton.click()
      await expect(page.locator('text=已登入')).toBeVisible()
      
      // 測試登出
      await page.locator('text=登出').click()
      await expect(page.locator('text=未登入')).toBeVisible()
    }
    
    // 測試設定功能
    const themeSelect = page.locator('select').first()
    await themeSelect.selectOption('dark')
    await expect(themeSelect).toHaveValue('dark')
  })

  test('404 頁面處理', async ({ page }) => {
    // 訪問不存在的頁面
    await page.goto('/non-existent-page')
    
    // 檢查是否顯示 404 頁面
    await expect(page.locator('text=404')).toBeVisible()
  })

  test('API 測試頁面', async ({ page }) => {
    // 訪問 API 測試頁面
    await page.goto('/api-test-v2')
    
    // 檢查頁面標題
    await expect(page.locator('h1')).toContainText('API 客戶端測試')
    
    // 檢查測試按鈕
    await expect(page.locator('text=開始測試')).toBeVisible()
    
    // 點擊測試按鈕（如果 Django backend 沒有運行，會顯示錯誤，這是正常的）
    await page.locator('text=開始測試').click()
    
    // 等待測試完成
    await page.waitForTimeout(5000)
    
    // 檢查是否有測試結果
    const hasResults = await page.locator('text=測試結果').isVisible()
    expect(hasResults).toBeTruthy()
  })

  test('響應式設計測試', async ({ page }) => {
    // 測試桌面版
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.goto('/')
    
    // 檢查桌面版導航
    await expect(page.locator('nav a').first()).toBeVisible()
    
    // 測試移動版
    await page.setViewportSize({ width: 375, height: 667 })
    await page.reload()
    
    // 檢查移動版漢堡菜單
    const mobileMenuButton = page.locator('button[aria-label="開啟主選單"]')
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click()
      // 檢查移動版菜單是否展開
      await expect(page.locator('nav a').first()).toBeVisible()
    }
  })

  test('頁面載入性能', async ({ page }) => {
    // 開始性能監控
    await page.goto('/', { waitUntil: 'networkidle' })
    
    // 檢查頁面是否在合理時間內載入
    const startTime = Date.now()
    await page.waitForLoadState('domcontentloaded')
    const loadTime = Date.now() - startTime
    
    // 載入時間應該少於 5 秒
    expect(loadTime).toBeLessThan(5000)
    
    // 檢查是否有 JavaScript 錯誤
    const errors: string[] = []
    page.on('pageerror', (error) => {
      errors.push(error.message)
    })
    
    // 等待一段時間收集可能的錯誤
    await page.waitForTimeout(2000)
    
    // 不應該有 JavaScript 錯誤
    expect(errors).toHaveLength(0)
  })
})
