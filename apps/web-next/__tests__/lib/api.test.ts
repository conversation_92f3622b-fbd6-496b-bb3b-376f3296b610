import { APIClient, APIError, novelsAPI } from "@/lib/api";

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe("APIClient", () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  describe("GET requests", () => {
    it("should make successful GET request", async () => {
      const mockData = { id: 1, title: "Test Novel" };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: async () => mockData,
      } as Response);

      const response = await APIClient.get("/novels/1");

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/v1/novels/1",
        expect.objectContaining({
          method: "GET",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Accept: "application/json",
          }),
        }),
      );

      expect(response.data).toEqual(mockData);
      expect(response.status).toBe(200);
    });

    it("should handle API errors", async () => {
      const errorData = { error: "Not found" };
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
        headers: new Headers({ "content-type": "application/json" }),
        json: async () => errorData,
      } as Response);

      await expect(APIClient.get("/novels/999")).rejects.toThrow(APIError);
    });
  });

  describe("POST requests", () => {
    it("should make successful POST request", async () => {
      const requestData = { title: "New Novel", author: "Test Author" };
      const responseData = { id: 1, ...requestData };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        statusText: "Created",
        headers: new Headers({ "content-type": "application/json" }),
        json: async () => responseData,
      } as Response);

      const response = await APIClient.post("/novels", requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/v1/novels",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
            Accept: "application/json",
          }),
          body: JSON.stringify(requestData),
        }),
      );

      expect(response.data).toEqual(responseData);
      expect(response.status).toBe(201);
    });
  });

  describe("Error handling", () => {
    it("should handle network errors", async () => {
      mockFetch.mockRejectedValueOnce(new TypeError("Network error"));

      await expect(APIClient.get("/novels")).rejects.toThrow(APIError);
    });

    it("should handle timeout errors", async () => {
      mockFetch.mockRejectedValueOnce(
        new DOMException("Timeout", "AbortError"),
      );

      await expect(APIClient.get("/novels")).rejects.toThrow(APIError);
    });

    it("should retry on server errors", async () => {
      // 第一次請求失敗
      mockFetch.mockRejectedValueOnce(new TypeError("Network error"));

      // 第二次請求成功
      const mockData = { novels: [] };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: async () => mockData,
      } as Response);

      const response = await APIClient.get("/novels");

      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(response.data).toEqual(mockData);
    });
  });
});

describe("novelsAPI", () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  describe("getList", () => {
    it("should fetch novels list", async () => {
      const mockData = { novels: [], total: 0 };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: async () => mockData,
      } as Response);

      const response = await novelsAPI.getList();

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/v1/novels",
        expect.any(Object),
      );
      expect(response.data).toEqual(mockData);
    });

    it("should handle query parameters", async () => {
      const mockData = { novels: [], total: 0 };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: async () => mockData,
      } as Response);

      await novelsAPI.getList({ page: 1, limit: 10 });

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/v1/novels?page=1&limit=10",
        expect.any(Object),
      );
    });
  });

  describe("getDetail", () => {
    it("should fetch novel detail", async () => {
      const mockData = { id: 1, title: "Test Novel" };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: async () => mockData,
      } as Response);

      const response = await novelsAPI.getDetail(1);

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/v1/novels/1",
        expect.any(Object),
      );
      expect(response.data).toEqual(mockData);
    });
  });

  describe("getChapters", () => {
    it("should fetch novel chapters", async () => {
      const mockData = { chapters: [], total: 0 };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: async () => mockData,
      } as Response);

      const response = await novelsAPI.getChapters(1);

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/v1/novels/1/chapters",
        expect.any(Object),
      );
      expect(response.data).toEqual(mockData);
    });
  });
});
