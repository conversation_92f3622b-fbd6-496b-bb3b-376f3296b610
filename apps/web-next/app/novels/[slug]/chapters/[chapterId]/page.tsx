import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { chaptersAPI, generateSlug, type ChapterNavigation } from '@novelwebsite/api'

interface PageProps {
  params: Promise<{ slug: string; chapterId: string }>
}

// ISR 配置：60 秒重新驗證
export const revalidate = 60

// 生成動態 metadata
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug, chapterId } = await params

  try {
    const navigation = await chaptersAPI.getChapterNavigation(parseInt(chapterId, 10))
    const { current: chapter, novel } = navigation

    return {
      title: `第 ${chapter.chapter_number} 章 ${chapter.title} - ${novel.title}`,
      description: `${novel.author} 的作品《${novel.title}》第 ${chapter.chapter_number} 章`,
      openGraph: {
        title: `第 ${chapter.chapter_number} 章 ${chapter.title}`,
        description: `${novel.title} - ${novel.author}`,
        type: 'article',
        authors: [novel.author],
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: '章節閱讀',
      description: '小說章節閱讀頁面',
    }
  }
}

// 獲取章節導航數據
async function getChapterNavigation(chapterId: string): Promise<ChapterNavigation | null> {
  try {
    return await chaptersAPI.getChapterNavigation(parseInt(chapterId, 10))
  } catch (error) {
    console.error('Error fetching chapter navigation:', error)
    return null
  }
}

export default async function ChapterPage({ params }: PageProps) {
  const { slug, chapterId } = await params
  const navigation = await getChapterNavigation(chapterId)

  if (!navigation) {
    notFound()
  }

  const { current: chapter, novel, previous, next } = navigation
  const novelSlug = generateSlug(novel.title, Number(novel.id))

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂部導航 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href={`/novels/${novelSlug}`}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                ← 返回小說詳情
              </Link>
              <div className="text-sm text-gray-500">
                {novel.title} / 第 {chapter.chapter_number} 章
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-600 hover:text-gray-800">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 章節內容 */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        <article className="bg-white rounded-lg shadow-sm p-8">
          {/* 章節標題 */}
          <header className="mb-8 text-center border-b pb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              第 {chapter.chapter_number} 章 {chapter.title}
            </h1>
            <div className="text-sm text-gray-500 space-x-4">
              <span>{novel.title}</span>
              <span>•</span>
              <span>{novel.author}</span>
              <span>•</span>
              <span>{new Date(chapter.updated_at).toLocaleDateString()}</span>
            </div>
          </header>

          {/* 章節正文 */}
          <div className="prose max-w-none">
            <div className="text-gray-800 leading-relaxed text-lg">
              {chapter.content ? (
                <div className="whitespace-pre-wrap">
                  {chapter.content}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <p>章節內容載入中...</p>
                </div>
              )}
            </div>
          </div>

          {/* 章節統計 */}
          <footer className="mt-8 pt-6 border-t text-center text-sm text-gray-500">
            <div className="flex justify-center space-x-6">
              <span>👁️ {chapter.views.toLocaleString()} 次閱讀</span>
              {chapter.is_vip && (
                <span className="text-yellow-600">👑 VIP 章節</span>
              )}
            </div>
          </footer>
        </article>

        {/* 章節導航 */}
        <nav className="mt-6 bg-white rounded-lg shadow-sm p-4">
          <div className="flex justify-between items-center">
            {previous ? (
              <Link
                href={`/novels/${novelSlug}/chapters/${previous.id}`}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>上一章</span>
              </Link>
            ) : (
              <div className="flex items-center space-x-2 text-gray-400">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>上一章</span>
              </div>
            )}

            <Link
              href={`/novels/${novelSlug}`}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              目錄
            </Link>

            {next ? (
              <Link
                href={`/novels/${novelSlug}/chapters/${next.id}`}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
              >
                <span>下一章</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            ) : (
              <div className="flex items-center space-x-2 text-gray-400">
                <span>下一章</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            )}
          </div>
        </nav>
      </main>
    </div>
  )
}
