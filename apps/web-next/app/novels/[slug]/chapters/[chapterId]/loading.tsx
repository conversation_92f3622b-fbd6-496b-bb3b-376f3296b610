/**
 * 章節閱讀頁載入骨架屏
 */

export default function ChapterLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂部導航骨架 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-24" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-32" />
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="w-9 h-9 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        </div>
      </header>

      {/* 章節內容骨架 */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        <article className="bg-white rounded-lg shadow-sm p-8">
          {/* 章節標題骨架 */}
          <header className="mb-8 text-center border-b pb-6">
            <div className="h-8 bg-gray-200 rounded animate-pulse mb-4 mx-auto w-2/3" />
            <div className="flex justify-center space-x-4">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-24" />
            </div>
          </header>

          {/* 章節正文骨架 */}
          <div className="prose max-w-none">
            <div className="space-y-4">
              {Array.from({ length: 15 }).map((_, index) => (
                <div
                  key={index}
                  className={`h-6 bg-gray-200 rounded animate-pulse ${
                    index % 4 === 3 ? 'w-3/4' : 'w-full'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* 章節統計骨架 */}
          <footer className="mt-8 pt-6 border-t text-center">
            <div className="flex justify-center space-x-6">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
            </div>
          </footer>
        </article>

        {/* 章節導航骨架 */}
        <nav className="mt-6 bg-white rounded-lg shadow-sm p-4">
          <div className="flex justify-between items-center">
            <div className="h-6 bg-gray-200 rounded animate-pulse w-16" />
            <div className="h-6 bg-gray-200 rounded animate-pulse w-12" />
            <div className="h-6 bg-gray-200 rounded animate-pulse w-16" />
          </div>
        </nav>
      </main>
    </div>
  )
}
