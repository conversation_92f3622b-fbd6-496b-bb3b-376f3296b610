import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { novelsAPI, generateSlug, type Novel } from '@novelwebsite/api'
import { ChapterCard } from '@novelwebsite/ui'

interface PageProps {
  params: Promise<{ slug: string }>
}

// ISR 配置：60 秒重新驗證
export const revalidate = 60

// 生成靜態參數 (預生成熱門小說)
export async function generateStaticParams() {
  // 在構建時不嘗試連接 API，使用空陣列以啟用動態路由
  return []
}

// 生成動態 metadata
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params
  
  try {
    const novel = await novelsAPI.getDetailBySlug(slug)
    
    if (!novel) {
      return {
        title: '小說未找到',
        description: '您查找的小說不存在或已被移除',
      }
    }

    return {
      title: `${novel.title} - ${novel.author} | 小說閱讀`,
      description: novel.description || `${novel.author} 的作品《${novel.title}》`,
      openGraph: {
        title: novel.title,
        description: novel.description || `${novel.author} 的作品`,
        type: 'article',
        authors: [novel.author],
        images: novel.cover ? [
          {
            url: novel.cover,
            alt: novel.title,
          }
        ] : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: novel.title,
        description: novel.description || `${novel.author} 的作品`,
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: '小說詳情',
      description: '小說閱讀平台',
    }
  }
}

// 獲取小說數據
async function getNovelData(slug: string): Promise<Novel | null> {
  try {
    return await novelsAPI.getDetailBySlug(slug)
  } catch (error) {
    console.error('Error fetching novel:', error)
    return null
  }
}

export default async function NovelDetailPage({ params }: PageProps) {
  const { slug } = await params
  const novel = await getNovelData(slug)

  if (!novel) {
    notFound()
  }

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* 小說封面 */}
          <div className="flex-shrink-0">
            <img
              src={novel.cover || '/default-cover.jpg'}
              alt={novel.title}
              className="w-48 h-64 object-cover rounded-lg shadow-md"
            />
          </div>

          {/* 小說資訊 */}
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {novel.title}
            </h1>
            
            <div className="space-y-2 text-gray-600 mb-4">
              <p>
                <span className="font-semibold">作者：</span>
                {novel.author}
              </p>
              
              <p>
                <span className="font-semibold">狀態：</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  novel.status === 'completed' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {novel.status === 'completed' ? '已完結' : '連載中'}
                </span>
              </p>
              
              {novel.category && (
                <p>
                  <span className="font-semibold">分類：</span>
                  {novel.category.name}
                </p>
              )}
              
              <div className="flex gap-4 text-sm">
                <span>👁️ {novel.views.toLocaleString()} 次觀看</span>
                <span>❤️ {novel.favorites.toLocaleString()} 收藏</span>
              </div>
            </div>

            {/* 操作按鈕 */}
            <div className="flex gap-3 mb-6">
              <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                開始閱讀
              </button>
              <button className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                加入收藏
              </button>
            </div>

            {/* 小說簡介 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                作品簡介
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {novel.description || '暫無簡介'}
              </p>
            </div>
          </div>
        </div>

        {/* 章節列表 */}
        <div className="mt-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            章節目錄
          </h2>
          
          {novel.chapters && novel.chapters.length > 0 ? (
            <div className="grid gap-2">
              {novel.chapters.slice(0, 20).map((chapter) => (
                <ChapterCard
                  key={chapter.id}
                  chapter={chapter}
                  href={`/novels/${slug}/chapters/${chapter.id}`}
                />
              ))}

              {novel.chapters.length > 20 && (
                <div className="text-center py-4">
                  <button className="text-blue-600 hover:text-blue-800">
                    載入更多章節...
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <p>暫無章節內容</p>
            </div>
          )}
        </div>
      </div>

      {/* 返回按鈕 */}
      <div className="mt-6">
        <Link
          href="/"
          className="inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          ← 返回首頁
        </Link>
      </div>
    </div>
  )
}
