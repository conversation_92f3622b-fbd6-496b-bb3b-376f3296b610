/**
 * 小說詳情頁載入骨架屏
 * 在 SSR 數據獲取期間顯示
 */

export default function NovelDetailLoading() {
  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* 封面骨架 */}
          <div className="flex-shrink-0">
            <div className="w-48 h-64 bg-gray-200 rounded-lg animate-pulse" />
          </div>

          {/* 資訊骨架 */}
          <div className="flex-1">
            {/* 標題骨架 */}
            <div className="h-8 bg-gray-200 rounded animate-pulse mb-4" />
            
            {/* 作者和狀態骨架 */}
            <div className="space-y-3 mb-6">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
              <div className="flex gap-4">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
              </div>
            </div>

            {/* 按鈕骨架 */}
            <div className="flex gap-3 mb-6">
              <div className="h-10 bg-gray-200 rounded-lg animate-pulse w-24" />
              <div className="h-10 bg-gray-200 rounded-lg animate-pulse w-24" />
            </div>

            {/* 簡介骨架 */}
            <div>
              <div className="h-6 bg-gray-200 rounded animate-pulse mb-3 w-32" />
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
              </div>
            </div>
          </div>
        </div>

        {/* 章節列表骨架 */}
        <div className="mt-8">
          <div className="h-6 bg-gray-200 rounded animate-pulse mb-4 w-32" />
          
          <div className="grid gap-2">
            {Array.from({ length: 10 }).map((_, index) => (
              <div
                key={index}
                className="p-3 border border-gray-200 rounded-lg"
              >
                <div className="flex justify-between items-center">
                  <div className="h-4 bg-gray-200 rounded animate-pulse flex-1 mr-4" />
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 返回按鈕骨架 */}
      <div className="mt-6">
        <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
      </div>
    </div>
  )
}
