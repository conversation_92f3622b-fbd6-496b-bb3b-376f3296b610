'use client'

import { useEffect } from 'react'
import Link from 'next/link'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function NovelDetailError({ error, reset }: ErrorProps) {
  useEffect(() => {
    // 記錄錯誤到監控服務
    console.error('Novel detail page error:', error)
  }, [error])

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        {/* 錯誤圖示 */}
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        </div>

        {/* 錯誤標題 */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          載入小說時發生錯誤
        </h1>

        {/* 錯誤描述 */}
        <p className="text-gray-600 mb-6">
          很抱歉，我們在載入這本小說的詳細資訊時遇到了問題。
          這可能是暫時的網路問題或伺服器錯誤。
        </p>

        {/* 錯誤詳情 (僅開發環境) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-6 p-4 bg-gray-100 rounded-lg text-left">
            <h3 className="font-semibold text-gray-900 mb-2">錯誤詳情：</h3>
            <pre className="text-sm text-gray-700 overflow-auto">
              {error.message}
            </pre>
            {error.digest && (
              <p className="text-xs text-gray-500 mt-2">
                錯誤 ID: {error.digest}
              </p>
            )}
          </div>
        )}

        {/* 操作按鈕 */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={reset}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            重試載入
          </button>
          
          <Link
            href="/"
            className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors inline-block"
          >
            返回首頁
          </Link>
        </div>

        {/* 幫助資訊 */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            如果問題持續發生，請嘗試：
          </p>
          <ul className="text-sm text-gray-500 mt-2 space-y-1">
            <li>• 檢查網路連線</li>
            <li>• 重新整理頁面</li>
            <li>• 稍後再試</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
