import Link from 'next/link'

export default function NovelNotFound() {
  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        {/* 404 圖示 */}
        <div className="mb-6">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center">
            <svg
              className="w-12 h-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              />
            </svg>
          </div>
        </div>

        {/* 404 標題 */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          小說未找到
        </h1>

        {/* 404 描述 */}
        <p className="text-gray-600 mb-8">
          很抱歉，您查找的小說不存在或已被移除。
          可能是連結錯誤，或者該小說已經下架。
        </p>

        {/* 建議操作 */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            您可以嘗試：
          </h2>
          <ul className="text-gray-600 space-y-2">
            <li>• 檢查網址是否正確</li>
            <li>• 使用搜尋功能尋找相關小說</li>
            <li>• 瀏覽熱門小說推薦</li>
            <li>• 返回首頁查看最新更新</li>
          </ul>
        </div>

        {/* 操作按鈕 */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center mb-8">
          <Link
            href="/"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回首頁
          </Link>
          
          <Link
            href="/search"
            className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            搜尋小說
          </Link>
        </div>

        {/* 熱門推薦 */}
        <div className="pt-6 border-t border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            熱門推薦
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Link
              href="/novels/example-1"
              className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
            >
              <h4 className="font-medium text-gray-900">熱門小說 1</h4>
              <p className="text-sm text-gray-600 mt-1">精彩的故事情節...</p>
            </Link>
            
            <Link
              href="/novels/example-2"
              className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
            >
              <h4 className="font-medium text-gray-900">熱門小說 2</h4>
              <p className="text-sm text-gray-600 mt-1">引人入勝的劇情...</p>
            </Link>
          </div>
        </div>

        {/* 幫助資訊 */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            如果您認為這是一個錯誤，請聯繫我們的客服團隊
          </p>
        </div>
      </div>
    </div>
  )
}
