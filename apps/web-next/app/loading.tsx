/**
 * 首頁載入骨架屏
 */

export default function HomeLoading() {
  return (
    <div className="space-y-8">
      {/* Hero Section 骨架 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8">
        <div className="h-10 bg-white/20 rounded animate-pulse mb-4 w-2/3" />
        <div className="h-6 bg-white/20 rounded animate-pulse mb-6 w-3/4" />
        <div className="flex gap-4">
          <div className="h-12 bg-white/20 rounded-lg animate-pulse w-32" />
          <div className="h-12 bg-white/20 rounded-lg animate-pulse w-32" />
        </div>
      </section>

      {/* Latest Novels Section 骨架 */}
      <section>
        <div className="flex justify-between items-center mb-6">
          <div className="h-8 bg-gray-200 rounded animate-pulse w-32" />
          <div className="h-6 bg-gray-200 rounded animate-pulse w-20" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm border p-4">
              <div className="flex gap-4">
                <div className="w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="h-5 bg-gray-200 rounded animate-pulse mb-2" />
                  <div className="h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3" />
                  <div className="flex items-center gap-2">
                    <div className="h-6 bg-gray-200 rounded animate-pulse w-16" />
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-12" />
                  </div>
                </div>
              </div>
              <div className="mt-3 space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Features Section 骨架 */}
      <section className="grid md:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="h-6 bg-gray-200 rounded animate-pulse mb-3 w-24" />
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3" />
            </div>
          </div>
        ))}
      </section>
    </div>
  )
}
