'use client'

import { useState } from 'react'
import { novelsAPI, chaptersAPI, healthAPI, generateSlug, extractIdFromSlug } from '@novelwebsite/api'

interface TestResult {
  name: string
  success: boolean
  data?: any
  error?: any
  duration?: number
}

export default function APITestPage() {
  const [results, setResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addResult = (name: string, success: boolean, data?: any, error?: any, duration?: number) => {
    setResults(prev => [...prev, { name, success, data, error, duration }])
  }

  const runTests = async () => {
    setIsRunning(true)
    setResults([])

    const tests = [
      {
        name: '健康檢查',
        test: async () => {
          const start = Date.now()
          const result = await healthAPI.check()
          const duration = Date.now() - start
          return { result, duration }
        }
      },
      {
        name: '小說列表 API',
        test: async () => {
          const start = Date.now()
          const result = await novelsAPI.getList({ limit: 5 })
          const duration = Date.now() - start
          return { result, duration }
        }
      },
      {
        name: 'Slug 生成測試',
        test: async () => {
          const start = Date.now()
          const testCases = [
            { title: '瘟仙', id: 1, expected: '瘟仙-1' },
            { title: '修真：從撿垃圾開始！', id: 123, expected: '修真從撿垃圾開始-123' },
            { title: '我的 修仙 人生...', id: 456, expected: '我的-修仙-人生-456' }
          ]
          
          const results = testCases.map(({ title, id, expected }) => {
            const generated = generateSlug(title, id)
            const extracted = extractIdFromSlug(generated)
            return {
              title,
              id,
              generated,
              expected,
              extractedId: extracted,
              slugMatch: generated === expected,
              idMatch: extracted === id
            }
          })
          
          const duration = Date.now() - start
          return { result: results, duration }
        }
      },
      {
        name: '小說詳情 API (如果有數據)',
        test: async () => {
          const start = Date.now()
          try {
            // 先獲取列表，然後測試第一個小說的詳情
            const listResult = await novelsAPI.getList({ limit: 1 })
            if (listResult.results.length > 0) {
              const novel = listResult.results[0]
              const detailResult = await novelsAPI.getDetail(Number(novel.id))
              const duration = Date.now() - start
              return { result: detailResult, duration }
            } else {
              const duration = Date.now() - start
              return { result: { message: '沒有小說數據可測試' }, duration }
            }
          } catch (error) {
            const duration = Date.now() - start
            throw { error, duration }
          }
        }
      },
      {
        name: 'Slug 查詢測試 (如果有數據)',
        test: async () => {
          const start = Date.now()
          try {
            const listResult = await novelsAPI.getList({ limit: 1 })
            if (listResult.results.length > 0) {
              const novel = listResult.results[0]
              const slug = generateSlug(novel.title, Number(novel.id))
              const slugResult = await novelsAPI.getDetailBySlug(slug)
              const duration = Date.now() - start
              return { result: { slug, novel: slugResult }, duration }
            } else {
              const duration = Date.now() - start
              return { result: { message: '沒有小說數據可測試' }, duration }
            }
          } catch (error) {
            const duration = Date.now() - start
            throw { error, duration }
          }
        }
      }
    ]

    for (const { name, test } of tests) {
      try {
        const { result, duration } = await test()
        addResult(name, true, result, null, duration)
      } catch (error: any) {
        addResult(name, false, null, error, error.duration)
      }
    }

    setIsRunning(false)
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          API 客戶端測試 v2
        </h1>
        <p className="text-gray-600 mb-6">
          測試新的 @novelwebsite/api 套件功能，包含重試機制、Slug 處理和導航功能
        </p>
        
        <button
          onClick={runTests}
          disabled={isRunning}
          className={`px-6 py-3 rounded-lg font-semibold ${
            isRunning
              ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isRunning ? '測試進行中...' : '開始測試'}
        </button>
      </div>

      {results.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-gray-900">測試結果</h2>
          
          {results.map((result, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                result.success
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold">
                  {result.success ? '✅' : '❌'} {result.name}
                </h3>
                {result.duration && (
                  <span className="text-sm text-gray-500">
                    {result.duration}ms
                  </span>
                )}
              </div>
              
              {result.success && result.data && (
                <div className="mt-2">
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-64">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              )}
              
              {!result.success && result.error && (
                <div className="mt-2">
                  <div className="text-red-700 font-medium">錯誤訊息:</div>
                  <pre className="bg-red-100 p-3 rounded text-sm overflow-auto max-h-64 text-red-800">
                    {JSON.stringify(result.error, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">測試說明</h3>
        <ul className="text-blue-800 text-sm space-y-1">
          <li>• 確保 Django backend 在 http://localhost:8000 運行</li>
          <li>• 確保有測試數據 (至少 1-2 本小說)</li>
          <li>• 測試包含 API 連通性、Slug 處理、錯誤處理等功能</li>
          <li>• 查看瀏覽器開發者工具的 Network 標籤以檢查實際請求</li>
        </ul>
      </div>
    </div>
  )
}
