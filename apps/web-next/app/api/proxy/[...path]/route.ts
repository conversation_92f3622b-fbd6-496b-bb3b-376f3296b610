import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

// API 路徑白名單 - 只允許代理這些路徑
const ALLOWED_API_PATHS = [
  // 認證相關
  "auth/login",
  "auth/logout",
  "auth/register",
  "auth/profile",
  "auth/refresh",

  // 小說相關
  "novels",
  "novels/\\d+", // 小說詳情 (支援正則)
  "novels/\\d+/chapters", // 章節列表
  "novels/\\d+/chapters/\\d+", // 章節內容

  // 用戶相關
  "users/profile",
  "users/bookmarks",
  "users/reading-history",

  // 搜索相關
  "search",
  "search/novels",
  "search/authors",

  // 系統相關
  "health",
  "version",
  "status",
];

// 檢查路徑是否在白名單中
function isPathAllowed(path: string): boolean {
  // 移除開頭的斜線和 api/v1 前綴
  const cleanPath = path.replace(/^\/?(api\/v1\/)?/, "");

  return ALLOWED_API_PATHS.some((allowedPath) => {
    // 支援正則表達式匹配
    const regex = new RegExp(`^${allowedPath}(/.*)?$`);
    return regex.test(cleanPath);
  });
}

// 記錄可疑請求
function logSuspiciousRequest(path: string, ip: string, userAgent: string) {
  console.warn("🚨 Blocked suspicious API proxy request:", {
    path,
    ip,
    userAgent,
    timestamp: new Date().toISOString(),
  });
}

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } },
) {
  return handleRequest(request, params, "GET");
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } },
) {
  return handleRequest(request, params, "POST");
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } },
) {
  return handleRequest(request, params, "PUT");
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } },
) {
  return handleRequest(request, params, "DELETE");
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { path: string[] } },
) {
  return handleRequest(request, params, "PATCH");
}

async function handleRequest(
  request: NextRequest,
  params: { path: string[] },
  method: string,
) {
  try {
    const { path } = params;
    const apiPath = path.join("/");

    // 安全檢查：驗證路徑是否在白名單中
    if (!isPathAllowed(apiPath)) {
      const ip =
        request.headers.get("x-forwarded-for") ||
        request.headers.get("x-real-ip") ||
        "unknown";
      const userAgent = request.headers.get("user-agent") || "unknown";

      logSuspiciousRequest(apiPath, ip, userAgent);

      return NextResponse.json(
        {
          error: "Forbidden",
          message: "API path not allowed",
          code: "PATH_NOT_ALLOWED",
        },
        { status: 403 },
      );
    }

    // 構建完整的 API URL
    const url = new URL(`/api/v1/${apiPath}`, API_BASE_URL);

    // 複製查詢參數
    const searchParams = request.nextUrl.searchParams;
    searchParams.forEach((value, key) => {
      url.searchParams.append(key, value);
    });

    // 準備請求標頭
    const headers: HeadersInit = {
      "Content-Type": "application/json",
      Accept: "application/json",
    };

    // 複製相關的請求標頭
    const relevantHeaders = [
      "authorization",
      "x-api-key",
      "user-agent",
      "accept-language",
    ];

    relevantHeaders.forEach((headerName) => {
      const headerValue = request.headers.get(headerName);
      if (headerValue) {
        headers[headerName] = headerValue;
      }
    });

    // 準備請求體
    let body: string | undefined;
    if (["POST", "PUT", "PATCH"].includes(method)) {
      try {
        const requestBody = await request.text();
        if (requestBody) {
          body = requestBody;
        }
      } catch (error) {
        console.error("Error reading request body:", error);
      }
    }

    // 發送請求到 Django 後端
    const response = await fetch(url.toString(), {
      method,
      headers,
      body,
      // 設置超時時間
      signal: AbortSignal.timeout(30000), // 30 秒超時
    });

    // 獲取響應數據
    const responseText = await response.text();
    let responseData: unknown;

    try {
      responseData = JSON.parse(responseText);
    } catch {
      // 如果不是 JSON，直接返回文本
      responseData = responseText;
    }

    // 準備響應標頭
    const responseHeaders: HeadersInit = {
      "Content-Type":
        response.headers.get("content-type") || "application/json",
    };

    // 複製 CORS 相關標頭
    const corsHeaders = [
      "access-control-allow-origin",
      "access-control-allow-methods",
      "access-control-allow-headers",
      "access-control-allow-credentials",
    ];

    corsHeaders.forEach((headerName) => {
      const headerValue = response.headers.get(headerName);
      if (headerValue) {
        responseHeaders[headerName] = headerValue;
      }
    });

    // 返回響應
    return new NextResponse(
      typeof responseData === "string"
        ? responseData
        : JSON.stringify(responseData),
      {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      },
    );
  } catch (error) {
    console.error("API Proxy Error:", error);

    // 處理不同類型的錯誤
    if (error instanceof TypeError && error.message.includes("Network error")) {
      return NextResponse.json(
        {
          error: "Backend service unavailable",
          message: "Unable to connect to Django backend",
          details:
            process.env.NODE_ENV === "development" ? error.message : undefined,
        },
        { status: 503 },
      );
    }

    if (error instanceof DOMException && error.name === "AbortError") {
      return NextResponse.json(
        {
          error: "Request timeout",
          message: "Backend request timed out",
        },
        { status: 504 },
      );
    }

    return NextResponse.json(
      {
        error: "Internal server error",
        message: "An unexpected error occurred",
        details: process.env.NODE_ENV === "development" ? error : undefined,
      },
      { status: 500 },
    );
  }
}

// 處理 OPTIONS 請求 (CORS 預檢)
export async function OPTIONS(_request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization, X-API-Key",
      "Access-Control-Max-Age": "86400",
    },
  });
}
