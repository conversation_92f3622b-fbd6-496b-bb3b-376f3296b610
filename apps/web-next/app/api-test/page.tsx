"use client";

import { useState } from "react";
import Link from "next/link";
import { APIClient, novelsAPI } from "@/lib/api";

export default function APITestPage() {
  const [testResults, setTestResults] = useState<
    Array<{
      test: string;
      success: boolean;
      data?: unknown;
      error?: unknown;
      timestamp: string;
    }>
  >([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (
    test: string,
    success: boolean,
    data?: unknown,
    error?: unknown,
  ) => {
    setTestResults((prev) => [
      ...prev,
      {
        test,
        success,
        data,
        error,
        timestamp: new Date().toLocaleTimeString(),
      },
    ]);
  };

  const testAPIConnection = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      // 測試 1: 基本連接測試
      try {
        const response = await APIClient.get("/health");
        addResult("健康檢查", true, response.data);
      } catch (error) {
        addResult("健康檢查", false, null, error);
      }

      // 測試 2: 小說列表 API
      try {
        const response = await novelsAPI.getList({ page: 1, limit: 5 });
        addResult("小說列表 API", true, response.data);
      } catch (error) {
        addResult("小說列表 API", false, null, error);
      }

      // 測試 3: 小說詳情 API
      try {
        const response = await novelsAPI.getDetail(1);
        addResult("小說詳情 API", true, response.data);
      } catch (error) {
        addResult("小說詳情 API", false, null, error);
      }

      // 測試 4: 章節列表 API
      try {
        const response = await novelsAPI.getChapters(1, { page: 1, limit: 5 });
        addResult("章節列表 API", true, response.data);
      } catch (error) {
        addResult("章節列表 API", false, null, error);
      }

      // 測試 5: 直接代理測試
      try {
        const response = await fetch("/api/proxy/novels");
        const data = await response.json();
        addResult("API 代理測試", response.ok, data);
      } catch (error) {
        addResult("API 代理測試", false, null, error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">API 連接測試</h1>
        <Link
          href="/"
          className="text-blue-600 hover:text-blue-800 transition-colors"
        >
          ← 返回首頁
        </Link>
      </div>

      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <h2 className="text-xl font-semibold mb-3 text-blue-900">
          🔧 API 代理測試工具
        </h2>
        <p className="text-blue-800 mb-4">
          這個頁面用於測試 Next.js 15 App Router 與 Django 後端的 API 連接。
          點擊下方按鈕開始測試各個 API 端點。
        </p>
        <div className="text-sm text-blue-700 space-y-1">
          <p>
            • 測試 API 代理路由:{" "}
            <code className="bg-blue-100 px-1 rounded">
              /api/proxy/[...path]
            </code>
          </p>
          <p>
            • 測試 Django 後端連接:{" "}
            <code className="bg-blue-100 px-1 rounded">
              http://localhost:8000
            </code>
          </p>
          <p>• 測試 API 客戶端功能</p>
          <p>• 驗證錯誤處理機制</p>
        </div>
      </div>

      <div className="flex gap-4">
        <button
          onClick={testAPIConnection}
          disabled={isLoading}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isLoading ? "測試中..." : "開始 API 測試"}
        </button>

        <button
          onClick={clearResults}
          disabled={isLoading}
          className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors"
        >
          清除結果
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-xl font-semibold mb-4 text-gray-900">測試結果</h3>
          <div className="space-y-4">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  result.success
                    ? "bg-green-50 border-green-200"
                    : "bg-red-50 border-red-200"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4
                    className={`font-semibold ${
                      result.success ? "text-green-900" : "text-red-900"
                    }`}
                  >
                    {result.success ? "✅" : "❌"} {result.test}
                  </h4>
                  <span className="text-sm text-gray-500">
                    {result.timestamp}
                  </span>
                </div>

                {result.success && result.data !== undefined && (
                  <div className="mt-2">
                    <p className="text-sm text-green-800 mb-2">響應數據:</p>
                    <pre className="bg-green-100 p-2 rounded text-xs overflow-x-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}

                {!result.success && result.error !== undefined && (
                  <div className="mt-2">
                    <p className="text-sm text-red-800 mb-2">錯誤信息:</p>
                    <pre className="bg-red-100 p-2 rounded text-xs overflow-x-auto">
                      {JSON.stringify(result.error, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="bg-gray-50 p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-3 text-gray-900">
          API 端點說明
        </h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-semibold mb-2 text-gray-700">
              Django 後端 API
            </h4>
            <ul className="space-y-1 text-gray-600">
              <li>
                • <code>/api/v1/health</code> - 健康檢查
              </li>
              <li>
                • <code>/api/v1/novels</code> - 小說列表
              </li>
              <li>
                • <code>/api/v1/novels/:id</code> - 小說詳情
              </li>
              <li>
                • <code>/api/v1/novels/:id/chapters</code> - 章節列表
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2 text-gray-700">
              Next.js API 代理
            </h4>
            <ul className="space-y-1 text-gray-600">
              <li>
                • <code>/api/proxy/health</code> - 代理健康檢查
              </li>
              <li>
                • <code>/api/proxy/novels</code> - 代理小說列表
              </li>
              <li>
                • <code>/api/proxy/novels/:id</code> - 代理小說詳情
              </li>
              <li>• 支援所有 HTTP 方法 (GET, POST, PUT, DELETE)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
