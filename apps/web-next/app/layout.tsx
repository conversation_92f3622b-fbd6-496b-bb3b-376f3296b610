import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "瘟仙小說 - Next.js 15 App Router",
    template: "%s | 瘟仙小說",
  },
  description: "線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",
  keywords: ["小說", "線上閱讀", "Next.js", "App Router"],
  authors: [{ name: "NovelWebsite Team" }],
  creator: "NovelWebsite",
  publisher: "NovelWebsite",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001",
  ),
  openGraph: {
    type: "website",
    locale: "zh_TW",
    url: "/",
    title: "瘟仙小說 - Next.js 15 App Router",
    description: "線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",
    siteName: "瘟仙小說",
  },
  twitter: {
    card: "summary_large_image",
    title: "瘟仙小說 - Next.js 15 App Router",
    description: "線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-TW" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50`}>
        <div className="min-h-screen bg-gray-50">
          <header className="bg-white shadow-sm">
            <div className="max-w-6xl mx-auto px-4 py-4">
              <h1 className="text-2xl font-bold text-gray-900">
                瘟仙小說 (Next.js 15 App Router)
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                與 CRA 應用並行運行 - 端口: 3001
              </p>
            </div>
          </header>
          <main className="max-w-6xl mx-auto px-4 py-6">{children}</main>
          <footer className="bg-white border-t mt-12">
            <div className="max-w-6xl mx-auto px-4 py-6">
              <p className="text-center text-gray-600 text-sm">
                © 2025 瘟仙小說 - Next.js 15 App Router 版本
              </p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}
