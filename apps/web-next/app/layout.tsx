import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { ClientContexts, Header, Layout } from '@novelwebsite/ui'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: '瘟仙小說 - Next.js 15 App Router',
    template: '%s | 瘟仙小說'
  },
  description: '線上小說閱讀平台 - 使用 Next.js 15 App Router 架構',
  keywords: ['小說', '線上閱讀', 'Next.js', 'App Router'],
  authors: [{ name: 'NovelWebsite Team' }],
  creator: 'NovelWebsite',
  publisher: 'NovelWebsite',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'),
  openGraph: {
    type: 'website',
    locale: 'zh_TW',
    url: '/',
    title: '瘟仙小說 - Next.js 15 App Router',
    description: '線上小說閱讀平台 - 使用 Next.js 15 App Router 架構',
    siteName: '瘟仙小說',
  },
  twitter: {
    card: 'summary_large_image',
    title: '瘟仙小說 - Next.js 15 App Router',
    description: '線上小說閱讀平台 - 使用 Next.js 15 App Router 架構',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // 導航配置
  const navigation = [
    { label: '首頁', href: '/', active: false },
    { label: '小說分類', href: '/categories', active: false },
    { label: '排行榜', href: '/rankings', active: false },
    { label: 'API 測試', href: '/api-test-v2', active: false },
  ]

  // 頭部操作按鈕
  const headerActions = (
    <div className="flex items-center space-x-2">
      <button className="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700">
        登入
      </button>
      <button className="px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
        註冊
      </button>
    </div>
  )

  return (
    <html lang="zh-TW" className="h-full">
      <body className={`${inter.className} h-full`}>
        <ClientContexts>
          <Layout
            header={
              <Header
                title="小說閱讀平台 (Next.js 15)"
                navigation={navigation}
                actions={headerActions}
                variant="default"
              />
            }
            footer={
              <div className="max-w-6xl mx-auto px-4 py-6">
                <p className="text-center text-gray-600 text-sm">
                  © 2025 小說閱讀平台 - Next.js 15 App Router 版本
                </p>
              </div>
            }
            maxWidth="xl"
          >
            {children}
          </Layout>
        </ClientContexts>
      </body>
    </html>
  )
}
