import Link from 'next/link'
import { Metadata } from 'next'
import { novelsAPI, generateSlug, type Novel } from '@novelwebsite/api'
import { NovelCard } from '@novelwebsite/ui'

export const metadata: Metadata = {
  title: '小說閱讀平台 | Next.js 15 App Router',
  description: '現代化的小說閱讀平台，提供豐富的小說資源和優質的閱讀體驗',
  openGraph: {
    title: '小說閱讀平台',
    description: '現代化的小說閱讀平台，提供豐富的小說資源和優質的閱讀體驗',
    type: 'website',
  },
}

// ISR 配置：60 秒重新驗證
export const revalidate = 60

// 獲取小說列表數據
async function getNovelsData(): Promise<Novel[]> {
  try {
    const response = await novelsAPI.getList({ limit: 12 })
    return response.results
  } catch (error) {
    console.error('Error fetching novels:', error)
    return []
  }
}



export default async function HomePage() {
  const novels = await getNovelsData();

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8">
        <h1 className="text-4xl font-bold mb-4">
          小說閱讀平台
        </h1>
        <p className="text-xl mb-6">
          現代化的小說閱讀體驗，支援 SSR/SSG，提供優質的閱讀環境
        </p>
        <div className="flex gap-4">
          <Link
            href="#latest-novels"
            className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            開始閱讀
          </Link>
          <Link
            href="/about"
            className="border border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
          >
            了解更多
          </Link>
        </div>
      </section>

      {/* Latest Novels Section */}
      <section id="latest-novels">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            最新小說
          </h2>
          <Link
            href="/novels"
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            查看全部 →
          </Link>
        </div>

        {novels.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {novels.map((novel) => {
              const slug = generateSlug(novel.title, Number(novel.id))
              return (
                <NovelCard
                  key={novel.id}
                  novel={novel}
                  href={`/novels/${slug}`}
                />
              )
            })}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              暫無小說內容
            </h3>
            <p className="text-gray-600">
              請確保 Django backend 正在運行並包含測試數據
            </p>
          </div>
        )}
      </section>

      {/* Features Section */}
      <section className="grid md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-xl font-semibold mb-3 text-gray-900">
            🚀 App Router
          </h3>
          <p className="text-gray-600">
            使用 Next.js 15 最新的 App Router 架構，提供更好的開發體驗和性能
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-xl font-semibold mb-3 text-gray-900">
            ⚡ 伺服器端渲染
          </h3>
          <p className="text-gray-600">
            支援 SSR 和 SSG，提升 SEO 效果和頁面載入速度
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-xl font-semibold mb-3 text-gray-900">
            🔄 ISR 快取
          </h3>
          <p className="text-gray-600">
            增量靜態重新生成，確保內容即時更新且性能優異
          </p>
        </div>
      </section>

      {/* Status Section */}
      <section className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900">
          系統狀態
        </h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-700">應用資訊</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Next.js 版本: 15.1.3</li>
              <li>• React 版本: 18.2.0</li>
              <li>• TypeScript 支援: ✅</li>
              <li>• Tailwind CSS: ✅</li>
              <li>• App Router: ✅</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-700">Monorepo 整合</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• pnpm workspace: ✅</li>
              <li>• Turborepo 支援: ✅</li>
              <li>• 共享套件: 準備中</li>
              <li>• CI/CD 整合: 準備中</li>
              <li>• API 代理: 準備中</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Development Info */}
      <section className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <h2 className="text-xl font-semibold mb-3 text-blue-900">
          開發資訊
        </h2>
        <div className="text-sm text-blue-800 space-y-1">
          <p>• 當前端口: 3001 (Next.js 15)</p>
          <p>• CRA 應用端口: 3000</p>
          <p>• Django 後端端口: 8000</p>
          <p>• 可使用 <code className="bg-blue-100 px-1 rounded">turbo dev</code> 同時啟動所有服務</p>
        </div>
      </section>
    </div>
  )
}
