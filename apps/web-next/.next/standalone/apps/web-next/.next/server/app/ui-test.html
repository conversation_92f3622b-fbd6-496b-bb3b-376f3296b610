<!DOCTYPE html><html lang="zh-TW" class="h-full"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/0536b5c2391eaaee.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-d95d3426f4d7cbd8.js"/><script src="/_next/static/chunks/vendor-0f2e12d39b2860a7.js" async=""></script><script src="/_next/static/chunks/common-76fccfb14bfa9d13.js" async=""></script><script src="/_next/static/chunks/main-app-44be1214710eafaf.js" async=""></script><script src="/_next/static/chunks/app/page-d135f1dee0e209f4.js" async=""></script><script src="/_next/static/chunks/app/ui-test/page-0f4d042e3e2a74d3.js" async=""></script><meta name="next-size-adjust" content=""/><title>瘟仙小說 - Next.js 15 App Router</title><meta name="description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><meta name="author" content="NovelWebsite Team"/><meta name="keywords" content="小說,線上閱讀,Next.js,App Router"/><meta name="creator" content="NovelWebsite"/><meta name="publisher" content="NovelWebsite"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="瘟仙小說 - Next.js 15 App Router"/><meta property="og:description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><meta property="og:url" content="http://localhost:3001"/><meta property="og:site_name" content="瘟仙小說"/><meta property="og:locale" content="zh_TW"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="瘟仙小說 - Next.js 15 App Router"/><meta name="twitter:description" content="線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c h-full"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><header class="sticky top-0 z-50 bg-white shadow-sm border-b"><header class="sticky top-0 z-50 bg-white border-b border-gray-200"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between items-center h-16"><div class="flex items-center"><h1 class="text-xl font-bold text-gray-900">小說閱讀平台 (Next.js 15)</h1></div><nav class="hidden md:flex space-x-8"><a href="/" class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100">首頁</a><a href="/categories" class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100">小說分類</a><a href="/rankings" class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100">排行榜</a><a href="/api-test-v2" class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-100">API 測試</a></nav><div class="flex items-center space-x-4"><div class="hidden md:flex items-center space-x-2"><div class="flex items-center space-x-2"><button class="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700">登入</button><button class="px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">註冊</button></div></div><button type="button" class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" aria-expanded="false" aria-label="開啟主選單"><svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></div></div></header></header><div class="flex flex-1"><main class="flex-1"><div class="w-full mx-auto p-4 sm:p-6 lg:p-8 max-w-6xl"><!--$--><div class="space-y-12"><div class="text-center"><h1 class="text-3xl font-bold text-gray-900 mb-4">UI 組件測試頁面</h1><p class="text-gray-600">驗證 @novelwebsite/ui 套件中所有組件的功能和樣式</p></div><section class="bg-white rounded-lg p-6 shadow-sm border"><h2 class="text-2xl font-semibold text-gray-900 mb-4">LoadingSpinner 組件</h2><div class="grid grid-cols-1 md:grid-cols-3 gap-6"><div class="text-center"><h3 class="text-lg font-medium mb-3">小尺寸</h3><div class="flex items-center justify-center" role="status" aria-label="載入中..." aria-live="polite"><div class="animate-spin rounded-full border-b-2 border-t-2 h-4 w-4 border-blue-500" aria-hidden="true"></div><div class="ml-3 text-sm text-gray-600">載入中...</div></div></div><div class="text-center"><h3 class="text-lg font-medium mb-3">中等尺寸</h3><div class="flex items-center justify-center" role="status" aria-label="處理中..." aria-live="polite"><div class="animate-spin rounded-full border-b-2 border-t-2 h-8 w-8 border-gray-500" aria-hidden="true"></div><div class="ml-3 text-base text-gray-500">處理中...</div></div></div><div class="text-center"><h3 class="text-lg font-medium mb-3">大尺寸</h3><div class="flex items-center justify-center bg-blue-600 p-4 rounded" role="status" aria-label="請稍候..." aria-live="polite"><div class="animate-spin rounded-full border-b-2 border-t-2 h-12 w-12 border-white" aria-hidden="true"></div><div class="ml-3 text-lg text-white">請稍候...</div></div></div></div><div class="mt-6"><button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">開始載入</button></div></section><section class="bg-white rounded-lg p-6 shadow-sm border"><h2 class="text-2xl font-semibold text-gray-900 mb-4">NovelCard 組件</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><h3 class="text-lg font-medium mb-3">連結模式</h3><a href="/novels/test-novel-1" class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow block"><div class="p-4"><div class="flex gap-4"><img src="/default-cover.jpg" alt="測試小說標題" class="w-16 h-20 object-cover rounded flex-shrink-0" loading="lazy"/><div class="flex-1 min-w-0"><h3 class="font-semibold text-gray-900 truncate mb-1">測試小說標題</h3><p class="text-sm text-gray-600 mb-2">作者：<!-- -->測試作者</p><div class="flex items-center gap-2 text-xs text-gray-500"><span class="px-2 py-1 rounded bg-blue-100 text-blue-800">連載中</span><span>👁️ <!-- -->12,345</span><span>❤️ <!-- -->678</span></div><div class="mt-1"><span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">玄幻</span></div></div></div><p class="text-sm text-gray-600 mt-3 line-clamp-2">這是一個測試小說的描述，用來驗證 NovelCard 組件的顯示效果。</p></div></a></div><div><h3 class="text-lg font-medium mb-3">點擊模式</h3><button type="button" class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow block w-full text-left"><div class="p-4"><div class="flex gap-4"><img src="/default-cover.jpg" alt="測試小說標題" class="w-16 h-20 object-cover rounded flex-shrink-0" loading="lazy"/><div class="flex-1 min-w-0"><h3 class="font-semibold text-gray-900 truncate mb-1">測試小說標題</h3><p class="text-sm text-gray-600 mb-2">作者：<!-- -->測試作者</p><div class="flex items-center gap-2 text-xs text-gray-500"><span class="px-2 py-1 rounded bg-green-100 text-green-800">已完結</span><span>👁️ <!-- -->12,345</span><span>❤️ <!-- -->678</span></div><div class="mt-1"><span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">玄幻</span></div></div></div><p class="text-sm text-gray-600 mt-3 line-clamp-2">這是一個測試小說的描述，用來驗證 NovelCard 組件的顯示效果。</p></div></button></div></div></section><section class="bg-white rounded-lg p-6 shadow-sm border"><h2 class="text-2xl font-semibold text-gray-900 mb-4">ChapterCard 組件</h2><div class="space-y-4"><div><h3 class="text-lg font-medium mb-3">普通章節</h3><a href="/novels/test-novel/chapters/1" class="block border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="p-3"><div class="flex justify-between items-start"><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-medium text-gray-900">第 <!-- -->1<!-- --> 章</span></div><h4 class="text-gray-900 font-medium truncate mb-2">第一章：開始的地方</h4><div class="flex items-center gap-4 text-sm text-gray-500"><span>👁️ <!-- -->5,432</span><span>2025/01/01</span></div></div><div class="flex-shrink-0 ml-3"><svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></div></div></div></a></div><div><h3 class="text-lg font-medium mb-3">VIP 章節</h3><button type="button" class="block border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors border-yellow-200 bg-yellow-50/30 w-full text-left"><div class="p-3"><div class="flex justify-between items-start"><div class="flex-1 min-w-0"><div class="flex items-center gap-2 mb-1"><span class="font-medium text-gray-900">第 <!-- -->2<!-- --> 章</span><span class="inline-flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">👑 VIP</span></div><h4 class="text-gray-900 font-medium truncate mb-2">第二章：VIP 章節</h4><div class="flex items-center gap-4 text-sm text-gray-500"><span>👁️ <!-- -->3,210</span><span>2025/01/02</span></div></div><div class="flex-shrink-0 ml-3"><svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></div></div></div></button></div></div></section><section class="bg-white rounded-lg p-6 shadow-sm border"><h2 class="text-2xl font-semibold text-gray-900 mb-4">Context API 測試</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><h3 class="text-lg font-medium mb-3">認證狀態</h3><div class="space-y-3"><p><strong>登入狀態：</strong><span class="text-red-600">未登入</span></p><div class="flex gap-2"><button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50">測試登入</button></div></div></div><div><h3 class="text-lg font-medium mb-3">設定管理</h3><div class="space-y-3"><p><strong>當前主題：</strong> <!-- -->light</p><p><strong>字體大小：</strong> <!-- -->md</p><div class="space-y-2"><div><label class="block text-sm font-medium mb-1">主題：</label><select class="border border-gray-300 rounded px-3 py-1"><option value="light" selected="">淺色</option><option value="dark">深色</option><option value="sepia">護眼</option><option value="night">夜間</option></select></div><div><label class="block text-sm font-medium mb-1">字體大小：</label><select class="border border-gray-300 rounded px-3 py-1"><option value="sm">小</option><option value="md" selected="">中</option><option value="lg">大</option><option value="xl">特大</option></select></div></div></div></div></div></section><section class="bg-green-50 rounded-lg p-6 border border-green-200"><h2 class="text-2xl font-semibold text-green-900 mb-4">✅ 測試狀態</h2><ul class="space-y-2 text-green-800"><li>✅ LoadingSpinner 組件正常渲染</li><li>✅ NovelCard 組件正常渲染</li><li>✅ ChapterCard 組件正常渲染</li><li>✅ AuthContext 正常工作</li><li>✅ SettingsContext 正常工作</li><li>✅ SSR/CSR Hydration 無錯誤</li></ul></section></div><!--$--><!--/$--><!--/$--></div></main></div><footer class="bg-white border-t"><div class="max-w-6xl mx-auto px-4 py-6"><p class="text-center text-gray-600 text-sm">© 2025 小說閱讀平台 - Next.js 15 App Router 版本</p></div></footer></div><script src="/_next/static/chunks/webpack-d95d3426f4d7cbd8.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[9054,[\"974\",\"static/chunks/app/page-d135f1dee0e209f4.js\"],\"ClientContexts\"]\n3:I[4441,[\"974\",\"static/chunks/app/page-d135f1dee0e209f4.js\"],\"default\"]\n4:I[4602,[],\"\"]\n5:I[4216,[],\"\"]\n6:I[9051,[],\"ClientPageRoot\"]\n7:I[5942,[\"72\",\"static/chunks/app/ui-test/page-0f4d042e3e2a74d3.js\"],\"default\"]\na:I[9938,[],\"OutletBoundary\"]\nd:I[2658,[],\"AsyncMetadataOutlet\"]\nf:I[9938,[],\"ViewportBoundary\"]\n11:I[9938,[],\"MetadataBoundary\"]\n13:I[7229,[],\"\"]\n:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/0536b5c2391eaaee.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"VKjA-EukV3HyQ00Ppa_Zh\",\"p\":\"\",\"c\":[\"\",\"ui-test\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"ui-test\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/0536b5c2391eaaee.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-TW\",\"className\":\"h-full\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c h-full\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[[\"$\",\"header\",null,{\"className\":\"sticky top-0 z-50 bg-white shadow-sm border-b\",\"children\":[\"$\",\"$L3\",null,{\"title\":\"小說閱讀平台 (Next.js 15)\",\"navigation\":[{\"label\":\"首頁\",\"href\":\"/\",\"active\":false},{\"label\":\"小說分類\",\"href\":\"/categories\",\"active\":false},{\"label\":\"排行榜\",\"href\":\"/rankings\",\"active\":false},{\"label\":\"API 測試\",\"href\":\"/api-test-v2\",\"active\":false}],\"actions\":[\"$\",\"div\",null,{\"className\":\"flex items-center space-x-2\",\"children\":[[\"$\",\"button\",null,{\"className\":\"px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\"children\":\"登入\"}],[\"$\",\"button\",null,{\"className\":\"px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\"children\":\"註冊\"}]]}],\"variant\":\"default\"}]}],[\"$\",\"div\",null,{\"className\":\"flex flex-1\",\"children\":[\"$undefined\",[\"$\",\"main\",null,{\"className\":\"flex-1\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-full mx-auto p-4 sm:p-6 lg:p-8 max-w-6xl\",\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],[\"$\",\"footer\",null,{\"className\":\"bg-white border-t\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-6xl mx-auto px-4 py-6\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-center text-gray-600 text-sm\",\"children\":\"© 2025 小說閱讀平台 - Next.js 15 App Router 版本\"}]}]}]]}]}]}]}]]}],{\"children\":[\"ui-test\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L6\",null,{\"Component\":\"$7\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@8\",\"$@9\"]}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},[[\"$\",\"div\",\"l\",{\"className\":\"space-y-8\",\"children\":[[\"$\",\"section\",null,{\"className\":\"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-10 bg-white/20 rounded animate-pulse mb-4 w-2/3\"}],[\"$\",\"div\",null,{\"className\":\"h-6 bg-white/20 rounded animate-pulse mb-6 w-3/4\"}],[\"$\",\"div\",null,{\"className\":\"flex gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-12 bg-white/20 rounded-lg animate-pulse w-32\"}],[\"$\",\"div\",null,{\"className\":\"h-12 bg-white/20 rounded-lg animate-pulse w-32\"}]]}]]}],[\"$\",\"section\",null,{\"children\":[[\"$\",\"div\",null,{\"className\":\"flex justify-between items-center mb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-8 bg-gray-200 rounded animate-pulse w-32\"}],[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse w-20\"}]]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"bg-white rounded-lg shadow-sm border p-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 min-w-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-5 bg-gray-200 rounded animate-pulse mb-2\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse w-16\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-12\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-3 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"}]]}]]}],[\"$\",\"div\",\"1\",{\"className\":\"bg-white rounded-lg shadow-sm border p-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 min-w-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-5 bg-gray-200 rounded animate-pulse mb-2\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse w-16\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-12\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-3 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"}]]}]]}],[\"$\",\"div\",\"2\",{\"className\":\"bg-white rounded-lg shadow-sm border p-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 min-w-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-5 bg-gray-200 rounded animate-pulse mb-2\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse w-16\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-12\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-3 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"}]]}]]}],[\"$\",\"div\",\"3\",{\"className\":\"bg-white rounded-lg shadow-sm border p-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 min-w-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-5 bg-gray-200 rounded animate-pulse mb-2\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse w-16\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-12\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-3 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"}]]}]]}],[\"$\",\"div\",\"4\",{\"className\":\"bg-white rounded-lg shadow-sm border p-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 min-w-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-5 bg-gray-200 rounded animate-pulse mb-2\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse w-16\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-12\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-3 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"}]]}]]}],[\"$\",\"div\",\"5\",{\"className\":\"bg-white rounded-lg shadow-sm border p-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0\"}],[\"$\",\"div\",null,{\"className\":\"flex-1 min-w-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-5 bg-gray-200 rounded animate-pulse mb-2\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3\"}],[\"$\",\"div\",null,{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse w-16\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-12\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-3 space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-3/4\"}]]}]]}]]}]]}],[\"$\",\"section\",null,{\"className\":\"grid md:grid-cols-3 gap-6\",\"children\":[[\"$\",\"div\",\"0\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse mb-3 w-24\"}],[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-2/3\"}]]}]]}],[\"$\",\"div\",\"1\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse mb-3 w-24\"}],[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-2/3\"}]]}]]}],[\"$\",\"div\",\"2\",{\"className\":\"bg-white p-6 rounded-lg shadow-sm border\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-6 bg-gray-200 rounded animate-pulse mb-3 w-24\"}],[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"h-4 bg-gray-200 rounded animate-pulse w-2/3\"}]]}]]}]]}]]}],[],[]],false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"ZSvbFhJ8mwlu8QRwUt1hov\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],[\"$\",\"$L11\",null,{\"children\":\"$L12\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$13\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"14:\"$Sreact.suspense\"\n15:I[2658,[],\"AsyncMetadata\"]\n8:{}\n9:{}\n12:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$14\",null,{\"fallback\":null,\"children\":[\"$\",\"$L15\",null,{\"promise\":\"$@16\"}]}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"e:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"NovelWebsite Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"小說,線上閱讀,Next.js,App Router\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"NovelWebsite\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"NovelWebsite\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"8\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:title\",\"content\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:url\",\"content\":\"http://localhost:3001\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:site_name\",\"content\":\"瘟仙小說\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:locale\",\"content\":\"zh_TW\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:title\",\"content\":\"瘟仙小說 - Next.js 15 App Router\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:description\",\"content\":\"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構\"}]],\"error\":null,\"digest\":\"$undefined\"}\n16:{\"metadata\":\"$e:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>