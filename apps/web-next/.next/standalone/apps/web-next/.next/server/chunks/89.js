exports.id=89,exports.ids=[89],exports.modules={375:()=>{},1822:()=>{},2309:(e,t,s)=>{let{createProxy:r}=s(9369);e.exports=r("/Users/<USER>/Documents/project/NovelWebsite/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_babel-plugin-macros@3.1.0_react-dom@18_4jqqjup23g4vegjq5p5xeio7v4/node_modules/next/dist/client/app-dir/link.js")},3670:()=>{},3961:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9751,23))},4193:()=>{},4276:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>i});var r=s(1862),a=s(7221),n=s.n(a);s(4193);let i={title:{default:"瘟仙小說 - Next.js 15 App Router",template:"%s | 瘟仙小說"},description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",keywords:["小說","線上閱讀","Next.js","App Router"],authors:[{name:"NovelWebsite Team"}],creator:"NovelWebsite",publisher:"NovelWebsite",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3001"),openGraph:{type:"website",locale:"zh_TW",url:"/",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",siteName:"瘟仙小說"},twitter:{card:"summary_large_image",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function l({children:e}){return(0,r.jsx)("html",{lang:"zh-TW",className:"h-full",children:(0,r.jsx)("body",{className:`${n().className} h-full bg-gray-50`,children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"瘟仙小說 (Next.js 15 App Router)"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"與 CRA 應用並行運行 - 端口: 3001"})]})}),(0,r.jsx)("main",{className:"max-w-6xl mx-auto px-4 py-6",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t mt-12",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:(0,r.jsx)("p",{className:"text-center text-gray-600 text-sm",children:"\xa9 2025 瘟仙小說 - Next.js 15 App Router 版本"})})})]})})})}},4349:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,3859,23)),Promise.resolve().then(s.t.bind(s,7535,23)),Promise.resolve().then(s.t.bind(s,3555,23)),Promise.resolve().then(s.t.bind(s,7426,23)),Promise.resolve().then(s.t.bind(s,5558,23)),Promise.resolve().then(s.t.bind(s,634,23)),Promise.resolve().then(s.t.bind(s,4344,23)),Promise.resolve().then(s.t.bind(s,2194,23))},4457:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2309,23))},5103:()=>{},6448:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1862);function a(){return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("section",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8",children:[(0,r.jsx)("div",{className:"h-10 bg-white/20 rounded animate-pulse mb-4 w-2/3"}),(0,r.jsx)("div",{className:"h-6 bg-white/20 rounded animate-pulse mb-6 w-3/4"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"h-12 bg-white/20 rounded-lg animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-12 bg-white/20 rounded-lg animate-pulse w-32"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-20"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded animate-pulse mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-16"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-12"})]})]})]}),(0,r.jsxs)("div",{className:"mt-3 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"})]})]},t))})]}),(0,r.jsx)("section",{className:"grid md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse mb-3 w-24"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-2/3"})]})]},t))})]})}},6497:(e,t,s)=>{"use strict";s.d(t,{jx:()=>c,z9:()=>n,WN:()=>d});let r={baseURL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api/v1",timeout:3e4,defaultRevalidate:60,retries:3,retryDelay:1e3};class a extends Error{constructor(e,t,s){super(e),this.status=t,this.data=s,this.name="APIClientError"}}function n(e,t){let s=e.replace(/[：！？。，、；""''（）【】《》〈〉]/g,"").replace(/[^\u4e00-\u9fa5\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+|-+$/g,"").trim();return t?`${s}-${t}`:s}function i(e){let t=e.match(/-(\d+)$/);return t?parseInt(t[1],10):null}async function l(e,t=r.retries,s=r.retryDelay){let n=null;for(let r=0;r<=t;r++)try{return await e()}catch(i){if(n=i,r===t||i instanceof a&&i.status>=400&&i.status<500)break;let e=s*Math.pow(2,r);await function(e){return new Promise(t=>setTimeout(t,e))}(e)}throw n||Error("Unknown error in retry mechanism")}async function o(e,t={}){let{revalidate:s=r.defaultRevalidate,tags:n=[],enableRetry:i=!0,...d}=t,c=`${r.baseURL}${e}`,m=async()=>{let e=await fetch(c,{...d,next:{revalidate:s,tags:n},headers:{"Content-Type":"application/json",...d.headers}});if(!e.ok){let t=await e.json().catch(()=>({}));throw new a(`API request failed: ${e.status} ${e.statusText}`,e.status,t)}return await e.json()};try{if(i)return await l(m);return await m()}catch(e){if(e instanceof a)throw e;throw new a(`Network error: ${e instanceof Error?e.message:"Unknown error"}`,0)}}let d={async getList(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{null!=s&&t.append(e,String(s))});let s=t.toString();return o(`/novels/${s?`?${s}`:""}`,{tags:["novels","novels-list"]})},getDetail:async e=>o(`/novels/${e}/`,{tags:["novels",`novel-${e}`]}),async getDetailBySlug(e){let t=i(e);if(t)try{return await this.getDetail(t)}catch(e){if(e instanceof a&&404===e.status)return null;throw e}if(/^\d+$/.test(e))try{return await this.getDetail(parseInt(e,10))}catch(e){if(e instanceof a&&404===e.status)return null;throw e}try{return(await this.getList({search:e.replace(/-/g," ")})).results.find(t=>n(t.title,Number(t.id))===e)||null}catch(e){return console.error("Error searching novel by slug:",e),null}},async getChapters(e,t={}){let s=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&s.append(e,String(t))});let r=s.toString();return o(`/novels/${e}/chapters/${r?`?${r}`:""}`,{tags:["chapters",`novel-${e}-chapters`]})},async getNovelIdBySlug(e){let t=i(e);if(t)try{return await this.getDetail(t),t}catch(e){if(e instanceof a&&404===e.status)return null;throw e}if(/^\d+$/.test(e)){let t=parseInt(e,10);try{return await this.getDetail(t),t}catch(e){if(e instanceof a&&404===e.status)return null;throw e}}try{let t=(await this.getList({search:e.replace(/-/g," "),limit:50})).results.find(t=>n(t.title,Number(t.id))===e);return t?Number(t.id):null}catch(e){return console.error("Error searching novel by slug:",e),null}}},c={getContent:async e=>o(`/chapters/${e}/`,{tags:["chapters",`chapter-${e}`]}),async getChapterNavigation(e){let t=await this.getContent(e),s=await d.getDetail(t.novel),r=(await d.getChapters(t.novel,{limit:1e3,ordering:"chapter_number"})).results,a=r.findIndex(t=>Number(t.id)===e);return{current:t,previous:a>0?r[a-1]:null,next:a<r.length-1?r[a+1]:null,novel:s}}}},7085:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6913,23)),Promise.resolve().then(s.t.bind(s,9481,23)),Promise.resolve().then(s.t.bind(s,9073,23)),Promise.resolve().then(s.t.bind(s,1424,23)),Promise.resolve().then(s.t.bind(s,2416,23)),Promise.resolve().then(s.t.bind(s,7060,23)),Promise.resolve().then(s.t.bind(s,7090,23)),Promise.resolve().then(s.t.bind(s,516,23))}};