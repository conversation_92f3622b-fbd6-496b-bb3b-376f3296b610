(()=>{var e={};e.id=739,e.ids=[739],e.modules={375:()=>{},436:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(9572),a=s(9789);let n={baseURL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api/v1",timeout:3e4,defaultRevalidate:60,retries:3,retryDelay:1e3};class l extends Error{constructor(e,t,s){super(e),this.status=t,this.data=s,this.name="APIClientError"}}function i(e,t){let s=e.replace(/[：！？。，、；""''（）【】《》〈〉]/g,"").replace(/[^\u4e00-\u9fa5\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+|-+$/g,"").trim();return t?`${s}-${t}`:s}function o(e){let t=e.match(/-(\d+)$/);return t?parseInt(t[1],10):null}async function d(e,t=n.retries,s=n.retryDelay){let r=null;for(let a=0;a<=t;a++)try{return await e()}catch(n){if(r=n,a===t||n instanceof l&&n.status>=400&&n.status<500)break;let e=s*Math.pow(2,a);await function(e){return new Promise(t=>setTimeout(t,e))}(e)}throw r||Error("Unknown error in retry mechanism")}async function c(e,t={}){let{revalidate:s=n.defaultRevalidate,tags:r=[],enableRetry:a=!0,...i}=t,o=`${n.baseURL}${e}`,u=async()=>{let e=await fetch(o,{...i,next:{revalidate:s,tags:r},headers:{"Content-Type":"application/json",...i.headers}});if(!e.ok){let t=await e.json().catch(()=>({}));throw new l(`API request failed: ${e.status} ${e.statusText}`,e.status,t)}return await e.json()};try{if(a)return await d(u);return await u()}catch(e){if(e instanceof l)throw e;throw new l(`Network error: ${e instanceof Error?e.message:"Unknown error"}`,0)}}let u={async getList(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{null!=s&&t.append(e,String(s))});let s=t.toString();return c(`/novels/${s?`?${s}`:""}`,{tags:["novels","novels-list"]})},getDetail:async e=>c(`/novels/${e}/`,{tags:["novels",`novel-${e}`]}),async getDetailBySlug(e){let t=o(e);if(t)try{return await this.getDetail(t)}catch(e){if(e instanceof l&&404===e.status)return null;throw e}if(/^\d+$/.test(e))try{return await this.getDetail(parseInt(e,10))}catch(e){if(e instanceof l&&404===e.status)return null;throw e}try{return(await this.getList({search:e.replace(/-/g," ")})).results.find(t=>i(t.title,Number(t.id))===e)||null}catch(e){return console.error("Error searching novel by slug:",e),null}},async getChapters(e,t={}){let s=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&s.append(e,String(t))});let r=s.toString();return c(`/novels/${e}/chapters/${r?`?${r}`:""}`,{tags:["chapters",`novel-${e}-chapters`]})},async getNovelIdBySlug(e){let t=o(e);if(t)try{return await this.getDetail(t),t}catch(e){if(e instanceof l&&404===e.status)return null;throw e}if(/^\d+$/.test(e)){let t=parseInt(e,10);try{return await this.getDetail(t),t}catch(e){if(e instanceof l&&404===e.status)return null;throw e}}try{let t=(await this.getList({search:e.replace(/-/g," "),limit:50})).results.find(t=>i(t.title,Number(t.id))===e);return t?Number(t.id):null}catch(e){return console.error("Error searching novel by slug:",e),null}}},m={check:async()=>c("/health/",{revalidate:0})};function h(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!1),l=(e,s,r,a,n)=>{t(t=>[...t,{name:e,success:s,data:r,error:a,duration:n}])},d=async()=>{for(let{name:e,test:s}of(n(!0),t([]),[{name:"健康檢查",test:async()=>{let e=Date.now();return{result:await m.check(),duration:Date.now()-e}}},{name:"小說列表 API",test:async()=>{let e=Date.now();return{result:await u.getList({limit:5}),duration:Date.now()-e}}},{name:"Slug 生成測試",test:async()=>{let e=Date.now();return{result:[{title:"瘟仙",id:1,expected:"瘟仙-1"},{title:"修真：從撿垃圾開始！",id:123,expected:"修真從撿垃圾開始-123"},{title:"我的 修仙 人生...",id:456,expected:"我的-修仙-人生-456"}].map(({title:e,id:t,expected:s})=>{let r=i(e,t),a=o(r);return{title:e,id:t,generated:r,expected:s,extractedId:a,slugMatch:r===s,idMatch:a===t}}),duration:Date.now()-e}}},{name:"小說詳情 API (如果有數據)",test:async()=>{let e=Date.now();try{let t=await u.getList({limit:1});if(t.results.length>0){let s=t.results[0],r=await u.getDetail(Number(s.id)),a=Date.now()-e;return{result:r,duration:a}}{let t=Date.now()-e;return{result:{message:"沒有小說數據可測試"},duration:t}}}catch(t){throw{error:t,duration:Date.now()-e}}}},{name:"Slug 查詢測試 (如果有數據)",test:async()=>{let e=Date.now();try{let t=await u.getList({limit:1});if(t.results.length>0){let s=t.results[0],r=i(s.title,Number(s.id)),a=await u.getDetailBySlug(r),n=Date.now()-e;return{result:{slug:r,novel:a},duration:n}}{let t=Date.now()-e;return{result:{message:"沒有小說數據可測試"},duration:t}}}catch(t){throw{error:t,duration:Date.now()-e}}}}]))try{let{result:t,duration:r}=await s();l(e,!0,t,null,r)}catch(t){l(e,!1,null,t,t.duration)}n(!1)};return(0,r.jsxs)("div",{className:"max-w-6xl mx-auto p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"API 客戶端測試 v2"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"測試新的 @novelwebsite/api 套件功能，包含重試機制、Slug 處理和導航功能"}),(0,r.jsx)("button",{onClick:d,disabled:s,className:`px-6 py-3 rounded-lg font-semibold ${s?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:s?"測試進行中...":"開始測試"})]}),e.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"測試結果"}),e.map((e,t)=>(0,r.jsxs)("div",{className:`p-4 rounded-lg border ${e.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("h3",{className:"font-semibold",children:[e.success?"✅":"❌"," ",e.name]}),e.duration&&(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[e.duration,"ms"]})]}),e.success&&e.data&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("pre",{className:"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-64",children:JSON.stringify(e.data,null,2)})}),!e.success&&e.error&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"text-red-700 font-medium",children:"錯誤訊息:"}),(0,r.jsx)("pre",{className:"bg-red-100 p-3 rounded text-sm overflow-auto max-h-64 text-red-800",children:JSON.stringify(e.error,null,2)})]})]},t))]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"測試說明"}),(0,r.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 確保 Django backend 在 http://localhost:8000 運行"}),(0,r.jsx)("li",{children:"• 確保有測試數據 (至少 1-2 本小說)"}),(0,r.jsx)("li",{children:"• 測試包含 API 連通性、Slug 處理、錯誤處理等功能"}),(0,r.jsx)("li",{children:"• 查看瀏覽器開發者工具的 Network 標籤以檢查實際請求"})]})]})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1822:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3670:()=>{},3873:e=>{"use strict";e.exports=require("path")},4193:()=>{},4276:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>l});var r=s(1862),a=s(7221),n=s.n(a);s(4193);let l={title:{default:"瘟仙小說 - Next.js 15 App Router",template:"%s | 瘟仙小說"},description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",keywords:["小說","線上閱讀","Next.js","App Router"],authors:[{name:"NovelWebsite Team"}],creator:"NovelWebsite",publisher:"NovelWebsite",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3001"),openGraph:{type:"website",locale:"zh_TW",url:"/",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",siteName:"瘟仙小說"},twitter:{card:"summary_large_image",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function i({children:e}){return(0,r.jsx)("html",{lang:"zh-TW",className:"h-full",children:(0,r.jsx)("body",{className:`${n().className} h-full bg-gray-50`,children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"瘟仙小說 (Next.js 15 App Router)"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"與 CRA 應用並行運行 - 端口: 3001"})]})}),(0,r.jsx)("main",{className:"max-w-6xl mx-auto px-4 py-6",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t mt-12",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:(0,r.jsx)("p",{className:"text-center text-gray-600 text-sm",children:"\xa9 2025 瘟仙小說 - Next.js 15 App Router 版本"})})})]})})})}},4349:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,3859,23)),Promise.resolve().then(s.t.bind(s,7535,23)),Promise.resolve().then(s.t.bind(s,3555,23)),Promise.resolve().then(s.t.bind(s,7426,23)),Promise.resolve().then(s.t.bind(s,5558,23)),Promise.resolve().then(s.t.bind(s,634,23)),Promise.resolve().then(s.t.bind(s,4344,23)),Promise.resolve().then(s.t.bind(s,2194,23))},4434:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(950).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx","default")},5103:()=>{},5347:(e,t,s)=>{Promise.resolve().then(s.bind(s,436))},6027:(e,t,s)=>{Promise.resolve().then(s.bind(s,4434))},6448:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1862);function a(){return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("section",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8",children:[(0,r.jsx)("div",{className:"h-10 bg-white/20 rounded animate-pulse mb-4 w-2/3"}),(0,r.jsx)("div",{className:"h-6 bg-white/20 rounded animate-pulse mb-6 w-3/4"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"h-12 bg-white/20 rounded-lg animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-12 bg-white/20 rounded-lg animate-pulse w-32"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-20"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded animate-pulse mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-16"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-12"})]})]})]}),(0,r.jsxs)("div",{className:"mt-3 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"})]})]},t))})]}),(0,r.jsx)("section",{className:"grid md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse mb-3 w-24"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-2/3"})]})]},t))})]})}},7085:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6913,23)),Promise.resolve().then(s.t.bind(s,9481,23)),Promise.resolve().then(s.t.bind(s,9073,23)),Promise.resolve().then(s.t.bind(s,1424,23)),Promise.resolve().then(s.t.bind(s,2416,23)),Promise.resolve().then(s.t.bind(s,7060,23)),Promise.resolve().then(s.t.bind(s,7090,23)),Promise.resolve().then(s.t.bind(s,516,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(5406),a=s(1049),n=s(9073),l=s.n(n),i=s(430),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["api-test-v2",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4434)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/api-test-v2/page",pathname:"/api-test-v2",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[67,847],()=>s(9968));module.exports=r})();