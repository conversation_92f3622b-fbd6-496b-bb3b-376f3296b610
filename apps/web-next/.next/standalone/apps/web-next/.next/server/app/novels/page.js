(()=>{var e={};e.id=984,e.ids=[984],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2309:(e,s,t)=>{let{createProxy:r}=t(9369);e.exports=r("/Users/<USER>/Documents/project/NovelWebsite/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_babel-plugin-macros@3.1.0_react-dom@18_4jqqjup23g4vegjq5p5xeio7v4/node_modules/next/dist/client/app-dir/link.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3961:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,9751,23))},4321:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var r=t(1862),n=t(2309),o=t.n(n);let i={title:"小說列表",description:"瀏覽所有可用的小說"};function l(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"小說列表"}),(0,r.jsx)(o(),{href:"/",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回首頁"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3 text-blue-900",children:"\uD83D\uDEA7 開發中"}),(0,r.jsx)("p",{className:"text-blue-800 mb-4",children:"這個頁面正在開發中。未來將整合 Django 後端 API 來顯示小說列表。"}),(0,r.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,r.jsx)("p",{children:"• 將使用 Next.js 15 的 Server Components"}),(0,r.jsx)("p",{children:"• 支援 SSR 和 SSG 以提升 SEO"}),(0,r.jsx)("p",{children:"• 整合現有的 Django API 端點"}),(0,r.jsx)("p",{children:"• 保持與 CRA 版本的功能一致性"})]})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow",children:[(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500",children:"封面圖片"})}),(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-2 text-gray-900",children:["示例小說 ",e]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局..."}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsx)("span",{children:"作者: 示例作者"}),(0,r.jsx)("span",{children:"更新: 2025-01-01"})]}),(0,r.jsx)(o(),{href:`/novels/${e}`,className:"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"閱讀小說"})]},e))})]})}},4457:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2309,23))},5842:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(5406),n=t(1049),o=t(9073),i=t.n(o),l=t(430),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);t.d(s,a);let d={children:["",{children:["novels",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4321)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/novels/page",pathname:"/novels",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[67,213,751,49],()=>t(5842));module.exports=r})();