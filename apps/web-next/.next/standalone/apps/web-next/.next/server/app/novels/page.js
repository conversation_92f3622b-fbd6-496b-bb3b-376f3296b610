(()=>{var e={};e.id=984,e.ids=[984],e.modules={375:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1822:()=>{},2309:(e,s,t)=>{let{createProxy:r}=t(9369);e.exports=r("/Users/<USER>/Documents/project/NovelWebsite/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_babel-plugin-macros@3.1.0_react-dom@18_4jqqjup23g4vegjq5p5xeio7v4/node_modules/next/dist/client/app-dir/link.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3670:()=>{},3873:e=>{"use strict";e.exports=require("path")},3961:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,9751,23))},4193:()=>{},4276:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var r=t(1862),a=t(7221),n=t.n(a);t(4193);let i={title:{default:"瘟仙小說 - Next.js 15 App Router",template:"%s | 瘟仙小說"},description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",keywords:["小說","線上閱讀","Next.js","App Router"],authors:[{name:"NovelWebsite Team"}],creator:"NovelWebsite",publisher:"NovelWebsite",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3001"),openGraph:{type:"website",locale:"zh_TW",url:"/",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",siteName:"瘟仙小說"},twitter:{card:"summary_large_image",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function l({children:e}){return(0,r.jsx)("html",{lang:"zh-TW",className:"h-full",children:(0,r.jsx)("body",{className:`${n().className} h-full bg-gray-50`,children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"瘟仙小說 (Next.js 15 App Router)"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"與 CRA 應用並行運行 - 端口: 3001"})]})}),(0,r.jsx)("main",{className:"max-w-6xl mx-auto px-4 py-6",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t mt-12",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:(0,r.jsx)("p",{className:"text-center text-gray-600 text-sm",children:"\xa9 2025 瘟仙小說 - Next.js 15 App Router 版本"})})})]})})})}},4321:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var r=t(1862),a=t(2309),n=t.n(a);let i={title:"小說列表",description:"瀏覽所有可用的小說"};function l(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"小說列表"}),(0,r.jsx)(n(),{href:"/",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回首頁"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3 text-blue-900",children:"\uD83D\uDEA7 開發中"}),(0,r.jsx)("p",{className:"text-blue-800 mb-4",children:"這個頁面正在開發中。未來將整合 Django 後端 API 來顯示小說列表。"}),(0,r.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,r.jsx)("p",{children:"• 將使用 Next.js 15 的 Server Components"}),(0,r.jsx)("p",{children:"• 支援 SSR 和 SSG 以提升 SEO"}),(0,r.jsx)("p",{children:"• 整合現有的 Django API 端點"}),(0,r.jsx)("p",{children:"• 保持與 CRA 版本的功能一致性"})]})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow",children:[(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500",children:"封面圖片"})}),(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-2 text-gray-900",children:["示例小說 ",e]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:"這是一個示例小說的描述，展示 Next.js 15 App Router 的卡片佈局..."}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,r.jsx)("span",{children:"作者: 示例作者"}),(0,r.jsx)("span",{children:"更新: 2025-01-01"})]}),(0,r.jsx)(n(),{href:`/novels/${e}`,className:"mt-3 block w-full text-center bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"閱讀小說"})]},e))})]})}},4349:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,3859,23)),Promise.resolve().then(t.t.bind(t,7535,23)),Promise.resolve().then(t.t.bind(t,3555,23)),Promise.resolve().then(t.t.bind(t,7426,23)),Promise.resolve().then(t.t.bind(t,5558,23)),Promise.resolve().then(t.t.bind(t,634,23)),Promise.resolve().then(t.t.bind(t,4344,23)),Promise.resolve().then(t.t.bind(t,2194,23))},4457:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2309,23))},5103:()=>{},5842:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>x,tree:()=>o});var r=t(5406),a=t(1049),n=t(9073),i=t.n(n),l=t(430),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["novels",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4321)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/novels/page",pathname:"/novels",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6448:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(1862);function a(){return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("section",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8",children:[(0,r.jsx)("div",{className:"h-10 bg-white/20 rounded animate-pulse mb-4 w-2/3"}),(0,r.jsx)("div",{className:"h-6 bg-white/20 rounded animate-pulse mb-6 w-3/4"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"h-12 bg-white/20 rounded-lg animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-12 bg-white/20 rounded-lg animate-pulse w-32"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-20"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,s)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded animate-pulse mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-16"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-12"})]})]})]}),(0,r.jsxs)("div",{className:"mt-3 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"})]})]},s))})]}),(0,r.jsx)("section",{className:"grid md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,s)=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse mb-3 w-24"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-2/3"})]})]},s))})]})}},7085:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6913,23)),Promise.resolve().then(t.t.bind(t,9481,23)),Promise.resolve().then(t.t.bind(t,9073,23)),Promise.resolve().then(t.t.bind(t,1424,23)),Promise.resolve().then(t.t.bind(t,2416,23)),Promise.resolve().then(t.t.bind(t,7060,23)),Promise.resolve().then(t.t.bind(t,7090,23)),Promise.resolve().then(t.t.bind(t,516,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[67,847,751],()=>t(5842));module.exports=r})();