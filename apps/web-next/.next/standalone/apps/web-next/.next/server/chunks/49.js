exports.id=49,exports.ids=[49],exports.modules={592:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>n,ClientContexts:()=>h,SettingsProvider:()=>m,useAuth:()=>i,useSettings:()=>c});var r=s(9572),a=s(9789);let l=(0,a.createContext)(null),i=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},n=({children:e})=>{let[t,s]=(0,a.useState)(null),[i,n]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(()=>{try{let e=localStorage.getItem("novelwebsite_user");if(e){let t=JSON.parse(e);s(t)}}catch(e){console.error("Failed to load user from localStorage:",e),localStorage.removeItem("novelwebsite_user")}finally{n(!1)}})()},[]);let o=(0,a.useCallback)(async e=>{n(!0);try{let t={id:"1",username:e.username,email:`${e.username}@example.com`,avatar:void 0};s(t),localStorage.setItem("novelwebsite_user",JSON.stringify(t))}catch(e){throw console.error("Login failed:",e),e}finally{n(!1)}},[]),d=(0,a.useCallback)(()=>{s(null),localStorage.removeItem("novelwebsite_user")},[]);return(0,r.jsx)(l.Provider,{value:{user:t,isAuthenticated:null!==t,login:o,logout:d,loading:i},children:e})};n.displayName="AuthProvider";let o={theme:"light",fontSize:"md",fontFamily:"sans",lineHeight:"normal"},d=(0,a.createContext)(null),c=()=>{let e=(0,a.useContext)(d);if(!e)throw Error("useSettings must be used within a SettingsProvider");return e},m=({children:e})=>{let[t,s]=(0,a.useState)(o),[l,i]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(()=>{try{let e=localStorage.getItem("novelwebsite_settings");if(e){let t=JSON.parse(e);s(e=>({...e,...t}))}}catch(e){console.error("Failed to load settings from localStorage:",e),localStorage.removeItem("novelwebsite_settings")}finally{i(!0)}})()},[]);let n=(0,a.useCallback)(e=>{try{localStorage.setItem("novelwebsite_settings",JSON.stringify(e))}catch(e){console.error("Failed to save settings to localStorage:",e)}},[]),c=(0,a.useCallback)(e=>{s(t=>{let s={...t,...e};return l&&n(s),s})},[l,n]),m=(0,a.useCallback)(()=>{s(o),l&&n(o)},[l,n]),h={...t,updateSettings:c,resetSettings:m};return(0,r.jsx)(d.Provider,{value:h,children:e})};m.displayName="SettingsProvider";let h=({children:e})=>(0,r.jsx)(n,{children:(0,r.jsx)(m,{children:e})});h.displayName="ClientContexts"},673:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(9572),a=s(9789),l=s(6049);let i=({title:e="小說閱讀平台",logo:t,navigation:s=[],actions:i,variant:n="default",className:o})=>{let[d,c]=(0,a.useState)(!1);return(0,r.jsx)("header",{className:(0,l.A)("sticky top-0 z-50",{default:"bg-white border-b border-gray-200",transparent:"bg-transparent",solid:"bg-blue-600 text-white"}[n],o),children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[t&&(0,r.jsx)("div",{className:"flex-shrink-0 mr-3",children:t}),(0,r.jsx)("h1",{className:(0,l.A)("text-xl font-bold",{default:"text-gray-900",transparent:"text-gray-900",solid:"text-white"}[n]),children:e})]}),(0,r.jsx)("nav",{className:"hidden md:flex space-x-8",children:s.map(e=>(0,r.jsx)("a",{href:e.href,className:(0,l.A)("px-3 py-2 rounded-md text-sm font-medium transition-colors",e.active?"solid"===n?"bg-blue-700 text-white":"bg-blue-100 text-blue-700":"solid"===n?"text-blue-100 hover:text-white hover:bg-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),...e.external&&{target:"_blank",rel:"noopener noreferrer"},children:e.label},e.href))}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[i&&(0,r.jsx)("div",{className:"hidden md:flex items-center space-x-2",children:i}),(0,r.jsx)("button",{type:"button",className:(0,l.A)("md:hidden p-2 rounded-md","solid"===n?"text-blue-100 hover:text-white hover:bg-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),onClick:()=>{c(!d)},"aria-expanded":d,"aria-label":"開啟主選單",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]})]}),d&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 border-t border-gray-200",children:[s.map(e=>(0,r.jsx)("a",{href:e.href,className:(0,l.A)("block px-3 py-2 rounded-md text-base font-medium transition-colors",e.active?"solid"===n?"bg-blue-700 text-white":"bg-blue-100 text-blue-700":"solid"===n?"text-blue-100 hover:text-white hover:bg-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),onClick:()=>c(!1),...e.external&&{target:"_blank",rel:"noopener noreferrer"},children:e.label},e.href)),i&&(0,r.jsx)("div",{className:"pt-4 border-t border-gray-200",children:i})]})})]})})};i.displayName="Header";let n=i},1822:()=>{},2981:(e,t,s)=>{"use strict";s.d(t,{iq:()=>m,mL:()=>h.ClientContexts,Y9:()=>n.default,PE:()=>i,fi:()=>d});var r=s(1862),a=s(4543);let l=({children:e,header:t,footer:s,sidebar:l,maxWidth:i="lg",className:n})=>(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t&&(0,r.jsx)("header",{className:"sticky top-0 z-50 bg-white shadow-sm border-b",children:t}),(0,r.jsxs)("div",{className:"flex flex-1",children:[l&&(0,r.jsx)("aside",{className:"hidden lg:block w-64 bg-white border-r",children:(0,r.jsx)("div",{className:"sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto",children:l})}),(0,r.jsx)("main",{className:"flex-1",children:(0,r.jsx)("div",{className:(0,a.A)("w-full mx-auto p-4 sm:p-6 lg:p-8",{sm:"max-w-sm",md:"max-w-md",lg:"max-w-4xl",xl:"max-w-6xl","2xl":"max-w-7xl",full:"max-w-full"}[i],n),children:e})})]}),s&&(0,r.jsx)("footer",{className:"bg-white border-t",children:s})]});l.displayName="Layout";let i=l;var n=s(4863);let o=({novel:e,href:t,onClick:s,className:l,children:i})=>{let n=(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("img",{src:e.cover||"/default-cover.jpg",alt:e.title,className:"w-16 h-20 object-cover rounded flex-shrink-0",loading:"lazy"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 truncate mb-1",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["作者：",e.author]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,r.jsx)("span",{className:(0,a.A)("px-2 py-1 rounded",{completed:"bg-green-100 text-green-800",ongoing:"bg-blue-100 text-blue-800"}[e.status]),children:"completed"===e.status?"已完結":"連載中"}),(0,r.jsxs)("span",{children:["\uD83D\uDC41️ ",e.views.toLocaleString()]}),e.favorites>0&&(0,r.jsxs)("span",{children:["❤️ ",e.favorites.toLocaleString()]})]}),e.category&&(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:e.category.name})})]})]}),e.description&&(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-3 line-clamp-2",children:e.description}),i]}),o=(0,a.A)("bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow",l);return t?(0,r.jsx)("a",{href:t,className:(0,a.A)(o,"block"),onClick:s,children:n}):s?(0,r.jsx)("button",{type:"button",className:(0,a.A)(o,"block w-full text-left"),onClick:s,children:n}):(0,r.jsx)("div",{className:o,children:n})};o.displayName="NovelCard";let d=o,c=({chapter:e,href:t,onClick:s,className:l,children:i})=>{let n=(0,r.jsxs)("div",{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsxs)("span",{className:"font-medium text-gray-900",children:["第 ",e.chapter_number," 章"]}),e.is_vip&&(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded",children:"\uD83D\uDC51 VIP"})]}),(0,r.jsx)("h4",{className:"text-gray-900 font-medium truncate mb-2",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["\uD83D\uDC41️ ",e.views.toLocaleString()]}),(0,r.jsx)("span",{children:(e=>{try{return new Date(e).toLocaleDateString("zh-TW",{year:"numeric",month:"2-digit",day:"2-digit"})}catch{return e}})(e.updated_at)})]})]}),(0,r.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),i]}),o=(0,a.A)("block border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",e.is_vip&&"border-yellow-200 bg-yellow-50/30",l);return t?(0,r.jsx)("a",{href:t,className:o,onClick:s,children:n}):s?(0,r.jsx)("button",{type:"button",className:(0,a.A)(o,"w-full text-left"),onClick:s,children:n}):(0,r.jsx)("div",{className:o,children:n})};c.displayName="ChapterCard";let m=c;var h=s(7793)},3670:()=>{},4035:(e,t,s)=>{Promise.resolve().then(s.bind(s,673)),Promise.resolve().then(s.bind(s,592))},4193:()=>{},4276:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>n});var r=s(1862),a=s(7221),l=s.n(a),i=s(2981);s(4193);let n={title:{default:"瘟仙小說 - Next.js 15 App Router",template:"%s | 瘟仙小說"},description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",keywords:["小說","線上閱讀","Next.js","App Router"],authors:[{name:"NovelWebsite Team"}],creator:"NovelWebsite",publisher:"NovelWebsite",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3001"),openGraph:{type:"website",locale:"zh_TW",url:"/",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構",siteName:"瘟仙小說"},twitter:{card:"summary_large_image",title:"瘟仙小說 - Next.js 15 App Router",description:"線上小說閱讀平台 - 使用 Next.js 15 App Router 架構"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function o({children:e}){let t=(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"登入"}),(0,r.jsx)("button",{className:"px-3 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50",children:"註冊"})]});return(0,r.jsx)("html",{lang:"zh-TW",className:"h-full",children:(0,r.jsx)("body",{className:`${l().className} h-full`,children:(0,r.jsx)(i.mL,{children:(0,r.jsx)(i.PE,{header:(0,r.jsx)(i.Y9,{title:"小說閱讀平台 (Next.js 15)",navigation:[{label:"首頁",href:"/",active:!1},{label:"小說分類",href:"/categories",active:!1},{label:"排行榜",href:"/rankings",active:!1},{label:"API 測試",href:"/api-test-v2",active:!1}],actions:t,variant:"default"}),footer:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:(0,r.jsx)("p",{className:"text-center text-gray-600 text-sm",children:"\xa9 2025 小說閱讀平台 - Next.js 15 App Router 版本"})}),maxWidth:"xl",children:e})})})})}},4349:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,3859,23)),Promise.resolve().then(s.t.bind(s,7535,23)),Promise.resolve().then(s.t.bind(s,3555,23)),Promise.resolve().then(s.t.bind(s,7426,23)),Promise.resolve().then(s.t.bind(s,5558,23)),Promise.resolve().then(s.t.bind(s,634,23)),Promise.resolve().then(s.t.bind(s,4344,23)),Promise.resolve().then(s.t.bind(s,2194,23))},4863:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(950).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/project/NovelWebsite/packages/ui/dist/components/Header.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/packages/ui/dist/components/Header.js","default")},6448:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1862);function a(){return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("section",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8",children:[(0,r.jsx)("div",{className:"h-10 bg-white/20 rounded animate-pulse mb-4 w-2/3"}),(0,r.jsx)("div",{className:"h-6 bg-white/20 rounded animate-pulse mb-6 w-3/4"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"h-12 bg-white/20 rounded-lg animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-12 bg-white/20 rounded-lg animate-pulse w-32"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-32"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-20"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("div",{className:"w-16 h-20 bg-gray-200 rounded animate-pulse flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded animate-pulse mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse mb-2 w-2/3"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-16"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-12"})]})]})]}),(0,r.jsxs)("div",{className:"mt-3 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"})]})]},t))})]}),(0,r.jsx)("section",{className:"grid md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse mb-3 w-24"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-2/3"})]})]},t))})]})}},7085:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6913,23)),Promise.resolve().then(s.t.bind(s,9481,23)),Promise.resolve().then(s.t.bind(s,9073,23)),Promise.resolve().then(s.t.bind(s,1424,23)),Promise.resolve().then(s.t.bind(s,2416,23)),Promise.resolve().then(s.t.bind(s,7060,23)),Promise.resolve().then(s.t.bind(s,7090,23)),Promise.resolve().then(s.t.bind(s,516,23))},7793:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>l,ClientContexts:()=>a,SettingsProvider:()=>n,useAuth:()=>i,useSettings:()=>o});var r=s(950);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ClientContexts() from the server but ClientContexts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/packages/ui/dist/contexts/ClientContexts.js","ClientContexts"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/packages/ui/dist/contexts/ClientContexts.js","AuthProvider"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/packages/ui/dist/contexts/ClientContexts.js","useAuth"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/packages/ui/dist/contexts/ClientContexts.js","SettingsProvider"),o=(0,r.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/packages/ui/dist/contexts/ClientContexts.js","useSettings")},8011:(e,t,s)=>{Promise.resolve().then(s.bind(s,4863)),Promise.resolve().then(s.bind(s,7793))}};