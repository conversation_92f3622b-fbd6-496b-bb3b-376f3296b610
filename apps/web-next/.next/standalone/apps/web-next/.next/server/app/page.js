"use strict";(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},8952:(e,s,t)=>{t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(5406),l=t(1049),n=t(9073),a=t.n(n),i=t(430),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9291)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,8467,23)),"next/dist/client/components/unauthorized-error"]}],c=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9291:(e,s,t)=>{t.r(s),t.d(s,{default:()=>x,metadata:()=>i,revalidate:()=>o});var r=t(1862),l=t(2309),n=t.n(l),a=t(6497);let i={title:"小說閱讀平台 | Next.js 15 App Router",description:"現代化的小說閱讀平台，提供豐富的小說資源和優質的閱讀體驗",openGraph:{title:"小說閱讀平台",description:"現代化的小說閱讀平台，提供豐富的小說資源和優質的閱讀體驗",type:"website"}},o=60;async function d(){try{return(await a.WN.getList({limit:12})).results}catch(e){return console.error("Error fetching novels:",e),[]}}function c({novel:e}){let s=(0,a.z9)(e.title,Number(e.id));return(0,r.jsx)(n(),{href:`/novels/${s}`,className:"block bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("img",{src:e.cover||"/default-cover.jpg",alt:e.title,className:"w-16 h-20 object-cover rounded flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 truncate mb-1",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["作者：",e.author]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,r.jsx)("span",{className:`px-2 py-1 rounded ${"completed"===e.status?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:"completed"===e.status?"已完結":"連載中"}),(0,r.jsxs)("span",{children:["\uD83D\uDC41️ ",e.views.toLocaleString()]})]})]})]}),e.description&&(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-3 line-clamp-2",children:e.description})]})})}async function x(){let e=await d();return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("section",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"小說閱讀平台"}),(0,r.jsx)("p",{className:"text-xl mb-6",children:"現代化的小說閱讀體驗，支援 SSR/SSG，提供優質的閱讀環境"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(n(),{href:"#latest-novels",className:"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"開始閱讀"}),(0,r.jsx)(n(),{href:"/about",className:"border border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors",children:"了解更多"})]})]}),(0,r.jsxs)("section",{id:"latest-novels",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"最新小說"}),(0,r.jsx)(n(),{href:"/novels",className:"text-blue-600 hover:text-blue-800 font-medium",children:"查看全部 →"})]}),e.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsx)(c,{novel:e},e.id))}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-8 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"暫無小說內容"}),(0,r.jsx)("p",{className:"text-gray-600",children:"請確保 Django backend 正在運行並包含測試數據"})]})]}),(0,r.jsxs)("section",{className:"grid md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:"\uD83D\uDE80 App Router"}),(0,r.jsx)("p",{className:"text-gray-600",children:"使用 Next.js 15 最新的 App Router 架構，提供更好的開發體驗和性能"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:"⚡ 伺服器端渲染"}),(0,r.jsx)("p",{className:"text-gray-600",children:"支援 SSR 和 SSG，提升 SEO 效果和頁面載入速度"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:"\uD83D\uDD04 ISR 快取"}),(0,r.jsx)("p",{className:"text-gray-600",children:"增量靜態重新生成，確保內容即時更新且性能優異"})]})]}),(0,r.jsxs)("section",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"系統狀態"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-700",children:"應用資訊"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Next.js 版本: 15.1.3"}),(0,r.jsx)("li",{children:"• React 版本: 18.2.0"}),(0,r.jsx)("li",{children:"• TypeScript 支援: ✅"}),(0,r.jsx)("li",{children:"• Tailwind CSS: ✅"}),(0,r.jsx)("li",{children:"• App Router: ✅"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-700",children:"Monorepo 整合"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• pnpm workspace: ✅"}),(0,r.jsx)("li",{children:"• Turborepo 支援: ✅"}),(0,r.jsx)("li",{children:"• 共享套件: 準備中"}),(0,r.jsx)("li",{children:"• CI/CD 整合: 準備中"}),(0,r.jsx)("li",{children:"• API 代理: 準備中"})]})]})]})]}),(0,r.jsxs)("section",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3 text-blue-900",children:"開發資訊"}),(0,r.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("p",{children:"• 當前端口: 3001 (Next.js 15)"}),(0,r.jsx)("p",{children:"• CRA 應用端口: 3000"}),(0,r.jsx)("p",{children:"• Django 後端端口: 8000"}),(0,r.jsxs)("p",{children:["• 可使用 ",(0,r.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:"turbo dev"})," 同時啟動所有服務"]})]})]})]})}},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[67,847,751,89],()=>t(8952));module.exports=r})();