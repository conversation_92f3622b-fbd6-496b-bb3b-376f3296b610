{"name": "@novelwebsite/api", "version": "0.1.0", "private": true, "description": "Shared API client for NovelWebsite monorepo", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["api", "client", "monorepo", "shared"], "dependencies": {}, "devDependencies": {"typescript": "^5.0.0", "jest": "^29.7.0", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "ts-jest": "^29.1.0"}, "peerDependencies": {"typescript": ">=4.9.0"}}