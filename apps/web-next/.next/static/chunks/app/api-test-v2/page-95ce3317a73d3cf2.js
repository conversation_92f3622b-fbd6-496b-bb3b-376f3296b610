(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[739],{4493:(t,e,a)=>{Promise.resolve().then(a.bind(a,8077))},8077:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>x});var r=a(3762),s=a(2570),n=a(1535),l=a(4411),i=a(6957);let c={baseURL:a(1084).env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api/v1",timeout:3e4,defaultRevalidate:60,retries:3,retryDelay:1e3};class o extends Error{constructor(t,e,a){super(t),this.status=e,this.data=a,this.name="APIClientError"}}function u(t,e){let a=t.replace(/[：！？。，、；""''（）【】《》〈〉]/g,"").replace(/[^\u4e00-\u9fa5\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+|-+$/g,"").trim();return e?"".concat(a,"-").concat(e):a}function d(t){let e=t.match(/-(\d+)$/);return e?parseInt(e[1],10):null}async function h(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:c.retries,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:c.retryDelay,r=null;for(let s=0;s<=e;s++)try{return await t()}catch(n){if(r=n,s===e||n instanceof o&&n.status>=400&&n.status<500)break;let t=a*Math.pow(2,s);await function(t){return new Promise(e=>setTimeout(e,t))}(t)}throw r||Error("Unknown error in retry mechanism")}async function g(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{revalidate:a=c.defaultRevalidate,tags:r=[],enableRetry:s=!0}=e,u=(0,i._)(e,["revalidate","tags","enableRetry"]),d="".concat(c.baseURL).concat(t),g=async()=>{let t=await fetch(d,(0,l._)((0,n._)({},u),{next:{revalidate:a,tags:r},headers:(0,n._)({"Content-Type":"application/json"},u.headers)}));if(!t.ok){let e=await t.json().catch(()=>({}));throw new o("API request failed: ".concat(t.status," ").concat(t.statusText),t.status,e)}return await t.json()};try{if(s)return await h(g);return await g()}catch(t){if(t instanceof o)throw t;throw new o("Network error: ".concat(t instanceof Error?t.message:"Unknown error"),0)}}let m={async getList(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&e.append(a,String(r))});let a=e.toString();return g("/novels/".concat(a?"?".concat(a):""),{tags:["novels","novels-list"]})},getDetail:async t=>g("/novels/".concat(t,"/"),{tags:["novels","novel-".concat(t)]}),async getDetailBySlug(t){let e=d(t);if(e)try{return await this.getDetail(e)}catch(t){if(t instanceof o&&404===t.status)return null;throw t}if(/^\d+$/.test(t))try{return await this.getDetail(parseInt(t,10))}catch(t){if(t instanceof o&&404===t.status)return null;throw t}try{return(await this.getList({search:t.replace(/-/g," ")})).results.find(e=>u(e.title,Number(e.id))===t)||null}catch(t){return console.error("Error searching novel by slug:",t),null}},async getChapters(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;Object.entries(e).forEach(t=>{let[e,r]=t;null!=r&&a.append(e,String(r))});let r=a.toString();return g("/novels/".concat(t,"/chapters/").concat(r?"?".concat(r):""),{tags:["chapters","novel-".concat(t,"-chapters")]})},async getNovelIdBySlug(t){let e=d(t);if(e)try{return await this.getDetail(e),e}catch(t){if(t instanceof o&&404===t.status)return null;throw t}if(/^\d+$/.test(t)){let e=parseInt(t,10);try{return await this.getDetail(e),e}catch(t){if(t instanceof o&&404===t.status)return null;throw t}}try{let e=(await this.getList({search:t.replace(/-/g," "),limit:50})).results.find(e=>u(e.title,Number(e.id))===t);return e?Number(e.id):null}catch(t){return console.error("Error searching novel by slug:",t),null}}},w={check:async()=>g("/health/",{revalidate:0})};function x(){let[t,e]=(0,s.useState)([]),[a,n]=(0,s.useState)(!1),l=(t,a,r,s,n)=>{e(e=>[...e,{name:t,success:a,data:r,error:s,duration:n}])},i=async()=>{for(let{name:t,test:a}of(n(!0),e([]),[{name:"健康檢查",test:async()=>{let t=Date.now();return{result:await w.check(),duration:Date.now()-t}}},{name:"小說列表 API",test:async()=>{let t=Date.now();return{result:await m.getList({limit:5}),duration:Date.now()-t}}},{name:"Slug 生成測試",test:async()=>{let t=Date.now();return{result:[{title:"瘟仙",id:1,expected:"瘟仙-1"},{title:"修真：從撿垃圾開始！",id:123,expected:"修真從撿垃圾開始-123"},{title:"我的 修仙 人生...",id:456,expected:"我的-修仙-人生-456"}].map(t=>{let{title:e,id:a,expected:r}=t,s=u(e,a),n=d(s);return{title:e,id:a,generated:s,expected:r,extractedId:n,slugMatch:s===r,idMatch:n===a}}),duration:Date.now()-t}}},{name:"小說詳情 API (如果有數據)",test:async()=>{let t=Date.now();try{let e=await m.getList({limit:1});if(e.results.length>0){let a=e.results[0],r=await m.getDetail(Number(a.id)),s=Date.now()-t;return{result:r,duration:s}}{let e=Date.now()-t;return{result:{message:"沒有小說數據可測試"},duration:e}}}catch(e){throw{error:e,duration:Date.now()-t}}}},{name:"Slug 查詢測試 (如果有數據)",test:async()=>{let t=Date.now();try{let e=await m.getList({limit:1});if(e.results.length>0){let a=e.results[0],r=u(a.title,Number(a.id)),s=await m.getDetailBySlug(r),n=Date.now()-t;return{result:{slug:r,novel:s},duration:n}}{let e=Date.now()-t;return{result:{message:"沒有小說數據可測試"},duration:e}}}catch(e){throw{error:e,duration:Date.now()-t}}}}]))try{let{result:e,duration:r}=await a();l(t,!0,e,null,r)}catch(e){l(t,!1,null,e,e.duration)}n(!1)};return(0,r.jsxs)("div",{className:"max-w-6xl mx-auto p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"API 客戶端測試 v2"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"測試新的 @novelwebsite/api 套件功能，包含重試機制、Slug 處理和導航功能"}),(0,r.jsx)("button",{onClick:i,disabled:a,className:"px-6 py-3 rounded-lg font-semibold ".concat(a?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:a?"測試進行中...":"開始測試"})]}),t.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"測試結果"}),t.map((t,e)=>(0,r.jsxs)("div",{className:"p-4 rounded-lg border ".concat(t.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("h3",{className:"font-semibold",children:[t.success?"✅":"❌"," ",t.name]}),t.duration&&(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[t.duration,"ms"]})]}),t.success&&t.data&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("pre",{className:"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-64",children:JSON.stringify(t.data,null,2)})}),!t.success&&t.error&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"text-red-700 font-medium",children:"錯誤訊息:"}),(0,r.jsx)("pre",{className:"bg-red-100 p-3 rounded text-sm overflow-auto max-h-64 text-red-800",children:JSON.stringify(t.error,null,2)})]})]},e))]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"測試說明"}),(0,r.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 確保 Django backend 在 http://localhost:8000 運行"}),(0,r.jsx)("li",{children:"• 確保有測試數據 (至少 1-2 本小說)"}),(0,r.jsx)("li",{children:"• 測試包含 API 連通性、Slug 處理、錯誤處理等功能"}),(0,r.jsx)("li",{children:"• 查看瀏覽器開發者工具的 Network 標籤以檢查實際請求"})]})]})]})}}},t=>{var e=e=>t(t.s=e);t.O(0,[121,76,358],()=>e(4493)),_N_E=t.O()}]);