(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72],{5880:(e,s,l)=>{Promise.resolve().then(l.bind(l,5942))},5942:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>m});var t=l(1535),r=l(4411),a=l(3762),i=l(2570),d=l(7358);let n=e=>{let{size:s="md",color:l="primary",text:t="載入中...",className:r,children:i}=e;return(0,a.jsxs)("div",{className:(0,d.A)("flex items-center justify-center",r),role:"status","aria-label":t,"aria-live":"polite",children:[(0,a.jsx)("div",{className:(0,d.A)("animate-spin rounded-full border-b-2 border-t-2",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[s],{primary:"border-blue-500",secondary:"border-gray-500",white:"border-white"}[l]),"aria-hidden":"true"}),(t||i)&&(0,a.jsx)("div",{className:(0,d.A)("ml-3",{sm:"text-sm",md:"text-base",lg:"text-lg"}[s],{primary:"text-gray-600",secondary:"text-gray-500",white:"text-white"}[l]),children:i||t})]})};n.displayName="LoadingSpinner",l(4441);let c=e=>{let{novel:s,href:l,onClick:t,className:r,children:i}=e,n=(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("img",{src:s.cover||"/default-cover.jpg",alt:s.title,className:"w-16 h-20 object-cover rounded flex-shrink-0",loading:"lazy"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 truncate mb-1",children:s.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["作者：",s.author]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,a.jsx)("span",{className:(0,d.A)("px-2 py-1 rounded",{completed:"bg-green-100 text-green-800",ongoing:"bg-blue-100 text-blue-800"}[s.status]),children:"completed"===s.status?"已完結":"連載中"}),(0,a.jsxs)("span",{children:["\uD83D\uDC41️ ",s.views.toLocaleString()]}),s.favorites>0&&(0,a.jsxs)("span",{children:["❤️ ",s.favorites.toLocaleString()]})]}),s.category&&(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:s.category.name})})]})]}),s.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-3 line-clamp-2",children:s.description}),i]}),c=(0,d.A)("bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow",r);return l?(0,a.jsx)("a",{href:l,className:(0,d.A)(c,"block"),onClick:t,children:n}):t?(0,a.jsx)("button",{type:"button",className:(0,d.A)(c,"block w-full text-left"),onClick:t,children:n}):(0,a.jsx)("div",{className:c,children:n})};c.displayName="NovelCard";let o=e=>{let{chapter:s,href:l,onClick:t,className:r,children:i}=e,n=(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsxs)("span",{className:"font-medium text-gray-900",children:["第 ",s.chapter_number," 章"]}),s.is_vip&&(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded",children:"\uD83D\uDC51 VIP"})]}),(0,a.jsx)("h4",{className:"text-gray-900 font-medium truncate mb-2",children:s.title}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["\uD83D\uDC41️ ",s.views.toLocaleString()]}),(0,a.jsx)("span",{children:(e=>{try{return new Date(e).toLocaleDateString("zh-TW",{year:"numeric",month:"2-digit",day:"2-digit"})}catch(s){return e}})(s.updated_at)})]})]}),(0,a.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),i]}),c=(0,d.A)("block border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",s.is_vip&&"border-yellow-200 bg-yellow-50/30",r);return l?(0,a.jsx)("a",{href:l,className:c,onClick:t,children:n}):t?(0,a.jsx)("button",{type:"button",className:(0,d.A)(c,"w-full text-left"),onClick:t,children:n}):(0,a.jsx)("div",{className:c,children:n})};o.displayName="ChapterCard";var x=l(9054);function m(){let[e,s]=(0,i.useState)(!1),{user:l,isAuthenticated:d,login:m,logout:h}=(0,x.useAuth)(),{theme:j,fontSize:g,updateSettings:u}=(0,x.useSettings)(),p={id:"1",title:"測試小說標題",author:"測試作者",description:"這是一個測試小說的描述，用來驗證 NovelCard 組件的顯示效果。",cover:"/default-cover.jpg",status:"ongoing",views:12345,favorites:678,category:{id:1,name:"玄幻"}},b=async()=>{s(!0);try{await m({username:"testuser",password:"password"})}catch(e){console.error("Login test failed:",e)}finally{s(!1)}},v=e=>{u({theme:e})},N=e=>{u({fontSize:e})};return(0,a.jsxs)("div",{className:"space-y-12",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"UI 組件測試頁面"}),(0,a.jsx)("p",{className:"text-gray-600",children:"驗證 @novelwebsite/ui 套件中所有組件的功能和樣式"})]}),(0,a.jsxs)("section",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"LoadingSpinner 組件"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"小尺寸"}),(0,a.jsx)(n,{size:"sm",color:"primary",text:"載入中..."})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"中等尺寸"}),(0,a.jsx)(n,{size:"md",color:"secondary",text:"處理中..."})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"大尺寸"}),(0,a.jsx)(n,{size:"lg",color:"white",text:"請稍候...",className:"bg-blue-600 p-4 rounded"})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("button",{onClick:()=>s(!e),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:e?"停止載入":"開始載入"}),e&&(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(n,{text:"動態載入測試..."})})]})]}),(0,a.jsxs)("section",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"NovelCard 組件"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"連結模式"}),(0,a.jsx)(c,{novel:p,href:"/novels/test-novel-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"點擊模式"}),(0,a.jsx)(c,{novel:(0,r._)((0,t._)({},p),{status:"completed"}),onClick:()=>alert("NovelCard 被點擊了！")})]})]})]}),(0,a.jsxs)("section",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"ChapterCard 組件"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"普通章節"}),(0,a.jsx)(o,{chapter:{id:"1",title:"第一章：開始的地方",chapter_number:1,updated_at:"2025-01-01T00:00:00Z",views:5432,is_vip:!1},href:"/novels/test-novel/chapters/1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"VIP 章節"}),(0,a.jsx)(o,{chapter:{id:"2",title:"第二章：VIP 章節",chapter_number:2,updated_at:"2025-01-02T00:00:00Z",views:3210,is_vip:!0},onClick:()=>alert("VIP 章節被點擊了！")})]})]})]}),(0,a.jsxs)("section",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Context API 測試"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"認證狀態"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"登入狀態："}),(0,a.jsx)("span",{className:d?"text-green-600":"text-red-600",children:d?"已登入":"未登入"})]}),l&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"用戶："})," ",l.username," (",l.email,")"]}),(0,a.jsx)("div",{className:"flex gap-2",children:d?(0,a.jsx)("button",{onClick:()=>{h()},className:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"登出"}):(0,a.jsx)("button",{onClick:b,disabled:e,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50",children:e?"登入中...":"測試登入"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-3",children:"設定管理"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"當前主題："})," ",j]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"字體大小："})," ",g]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"主題："}),(0,a.jsxs)("select",{value:j,onChange:e=>v(e.target.value),className:"border border-gray-300 rounded px-3 py-1",children:[(0,a.jsx)("option",{value:"light",children:"淺色"}),(0,a.jsx)("option",{value:"dark",children:"深色"}),(0,a.jsx)("option",{value:"sepia",children:"護眼"}),(0,a.jsx)("option",{value:"night",children:"夜間"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"字體大小："}),(0,a.jsxs)("select",{value:g,onChange:e=>N(e.target.value),className:"border border-gray-300 rounded px-3 py-1",children:[(0,a.jsx)("option",{value:"sm",children:"小"}),(0,a.jsx)("option",{value:"md",children:"中"}),(0,a.jsx)("option",{value:"lg",children:"大"}),(0,a.jsx)("option",{value:"xl",children:"特大"})]})]})]})]})]})]})]}),(0,a.jsxs)("section",{className:"bg-green-50 rounded-lg p-6 border border-green-200",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-green-900 mb-4",children:"✅ 測試狀態"}),(0,a.jsxs)("ul",{className:"space-y-2 text-green-800",children:[(0,a.jsx)("li",{children:"✅ LoadingSpinner 組件正常渲染"}),(0,a.jsx)("li",{children:"✅ NovelCard 組件正常渲染"}),(0,a.jsx)("li",{children:"✅ ChapterCard 組件正常渲染"}),(0,a.jsx)("li",{children:"✅ AuthContext 正常工作"}),(0,a.jsx)("li",{children:"✅ SettingsContext 正常工作"}),(0,a.jsx)("li",{children:"✅ SSR/CSR Hydration 無錯誤"})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[121,76,358],()=>s(5880)),_N_E=e.O()}]);