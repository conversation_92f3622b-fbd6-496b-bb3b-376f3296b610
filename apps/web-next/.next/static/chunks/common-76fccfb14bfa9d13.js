(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[76],{387:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9839,23)),Promise.resolve().then(r.bind(r,4441)),Promise.resolve().then(r.bind(r,9054))},1674:()=>{},2540:()=>{},4441:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var l=r(1535),s=r(4411),a=r(3762),o=r(2570),i=r(7358);let n=e=>{let{title:t="小說閱讀平台",logo:r,navigation:n=[],actions:d,variant:c="default",className:h}=e,[u,x]=(0,o.useState)(!1);return(0,a.jsx)("header",{className:(0,i.A)("sticky top-0 z-50",{default:"bg-white border-b border-gray-200",transparent:"bg-transparent",solid:"bg-blue-600 text-white"}[c],h),children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[r&&(0,a.jsx)("div",{className:"flex-shrink-0 mr-3",children:r}),(0,a.jsx)("h1",{className:(0,i.A)("text-xl font-bold",{default:"text-gray-900",transparent:"text-gray-900",solid:"text-white"}[c]),children:t})]}),(0,a.jsx)("nav",{className:"hidden md:flex space-x-8",children:n.map(e=>(0,a.jsx)("a",(0,s._)((0,l._)({href:e.href,className:(0,i.A)("px-3 py-2 rounded-md text-sm font-medium transition-colors",e.active?"solid"===c?"bg-blue-700 text-white":"bg-blue-100 text-blue-700":"solid"===c?"text-blue-100 hover:text-white hover:bg-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100")},e.external&&{target:"_blank",rel:"noopener noreferrer"}),{children:e.label}),e.href))}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[d&&(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-2",children:d}),(0,a.jsx)("button",{type:"button",className:(0,i.A)("md:hidden p-2 rounded-md","solid"===c?"text-blue-100 hover:text-white hover:bg-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),onClick:()=>{x(!u)},"aria-expanded":u,"aria-label":"開啟主選單",children:(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u?(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]})]}),u&&(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 border-t border-gray-200",children:[n.map(e=>(0,a.jsx)("a",(0,s._)((0,l._)({href:e.href,className:(0,i.A)("block px-3 py-2 rounded-md text-base font-medium transition-colors",e.active?"solid"===c?"bg-blue-700 text-white":"bg-blue-100 text-blue-700":"solid"===c?"text-blue-100 hover:text-white hover:bg-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),onClick:()=>x(!1)},e.external&&{target:"_blank",rel:"noopener noreferrer"}),{children:e.label}),e.href)),d&&(0,a.jsx)("div",{className:"pt-4 border-t border-gray-200",children:d})]})})]})})};n.displayName="Header";let d=n},5303:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9839,23))},9054:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i,ClientContexts:()=>m,SettingsProvider:()=>x,useAuth:()=>o,useSettings:()=>u});var l=r(3762),s=r(2570);let a=(0,s.createContext)(null),o=()=>{let e=(0,s.useContext)(a);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},i=e=>{let{children:t}=e,[r,o]=(0,s.useState)(null),[i,n]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(()=>{try{let e=localStorage.getItem("novelwebsite_user");if(e){let t=JSON.parse(e);o(t)}}catch(e){console.error("Failed to load user from localStorage:",e),localStorage.removeItem("novelwebsite_user")}finally{n(!1)}})()},[]);let d=(0,s.useCallback)(async e=>{n(!0);try{let t={id:"1",username:e.username,email:"".concat(e.username,"@example.com"),avatar:void 0};o(t),localStorage.setItem("novelwebsite_user",JSON.stringify(t))}catch(e){throw console.error("Login failed:",e),e}finally{n(!1)}},[]),c=(0,s.useCallback)(()=>{o(null),localStorage.removeItem("novelwebsite_user")},[]);return(0,l.jsx)(a.Provider,{value:{user:r,isAuthenticated:null!==r,login:d,logout:c,loading:i},children:t})};i.displayName="AuthProvider";var n=r(1535),d=r(4411);let c={theme:"light",fontSize:"md",fontFamily:"sans",lineHeight:"normal"},h=(0,s.createContext)(null),u=()=>{let e=(0,s.useContext)(h);if(!e)throw Error("useSettings must be used within a SettingsProvider");return e},x=e=>{let{children:t}=e,[r,a]=(0,s.useState)(c),[o,i]=(0,s.useState)(!1);(0,s.useEffect)(()=>{(()=>{try{let e=localStorage.getItem("novelwebsite_settings");if(e){let t=JSON.parse(e);a(e=>(0,n._)({},e,t))}}catch(e){console.error("Failed to load settings from localStorage:",e),localStorage.removeItem("novelwebsite_settings")}finally{i(!0)}})()},[]);let u=(0,s.useCallback)(e=>{try{localStorage.setItem("novelwebsite_settings",JSON.stringify(e))}catch(e){console.error("Failed to save settings to localStorage:",e)}},[]),x=(0,s.useCallback)(e=>{a(t=>{let r=(0,n._)({},t,e);return o&&u(r),r})},[o,u]),m=(0,s.useCallback)(()=>{a(c),o&&u(c)},[o,u]),b=(0,d._)((0,n._)({},r),{updateSettings:x,resetSettings:m});return(0,l.jsx)(h.Provider,{value:b,children:t})};x.displayName="SettingsProvider";let m=e=>{let{children:t}=e;return(0,l.jsx)(i,{children:(0,l.jsx)(x,{children:t})})};m.displayName="ClientContexts"}}]);