(()=>{var e={};e.id=72,e.ids=[72],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1268:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(9572),l=t(9789),i=t(6049);let n=({size:e="md",color:s="primary",text:t="載入中...",className:l,children:n})=>(0,r.jsxs)("div",{className:(0,i.A)("flex items-center justify-center",l),role:"status","aria-label":t,"aria-live":"polite",children:[(0,r.jsx)("div",{className:(0,i.A)("animate-spin rounded-full border-b-2 border-t-2",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e],{primary:"border-blue-500",secondary:"border-gray-500",white:"border-white"}[s]),"aria-hidden":"true"}),(t||n)&&(0,r.jsx)("div",{className:(0,i.A)("ml-3",{sm:"text-sm",md:"text-base",lg:"text-lg"}[e],{primary:"text-gray-600",secondary:"text-gray-500",white:"text-white"}[s]),children:n||t})]});n.displayName="LoadingSpinner",t(673);let a=({novel:e,href:s,onClick:t,className:l,children:n})=>{let a=(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("img",{src:e.cover||"/default-cover.jpg",alt:e.title,className:"w-16 h-20 object-cover rounded flex-shrink-0",loading:"lazy"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 truncate mb-1",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["作者：",e.author]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,r.jsx)("span",{className:(0,i.A)("px-2 py-1 rounded",{completed:"bg-green-100 text-green-800",ongoing:"bg-blue-100 text-blue-800"}[e.status]),children:"completed"===e.status?"已完結":"連載中"}),(0,r.jsxs)("span",{children:["\uD83D\uDC41️ ",e.views.toLocaleString()]}),e.favorites>0&&(0,r.jsxs)("span",{children:["❤️ ",e.favorites.toLocaleString()]})]}),e.category&&(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:e.category.name})})]})]}),e.description&&(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-3 line-clamp-2",children:e.description}),n]}),d=(0,i.A)("bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow",l);return s?(0,r.jsx)("a",{href:s,className:(0,i.A)(d,"block"),onClick:t,children:a}):t?(0,r.jsx)("button",{type:"button",className:(0,i.A)(d,"block w-full text-left"),onClick:t,children:a}):(0,r.jsx)("div",{className:d,children:a})};a.displayName="NovelCard";let d=({chapter:e,href:s,onClick:t,className:l,children:n})=>{let a=(0,r.jsxs)("div",{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsxs)("span",{className:"font-medium text-gray-900",children:["第 ",e.chapter_number," 章"]}),e.is_vip&&(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded",children:"\uD83D\uDC51 VIP"})]}),(0,r.jsx)("h4",{className:"text-gray-900 font-medium truncate mb-2",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["\uD83D\uDC41️ ",e.views.toLocaleString()]}),(0,r.jsx)("span",{children:(e=>{try{return new Date(e).toLocaleDateString("zh-TW",{year:"numeric",month:"2-digit",day:"2-digit"})}catch{return e}})(e.updated_at)})]})]}),(0,r.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),n]}),d=(0,i.A)("block border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",e.is_vip&&"border-yellow-200 bg-yellow-50/30",l);return s?(0,r.jsx)("a",{href:s,className:d,onClick:t,children:a}):t?(0,r.jsx)("button",{type:"button",className:(0,i.A)(d,"w-full text-left"),onClick:t,children:a}):(0,r.jsx)("div",{className:d,children:a})};d.displayName="ChapterCard";var o=t(592);function c(){let[e,s]=(0,l.useState)(!1),{user:t,isAuthenticated:i,login:c,logout:x}=(0,o.useAuth)(),{theme:m,fontSize:h,updateSettings:p}=(0,o.useSettings)(),u={id:"1",title:"測試小說標題",author:"測試作者",description:"這是一個測試小說的描述，用來驗證 NovelCard 組件的顯示效果。",cover:"/default-cover.jpg",status:"ongoing",views:12345,favorites:678,category:{id:1,name:"玄幻"}},j=async()=>{s(!0);try{await c({username:"testuser",password:"password"})}catch(e){console.error("Login test failed:",e)}finally{s(!1)}},g=e=>{p({theme:e})},b=e=>{p({fontSize:e})};return(0,r.jsxs)("div",{className:"space-y-12",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"UI 組件測試頁面"}),(0,r.jsx)("p",{className:"text-gray-600",children:"驗證 @novelwebsite/ui 套件中所有組件的功能和樣式"})]}),(0,r.jsxs)("section",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"LoadingSpinner 組件"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"小尺寸"}),(0,r.jsx)(n,{size:"sm",color:"primary",text:"載入中..."})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"中等尺寸"}),(0,r.jsx)(n,{size:"md",color:"secondary",text:"處理中..."})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"大尺寸"}),(0,r.jsx)(n,{size:"lg",color:"white",text:"請稍候...",className:"bg-blue-600 p-4 rounded"})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("button",{onClick:()=>s(!e),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:e?"停止載入":"開始載入"}),e&&(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(n,{text:"動態載入測試..."})})]})]}),(0,r.jsxs)("section",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"NovelCard 組件"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"連結模式"}),(0,r.jsx)(a,{novel:u,href:"/novels/test-novel-1"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"點擊模式"}),(0,r.jsx)(a,{novel:{...u,status:"completed"},onClick:()=>alert("NovelCard 被點擊了！")})]})]})]}),(0,r.jsxs)("section",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"ChapterCard 組件"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"普通章節"}),(0,r.jsx)(d,{chapter:{id:"1",title:"第一章：開始的地方",chapter_number:1,updated_at:"2025-01-01T00:00:00Z",views:5432,is_vip:!1},href:"/novels/test-novel/chapters/1"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"VIP 章節"}),(0,r.jsx)(d,{chapter:{id:"2",title:"第二章：VIP 章節",chapter_number:2,updated_at:"2025-01-02T00:00:00Z",views:3210,is_vip:!0},onClick:()=>alert("VIP 章節被點擊了！")})]})]})]}),(0,r.jsxs)("section",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Context API 測試"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"認證狀態"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"登入狀態："}),(0,r.jsx)("span",{className:i?"text-green-600":"text-red-600",children:i?"已登入":"未登入"})]}),t&&(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"用戶："})," ",t.username," (",t.email,")"]}),(0,r.jsx)("div",{className:"flex gap-2",children:i?(0,r.jsx)("button",{onClick:()=>{x()},className:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"登出"}):(0,r.jsx)("button",{onClick:j,disabled:e,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50",children:e?"登入中...":"測試登入"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-3",children:"設定管理"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"當前主題："})," ",m]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"字體大小："})," ",h]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"主題："}),(0,r.jsxs)("select",{value:m,onChange:e=>g(e.target.value),className:"border border-gray-300 rounded px-3 py-1",children:[(0,r.jsx)("option",{value:"light",children:"淺色"}),(0,r.jsx)("option",{value:"dark",children:"深色"}),(0,r.jsx)("option",{value:"sepia",children:"護眼"}),(0,r.jsx)("option",{value:"night",children:"夜間"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"字體大小："}),(0,r.jsxs)("select",{value:h,onChange:e=>b(e.target.value),className:"border border-gray-300 rounded px-3 py-1",children:[(0,r.jsx)("option",{value:"sm",children:"小"}),(0,r.jsx)("option",{value:"md",children:"中"}),(0,r.jsx)("option",{value:"lg",children:"大"}),(0,r.jsx)("option",{value:"xl",children:"特大"})]})]})]})]})]})]})]}),(0,r.jsxs)("section",{className:"bg-green-50 rounded-lg p-6 border border-green-200",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-green-900 mb-4",children:"✅ 測試狀態"}),(0,r.jsxs)("ul",{className:"space-y-2 text-green-800",children:[(0,r.jsx)("li",{children:"✅ LoadingSpinner 組件正常渲染"}),(0,r.jsx)("li",{children:"✅ NovelCard 組件正常渲染"}),(0,r.jsx)("li",{children:"✅ ChapterCard 組件正常渲染"}),(0,r.jsx)("li",{children:"✅ AuthContext 正常工作"}),(0,r.jsx)("li",{children:"✅ SettingsContext 正常工作"}),(0,r.jsx)("li",{children:"✅ SSR/CSR Hydration 無錯誤"})]})]})]})}},2064:(e,s,t)=>{Promise.resolve().then(t.bind(t,6937))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5616:(e,s,t)=>{Promise.resolve().then(t.bind(t,1268))},6937:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(950).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/ui-test/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/ui-test/page.tsx","default")},7100:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(5406),l=t(1049),i=t(9073),n=t.n(i),a=t(430),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(s,d);let o={children:["",{children:["ui-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6937)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/ui-test/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/ui-test/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/ui-test/page",pathname:"/ui-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[67,213,49],()=>t(7100));module.exports=r})();