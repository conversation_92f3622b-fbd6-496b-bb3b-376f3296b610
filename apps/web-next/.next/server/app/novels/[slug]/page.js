(()=>{var e={};e.id=932,e.ids=[932],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1876:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(5406),n=s(1049),a=s(9073),o=s.n(a),i=s(430),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d={children:["",{children:["novels",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6337)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/page.tsx"]}]},{error:[()=>Promise.resolve().then(s.bind(s,4596)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,7602)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,122)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/novels/[slug]/page",pathname:"/novels/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2933:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9751,23)),Promise.resolve().then(s.bind(s,673)),Promise.resolve().then(s.bind(s,592))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6337:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,generateMetadata:()=>p,generateStaticParams:()=>c,revalidate:()=>d});var r=s(1862),n=s(7909),a=s(2309),o=s.n(a),i=s(6497),l=s(2981);let d=60;async function c(){try{return(await i.WN.getList({limit:50})).results.map(e=>({slug:(0,i.z9)(e.title,Number(e.id))}))}catch(e){return console.error("Error generating static params:",e),[]}}async function p({params:e}){let{slug:t}=await e;try{let e=await i.WN.getDetailBySlug(t);if(!e)return{title:"小說未找到",description:"您查找的小說不存在或已被移除"};return{title:`${e.title} - ${e.author} | 小說閱讀`,description:e.description||`${e.author} 的作品《${e.title}》`,openGraph:{title:e.title,description:e.description||`${e.author} 的作品`,type:"article",authors:[e.author],images:e.cover?[{url:e.cover,alt:e.title}]:[]},twitter:{card:"summary_large_image",title:e.title,description:e.description||`${e.author} 的作品`}}}catch(e){return console.error("Error generating metadata:",e),{title:"小說詳情",description:"小說閱讀平台"}}}async function x(e){try{return await i.WN.getDetailBySlug(e)}catch(e){return console.error("Error fetching novel:",e),null}}async function u({params:e}){let{slug:t}=await e,s=await x(t);return s||(0,n.notFound)(),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-4",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-6",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("img",{src:s.cover||"/default-cover.jpg",alt:s.title,className:"w-48 h-64 object-cover rounded-lg shadow-md"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:s.title}),(0,r.jsxs)("div",{className:"space-y-2 text-gray-600 mb-4",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-semibold",children:"作者："}),s.author]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-semibold",children:"狀態："}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm ${"completed"===s.status?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:"completed"===s.status?"已完結":"連載中"})]}),s.category&&(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-semibold",children:"分類："}),s.category.name]}),(0,r.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,r.jsxs)("span",{children:["\uD83D\uDC41️ ",s.views.toLocaleString()," 次觀看"]}),(0,r.jsxs)("span",{children:["❤️ ",s.favorites.toLocaleString()," 收藏"]})]})]}),(0,r.jsxs)("div",{className:"flex gap-3 mb-6",children:[(0,r.jsx)("button",{className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"開始閱讀"}),(0,r.jsx)("button",{className:"border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors",children:"加入收藏"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-3",children:"作品簡介"}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:s.description||"暫無簡介"})]})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"章節目錄"}),s.chapters&&s.chapters.length>0?(0,r.jsxs)("div",{className:"grid gap-2",children:[s.chapters.slice(0,20).map(e=>(0,r.jsx)(l.iq,{chapter:e,href:`/novels/${t}/chapters/${e.id}`},e.id)),s.chapters.length>20&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-800",children:"載入更多章節..."})})]}):(0,r.jsx)("div",{className:"text-center py-12 text-gray-500",children:(0,r.jsx)("p",{children:"暫無章節內容"})})]})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(o(),{href:"/",className:"inline-flex items-center text-blue-600 hover:text-blue-800",children:"← 返回首頁"})})]})}},9013:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2309,23)),Promise.resolve().then(s.bind(s,4863)),Promise.resolve().then(s.bind(s,7793))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[67,213,751,49,463],()=>s(1876));module.exports=r})();