(()=>{var e={};e.id=739,e.ids=[739],e.modules={436:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(9572),a=r(9789);let n={baseURL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api/v1",timeout:3e4,defaultRevalidate:60,retries:3,retryDelay:1e3};class l extends Error{constructor(e,t,r){super(e),this.status=t,this.data=r,this.name="APIClientError"}}function i(e,t){let r=e.replace(/[：！？。，、；""''（）【】《》〈〉]/g,"").replace(/[^\u4e00-\u9fa5\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+|-+$/g,"").trim();return t?`${r}-${t}`:r}function o(e){let t=e.match(/-(\d+)$/);return t?parseInt(t[1],10):null}async function c(e,t=n.retries,r=n.retryDelay){let s=null;for(let a=0;a<=t;a++)try{return await e()}catch(n){if(s=n,a===t||n instanceof l&&n.status>=400&&n.status<500)break;let e=r*Math.pow(2,a);await function(e){return new Promise(t=>setTimeout(t,e))}(e)}throw s||Error("Unknown error in retry mechanism")}async function u(e,t={}){let{revalidate:r=n.defaultRevalidate,tags:s=[],enableRetry:a=!0,...i}=t,o=`${n.baseURL}${e}`,d=async()=>{let e=await fetch(o,{...i,next:{revalidate:r,tags:s},headers:{"Content-Type":"application/json",...i.headers}});if(!e.ok){let t=await e.json().catch(()=>({}));throw new l(`API request failed: ${e.status} ${e.statusText}`,e.status,t)}return await e.json()};try{if(a)return await c(d);return await d()}catch(e){if(e instanceof l)throw e;throw new l(`Network error: ${e instanceof Error?e.message:"Unknown error"}`,0)}}let d={async getList(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,String(r))});let r=t.toString();return u(`/novels/${r?`?${r}`:""}`,{tags:["novels","novels-list"]})},getDetail:async e=>u(`/novels/${e}/`,{tags:["novels",`novel-${e}`]}),async getDetailBySlug(e){let t=o(e);if(t)try{return await this.getDetail(t)}catch(e){if(e instanceof l&&404===e.status)return null;throw e}if(/^\d+$/.test(e))try{return await this.getDetail(parseInt(e,10))}catch(e){if(e instanceof l&&404===e.status)return null;throw e}try{return(await this.getList({search:e.replace(/-/g," ")})).results.find(t=>i(t.title,Number(t.id))===e)||null}catch(e){return console.error("Error searching novel by slug:",e),null}},async getChapters(e,t={}){let r=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&r.append(e,String(t))});let s=r.toString();return u(`/novels/${e}/chapters/${s?`?${s}`:""}`,{tags:["chapters",`novel-${e}-chapters`]})},async getNovelIdBySlug(e){let t=o(e);if(t)try{return await this.getDetail(t),t}catch(e){if(e instanceof l&&404===e.status)return null;throw e}if(/^\d+$/.test(e)){let t=parseInt(e,10);try{return await this.getDetail(t),t}catch(e){if(e instanceof l&&404===e.status)return null;throw e}}try{let t=(await this.getList({search:e.replace(/-/g," "),limit:50})).results.find(t=>i(t.title,Number(t.id))===e);return t?Number(t.id):null}catch(e){return console.error("Error searching novel by slug:",e),null}}},p={check:async()=>u("/health/",{revalidate:0})};function h(){let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)(!1),l=(e,r,s,a,n)=>{t(t=>[...t,{name:e,success:r,data:s,error:a,duration:n}])},c=async()=>{for(let{name:e,test:r}of(n(!0),t([]),[{name:"健康檢查",test:async()=>{let e=Date.now();return{result:await p.check(),duration:Date.now()-e}}},{name:"小說列表 API",test:async()=>{let e=Date.now();return{result:await d.getList({limit:5}),duration:Date.now()-e}}},{name:"Slug 生成測試",test:async()=>{let e=Date.now();return{result:[{title:"瘟仙",id:1,expected:"瘟仙-1"},{title:"修真：從撿垃圾開始！",id:123,expected:"修真從撿垃圾開始-123"},{title:"我的 修仙 人生...",id:456,expected:"我的-修仙-人生-456"}].map(({title:e,id:t,expected:r})=>{let s=i(e,t),a=o(s);return{title:e,id:t,generated:s,expected:r,extractedId:a,slugMatch:s===r,idMatch:a===t}}),duration:Date.now()-e}}},{name:"小說詳情 API (如果有數據)",test:async()=>{let e=Date.now();try{let t=await d.getList({limit:1});if(t.results.length>0){let r=t.results[0],s=await d.getDetail(Number(r.id)),a=Date.now()-e;return{result:s,duration:a}}{let t=Date.now()-e;return{result:{message:"沒有小說數據可測試"},duration:t}}}catch(t){throw{error:t,duration:Date.now()-e}}}},{name:"Slug 查詢測試 (如果有數據)",test:async()=>{let e=Date.now();try{let t=await d.getList({limit:1});if(t.results.length>0){let r=t.results[0],s=i(r.title,Number(r.id)),a=await d.getDetailBySlug(s),n=Date.now()-e;return{result:{slug:s,novel:a},duration:n}}{let t=Date.now()-e;return{result:{message:"沒有小說數據可測試"},duration:t}}}catch(t){throw{error:t,duration:Date.now()-e}}}}]))try{let{result:t,duration:s}=await r();l(e,!0,t,null,s)}catch(t){l(e,!1,null,t,t.duration)}n(!1)};return(0,s.jsxs)("div",{className:"max-w-6xl mx-auto p-6",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"API 客戶端測試 v2"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"測試新的 @novelwebsite/api 套件功能，包含重試機制、Slug 處理和導航功能"}),(0,s.jsx)("button",{onClick:c,disabled:r,className:`px-6 py-3 rounded-lg font-semibold ${r?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:r?"測試進行中...":"開始測試"})]}),e.length>0&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"測試結果"}),e.map((e,t)=>(0,s.jsxs)("div",{className:`p-4 rounded-lg border ${e.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("h3",{className:"font-semibold",children:[e.success?"✅":"❌"," ",e.name]}),e.duration&&(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[e.duration,"ms"]})]}),e.success&&e.data&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("pre",{className:"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-64",children:JSON.stringify(e.data,null,2)})}),!e.success&&e.error&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("div",{className:"text-red-700 font-medium",children:"錯誤訊息:"}),(0,s.jsx)("pre",{className:"bg-red-100 p-3 rounded text-sm overflow-auto max-h-64 text-red-800",children:JSON.stringify(e.error,null,2)})]})]},t))]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"測試說明"}),(0,s.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• 確保 Django backend 在 http://localhost:8000 運行"}),(0,s.jsx)("li",{children:"• 確保有測試數據 (至少 1-2 本小說)"}),(0,s.jsx)("li",{children:"• 測試包含 API 連通性、Slug 處理、錯誤處理等功能"}),(0,s.jsx)("li",{children:"• 查看瀏覽器開發者工具的 Network 標籤以檢查實際請求"})]})]})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4434:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(950).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx","default")},5347:(e,t,r)=>{Promise.resolve().then(r.bind(r,436))},6027:(e,t,r)=>{Promise.resolve().then(r.bind(r,4434))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9968:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=r(5406),a=r(1049),n=r(9073),l=r.n(n),i=r(430),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let c={children:["",{children:["api-test-v2",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4434)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/api-test-v2/page",pathname:"/api-test-v2",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[67,213,49],()=>r(9968));module.exports=s})();