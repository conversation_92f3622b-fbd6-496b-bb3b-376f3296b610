(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2309:(e,t,s)=>{let{createProxy:r}=s(9369);e.exports=r("/Users/<USER>/Documents/project/NovelWebsite/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_babel-plugin-macros@3.1.0_react-dom@18_4jqqjup23g4vegjq5p5xeio7v4/node_modules/next/dist/client/app-dir/link.js")},2933:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9751,23)),Promise.resolve().then(s.bind(s,673)),Promise.resolve().then(s.bind(s,592))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6497:(e,t,s)=>{"use strict";s.d(t,{jx:()=>d,z9:()=>a,WN:()=>c});let r={baseURL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api/v1",timeout:3e4,defaultRevalidate:60,retries:3,retryDelay:1e3};class n extends Error{constructor(e,t,s){super(e),this.status=t,this.data=s,this.name="APIClientError"}}function a(e,t){let s=e.replace(/[：！？。，、；""''（）【】《》〈〉]/g,"").replace(/[^\u4e00-\u9fa5\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+|-+$/g,"").trim();return t?`${s}-${t}`:s}function l(e){let t=e.match(/-(\d+)$/);return t?parseInt(t[1],10):null}async function i(e,t=r.retries,s=r.retryDelay){let a=null;for(let r=0;r<=t;r++)try{return await e()}catch(l){if(a=l,r===t||l instanceof n&&l.status>=400&&l.status<500)break;let e=s*Math.pow(2,r);await function(e){return new Promise(t=>setTimeout(t,e))}(e)}throw a||Error("Unknown error in retry mechanism")}async function o(e,t={}){let{revalidate:s=r.defaultRevalidate,tags:a=[],enableRetry:l=!0,...c}=t,d=`${r.baseURL}${e}`,h=async()=>{let e=await fetch(d,{...c,next:{revalidate:s,tags:a},headers:{"Content-Type":"application/json",...c.headers}});if(!e.ok){let t=await e.json().catch(()=>({}));throw new n(`API request failed: ${e.status} ${e.statusText}`,e.status,t)}return await e.json()};try{if(l)return await i(h);return await h()}catch(e){if(e instanceof n)throw e;throw new n(`Network error: ${e instanceof Error?e.message:"Unknown error"}`,0)}}let c={async getList(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,s])=>{null!=s&&t.append(e,String(s))});let s=t.toString();return o(`/novels/${s?`?${s}`:""}`,{tags:["novels","novels-list"]})},getDetail:async e=>o(`/novels/${e}/`,{tags:["novels",`novel-${e}`]}),async getDetailBySlug(e){let t=l(e);if(t)try{return await this.getDetail(t)}catch(e){if(e instanceof n&&404===e.status)return null;throw e}if(/^\d+$/.test(e))try{return await this.getDetail(parseInt(e,10))}catch(e){if(e instanceof n&&404===e.status)return null;throw e}try{return(await this.getList({search:e.replace(/-/g," ")})).results.find(t=>a(t.title,Number(t.id))===e)||null}catch(e){return console.error("Error searching novel by slug:",e),null}},async getChapters(e,t={}){let s=new URLSearchParams;Object.entries(t).forEach(([e,t])=>{null!=t&&s.append(e,String(t))});let r=s.toString();return o(`/novels/${e}/chapters/${r?`?${r}`:""}`,{tags:["chapters",`novel-${e}-chapters`]})},async getNovelIdBySlug(e){let t=l(e);if(t)try{return await this.getDetail(t),t}catch(e){if(e instanceof n&&404===e.status)return null;throw e}if(/^\d+$/.test(e)){let t=parseInt(e,10);try{return await this.getDetail(t),t}catch(e){if(e instanceof n&&404===e.status)return null;throw e}}try{let t=(await this.getList({search:e.replace(/-/g," "),limit:50})).results.find(t=>a(t.title,Number(t.id))===e);return t?Number(t.id):null}catch(e){return console.error("Error searching novel by slug:",e),null}}},d={getContent:async e=>o(`/chapters/${e}/`,{tags:["chapters",`chapter-${e}`]}),async getChapterNavigation(e){let t=await this.getContent(e),s=await c.getDetail(t.novel),r=(await c.getChapters(t.novel,{limit:1e3,ordering:"chapter_number"})).results,n=r.findIndex(t=>Number(t.id)===e);return{current:t,previous:n>0?r[n-1]:null,next:n<r.length-1?r[n+1]:null,novel:s}}}},8952:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(5406),n=s(1049),a=s(9073),l=s.n(a),i=s(430),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9291)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,8467,23)),"next/dist/client/components/unauthorized-error"]}],d=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9013:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2309,23)),Promise.resolve().then(s.bind(s,4863)),Promise.resolve().then(s.bind(s,7793))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9291:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h,metadata:()=>o,revalidate:()=>c});var r=s(1862),n=s(2309),a=s.n(n),l=s(6497),i=s(2981);let o={title:"小說閱讀平台 | Next.js 15 App Router",description:"現代化的小說閱讀平台，提供豐富的小說資源和優質的閱讀體驗",openGraph:{title:"小說閱讀平台",description:"現代化的小說閱讀平台，提供豐富的小說資源和優質的閱讀體驗",type:"website"}},c=60;async function d(){try{return(await l.WN.getList({limit:12})).results}catch(e){return console.error("Error fetching novels:",e),[]}}async function h(){let e=await d();return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("section",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"小說閱讀平台"}),(0,r.jsx)("p",{className:"text-xl mb-6",children:"現代化的小說閱讀體驗，支援 SSR/SSG，提供優質的閱讀環境"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(a(),{href:"#latest-novels",className:"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"開始閱讀"}),(0,r.jsx)(a(),{href:"/about",className:"border border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors",children:"了解更多"})]})]}),(0,r.jsxs)("section",{id:"latest-novels",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"最新小說"}),(0,r.jsx)(a(),{href:"/novels",className:"text-blue-600 hover:text-blue-800 font-medium",children:"查看全部 →"})]}),e.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>{let t=(0,l.z9)(e.title,Number(e.id));return(0,r.jsx)(i.fi,{novel:e,href:`/novels/${t}`},e.id)})}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-8 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"暫無小說內容"}),(0,r.jsx)("p",{className:"text-gray-600",children:"請確保 Django backend 正在運行並包含測試數據"})]})]}),(0,r.jsxs)("section",{className:"grid md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:"\uD83D\uDE80 App Router"}),(0,r.jsx)("p",{className:"text-gray-600",children:"使用 Next.js 15 最新的 App Router 架構，提供更好的開發體驗和性能"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:"⚡ 伺服器端渲染"}),(0,r.jsx)("p",{className:"text-gray-600",children:"支援 SSR 和 SSG，提升 SEO 效果和頁面載入速度"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:"\uD83D\uDD04 ISR 快取"}),(0,r.jsx)("p",{className:"text-gray-600",children:"增量靜態重新生成，確保內容即時更新且性能優異"})]})]}),(0,r.jsxs)("section",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"系統狀態"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-700",children:"應用資訊"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Next.js 版本: 15.1.3"}),(0,r.jsx)("li",{children:"• React 版本: 18.2.0"}),(0,r.jsx)("li",{children:"• TypeScript 支援: ✅"}),(0,r.jsx)("li",{children:"• Tailwind CSS: ✅"}),(0,r.jsx)("li",{children:"• App Router: ✅"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-700",children:"Monorepo 整合"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• pnpm workspace: ✅"}),(0,r.jsx)("li",{children:"• Turborepo 支援: ✅"}),(0,r.jsx)("li",{children:"• 共享套件: 準備中"}),(0,r.jsx)("li",{children:"• CI/CD 整合: 準備中"}),(0,r.jsx)("li",{children:"• API 代理: 準備中"})]})]})]})]}),(0,r.jsxs)("section",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-3 text-blue-900",children:"開發資訊"}),(0,r.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("p",{children:"• 當前端口: 3001 (Next.js 15)"}),(0,r.jsx)("p",{children:"• CRA 應用端口: 3000"}),(0,r.jsx)("p",{children:"• Django 後端端口: 8000"}),(0,r.jsxs)("p",{children:["• 可使用 ",(0,r.jsx)("code",{className:"bg-blue-100 px-1 rounded",children:"turbo dev"})," 同時啟動所有服務"]})]})]})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[67,213,751,49],()=>s(8952));module.exports=r})();