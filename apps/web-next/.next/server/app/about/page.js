(()=>{var e={};e.id=220,e.ids=[220],e.modules={89:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d,metadata:()=>i});var t=r(1862),l=r(2309),n=r.n(l);let i={title:"關於我們",description:"了解 Next.js 15 App Router 版本的技術特色"};function d(){return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"關於 Next.js 15 版本"}),(0,t.jsx)(n(),{href:"/",className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← 返回首頁"})]}),(0,t.jsxs)("div",{className:"prose max-w-none",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4 text-white",children:"技術架構升級"}),(0,t.jsx)("p",{className:"text-blue-100 text-lg",children:"這是使用 Next.js 15 App Router 重新構建的版本，與現有 CRA 應用並行運行， 展示現代化前端架構的強大功能。"})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-2xl font-semibold text-gray-900",children:"\uD83D\uDE80 核心特色"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-white p-4 rounded-lg border",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"App Router 架構"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"使用 Next.js 15 最新的 App Router，提供更好的開發體驗、 更強的類型安全和更靈活的佈局系統。"})]}),(0,t.jsxs)("div",{className:"bg-white p-4 rounded-lg border",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"伺服器端渲染 (SSR)"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"支援 SSR 和 SSG，大幅提升 SEO 效果和首次載入速度， 解決 CRA 版本的 SEO 限制。"})]}),(0,t.jsxs)("div",{className:"bg-white p-4 rounded-lg border",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Monorepo 整合"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"完美整合 pnpm workspace 和 Turborepo， 支援共享套件和智能快取機制。"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-2xl font-semibold text-gray-900",children:"⚡ 性能優勢"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"自動程式碼分割"}),(0,t.jsx)("p",{className:"text-green-800 text-sm",children:"Next.js 自動進行程式碼分割，只載入當前頁面需要的 JavaScript， 提升頁面載入速度。"})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"圖片優化"}),(0,t.jsx)("p",{className:"text-green-800 text-sm",children:"內建圖片優化功能，自動轉換為 WebP 格式， 支援響應式圖片和懶載入。"})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,t.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"智能預載入"}),(0,t.jsx)("p",{className:"text-green-800 text-sm",children:"自動預載入可見連結的頁面，提供近乎即時的頁面切換體驗。"})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 p-6 rounded-lg border border-yellow-200 mt-8",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-yellow-900 mb-3",children:"\uD83D\uDD04 並行運行策略"}),(0,t.jsxs)("div",{className:"text-yellow-800 space-y-2",children:[(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"CRA 版本"}),": 運行在端口 3000，保持現有功能"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"Next.js 版本"}),": 運行在端口 3001，展示新架構"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"Django 後端"}),": 運行在端口 8000，兩個前端共享"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"漸進式遷移"}),": 可以逐步將功能從 CRA 遷移到 Next.js"]})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border border-blue-200",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-blue-900 mb-3",children:"\uD83D\uDEE0️ 開發工具"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-blue-800",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"前端技術棧"}),(0,t.jsxs)("ul",{className:"space-y-1",children:[(0,t.jsx)("li",{children:"• Next.js 15.3.4"}),(0,t.jsx)("li",{children:"• React 18.3.1"}),(0,t.jsx)("li",{children:"• TypeScript 5.8.3"}),(0,t.jsx)("li",{children:"• Tailwind CSS 3.4.17"}),(0,t.jsx)("li",{children:"• Material-UI 6.4.12"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"開發工具"}),(0,t.jsxs)("ul",{className:"space-y-1",children:[(0,t.jsx)("li",{children:"• pnpm workspace"}),(0,t.jsx)("li",{children:"• Turborepo 快取"}),(0,t.jsx)("li",{children:"• ESLint + Prettier"}),(0,t.jsx)("li",{children:"• 熱重載開發"}),(0,t.jsx)("li",{children:"• 自動類型檢查"})]})]})]})]})]})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2309:(e,s,r)=>{let{createProxy:t}=r(9369);e.exports=t("/Users/<USER>/Documents/project/NovelWebsite/node_modules/.pnpm/next@15.3.4_@babel+core@7.27.4_@playwright+test@1.53.1_babel-plugin-macros@3.1.0_react-dom@18_4jqqjup23g4vegjq5p5xeio7v4/node_modules/next/dist/client/app-dir/link.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3961:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,9751,23))},4457:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,2309,23))},5048:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>o});var t=r(5406),l=r(1049),n=r(9073),i=r.n(n),d=r(430),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);r.d(s,a);let o={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,89)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4276)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,6448)),"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5559,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,7402,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,8467,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[67,213,751,49],()=>r(5048));module.exports=t})();