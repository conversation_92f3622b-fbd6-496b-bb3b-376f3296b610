[{"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx": "1", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts": "2", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts": "3", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx": "4", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx": "5", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx": "6", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx": "7", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/chapters/[chapterId]/loading.tsx": "8", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/chapters/[chapterId]/page.tsx": "9", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/error.tsx": "10", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/loading.tsx": "11", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/not-found.tsx": "12", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/page.tsx": "13", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx": "14", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx": "15", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts": "16", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/types/api.ts": "17", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/ui-test/page.tsx": "18"}, {"size": 5654, "mtime": 1751263698032, "results": "19", "hashOfConfig": "20"}, {"size": 6599, "mtime": 1751263698033, "results": "21", "hashOfConfig": "20"}, {"size": 4444, "mtime": 1751263698033, "results": "22", "hashOfConfig": "20"}, {"size": 7161, "mtime": 1751263698033, "results": "23", "hashOfConfig": "20"}, {"size": 7246, "mtime": 1751266353173, "results": "24", "hashOfConfig": "20"}, {"size": 2981, "mtime": 1751267610774, "results": "25", "hashOfConfig": "20"}, {"size": 2723, "mtime": 1751266375719, "results": "26", "hashOfConfig": "20"}, {"size": 2743, "mtime": 1751265623942, "results": "27", "hashOfConfig": "20"}, {"size": 7476, "mtime": 1751266265083, "results": "28", "hashOfConfig": "20"}, {"size": 3131, "mtime": 1751265552033, "results": "29", "hashOfConfig": "20"}, {"size": 2843, "mtime": 1751265534338, "results": "30", "hashOfConfig": "20"}, {"size": 3671, "mtime": 1751265571842, "results": "31", "hashOfConfig": "20"}, {"size": 6177, "mtime": 1751267688945, "results": "32", "hashOfConfig": "20"}, {"size": 2444, "mtime": 1751263698035, "results": "33", "hashOfConfig": "20"}, {"size": 6570, "mtime": 1751267659966, "results": "34", "hashOfConfig": "20"}, {"size": 8644, "mtime": 1751263698037, "results": "35", "hashOfConfig": "20"}, {"size": 2310, "mtime": 1751263698037, "results": "36", "hashOfConfig": "20"}, {"size": 9266, "mtime": 1751267735220, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nmrpg4", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts", ["92"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx", ["93", "94", "95", "96", "97", "98"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/chapters/[chapterId]/loading.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/chapters/[chapterId]/page.tsx", ["99", "100"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/error.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/loading.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/not-found.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts", ["101", "102"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/types/api.ts", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/ui-test/page.tsx", ["103", "104"], [], {"ruleId": "105", "severity": 1, "message": "106", "line": 251, "column": 31, "nodeType": null, "messageId": "107", "endLine": 251, "endColumn": 39}, {"ruleId": "105", "severity": 1, "message": "108", "line": 4, "column": 21, "nodeType": null, "messageId": "107", "endLine": 4, "endColumn": 32}, {"ruleId": "109", "severity": 1, "message": "110", "line": 9, "column": 10, "nodeType": "111", "messageId": "112", "endLine": 9, "endColumn": 13, "suggestions": "113"}, {"ruleId": "109", "severity": 1, "message": "110", "line": 10, "column": 11, "nodeType": "111", "messageId": "112", "endLine": 10, "endColumn": 14, "suggestions": "114"}, {"ruleId": "109", "severity": 1, "message": "110", "line": 18, "column": 61, "nodeType": "111", "messageId": "112", "endLine": 18, "endColumn": 64, "suggestions": "115"}, {"ruleId": "109", "severity": 1, "message": "110", "line": 18, "column": 74, "nodeType": "111", "messageId": "112", "endLine": 18, "endColumn": 77, "suggestions": "116"}, {"ruleId": "109", "severity": 1, "message": "110", "line": 123, "column": 23, "nodeType": "111", "messageId": "112", "endLine": 123, "endColumn": 26, "suggestions": "117"}, {"ruleId": "105", "severity": 1, "message": "118", "line": 15, "column": 11, "nodeType": null, "messageId": "107", "endLine": 15, "endColumn": 15}, {"ruleId": "105", "severity": 1, "message": "118", "line": 51, "column": 11, "nodeType": null, "messageId": "107", "endLine": 51, "endColumn": 15}, {"ruleId": "105", "severity": 1, "message": "119", "line": 9, "column": 3, "nodeType": null, "messageId": "107", "endLine": 9, "endColumn": 14}, {"ruleId": "105", "severity": 1, "message": "120", "line": 10, "column": 20, "nodeType": null, "messageId": "107", "endLine": 10, "endColumn": 38}, {"ruleId": "109", "severity": 1, "message": "110", "line": 242, "column": 74, "nodeType": "111", "messageId": "112", "endLine": 242, "endColumn": 77, "suggestions": "121"}, {"ruleId": "109", "severity": 1, "message": "110", "line": 256, "column": 77, "nodeType": "111", "messageId": "112", "endLine": 256, "endColumn": 80, "suggestions": "122"}, "@typescript-eslint/no-unused-vars", "'_request' is defined but never used.", "unusedVar", "'chaptersAPI' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["123", "124"], ["125", "126"], ["127", "128"], ["129", "130"], ["131", "132"], "'slug' is assigned a value but never used.", "'ApiResponse' is defined but never used.", "'TypedRequestConfig' is defined but never used.", ["133", "134"], ["135", "136"], {"messageId": "137", "fix": "138", "desc": "139"}, {"messageId": "140", "fix": "141", "desc": "142"}, {"messageId": "137", "fix": "143", "desc": "139"}, {"messageId": "140", "fix": "144", "desc": "142"}, {"messageId": "137", "fix": "145", "desc": "139"}, {"messageId": "140", "fix": "146", "desc": "142"}, {"messageId": "137", "fix": "147", "desc": "139"}, {"messageId": "140", "fix": "148", "desc": "142"}, {"messageId": "137", "fix": "149", "desc": "139"}, {"messageId": "140", "fix": "150", "desc": "142"}, {"messageId": "137", "fix": "151", "desc": "139"}, {"messageId": "140", "fix": "152", "desc": "142"}, {"messageId": "137", "fix": "153", "desc": "139"}, {"messageId": "140", "fix": "154", "desc": "142"}, "suggestUnknown", {"range": "155", "text": "156"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "157", "text": "158"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "159", "text": "156"}, {"range": "160", "text": "158"}, {"range": "161", "text": "156"}, {"range": "162", "text": "158"}, {"range": "163", "text": "156"}, {"range": "164", "text": "158"}, {"range": "165", "text": "156"}, {"range": "166", "text": "158"}, {"range": "167", "text": "156"}, {"range": "168", "text": "158"}, {"range": "169", "text": "156"}, {"range": "170", "text": "158"}, [217, 220], "unknown", [217, 220], "never", [231, 234], [231, 234], [470, 473], [470, 473], [483, 486], [483, 486], [3959, 3962], [3959, 3962], [7065, 7068], [7065, 7068], [7690, 7693], [7690, 7693]]