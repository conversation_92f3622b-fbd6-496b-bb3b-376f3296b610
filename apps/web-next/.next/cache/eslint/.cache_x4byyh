[{"/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx": "1", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts": "2", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts": "3", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx": "4", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx": "5", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx": "6", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx": "7", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/chapters/[chapterId]/loading.tsx": "8", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/chapters/[chapterId]/page.tsx": "9", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/error.tsx": "10", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/loading.tsx": "11", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/not-found.tsx": "12", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/page.tsx": "13", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx": "14", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx": "15", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts": "16", "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/types/api.ts": "17"}, {"size": 5654, "mtime": 1751263698032, "results": "18", "hashOfConfig": "19"}, {"size": 6599, "mtime": 1751263698033, "results": "20", "hashOfConfig": "19"}, {"size": 4444, "mtime": 1751263698033, "results": "21", "hashOfConfig": "19"}, {"size": 7161, "mtime": 1751263698033, "results": "22", "hashOfConfig": "19"}, {"size": 7246, "mtime": 1751266353173, "results": "23", "hashOfConfig": "19"}, {"size": 2460, "mtime": 1751263698034, "results": "24", "hashOfConfig": "19"}, {"size": 2723, "mtime": 1751266375719, "results": "25", "hashOfConfig": "19"}, {"size": 2743, "mtime": 1751265623942, "results": "26", "hashOfConfig": "19"}, {"size": 7476, "mtime": 1751266265083, "results": "27", "hashOfConfig": "19"}, {"size": 3131, "mtime": 1751265552033, "results": "28", "hashOfConfig": "19"}, {"size": 2843, "mtime": 1751265534338, "results": "29", "hashOfConfig": "19"}, {"size": 3671, "mtime": 1751265571842, "results": "30", "hashOfConfig": "19"}, {"size": 6657, "mtime": 1751265518371, "results": "31", "hashOfConfig": "19"}, {"size": 2444, "mtime": 1751263698035, "results": "32", "hashOfConfig": "19"}, {"size": 7812, "mtime": 1751266183250, "results": "33", "hashOfConfig": "19"}, {"size": 8644, "mtime": 1751263698037, "results": "34", "hashOfConfig": "19"}, {"size": 2310, "mtime": 1751263698037, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nmrpg4", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/about/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/proxy/[...path]/route.ts", ["87"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api/revalidate/route.ts", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/api-test-v2/page.tsx", ["88", "89", "90", "91", "92", "93"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/layout.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/loading.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/chapters/[chapterId]/loading.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/chapters/[chapterId]/page.tsx", ["94", "95"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/error.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/loading.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/not-found.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/[slug]/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/novels/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/app/page.tsx", [], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/api.ts", ["96", "97"], [], "/Users/<USER>/Documents/project/NovelWebsite/apps/web-next/lib/types/api.ts", [], [], {"ruleId": "98", "severity": 1, "message": "99", "line": 251, "column": 31, "nodeType": null, "messageId": "100", "endLine": 251, "endColumn": 39}, {"ruleId": "98", "severity": 1, "message": "101", "line": 4, "column": 21, "nodeType": null, "messageId": "100", "endLine": 4, "endColumn": 32}, {"ruleId": "102", "severity": 1, "message": "103", "line": 9, "column": 10, "nodeType": "104", "messageId": "105", "endLine": 9, "endColumn": 13, "suggestions": "106"}, {"ruleId": "102", "severity": 1, "message": "103", "line": 10, "column": 11, "nodeType": "104", "messageId": "105", "endLine": 10, "endColumn": 14, "suggestions": "107"}, {"ruleId": "102", "severity": 1, "message": "103", "line": 18, "column": 61, "nodeType": "104", "messageId": "105", "endLine": 18, "endColumn": 64, "suggestions": "108"}, {"ruleId": "102", "severity": 1, "message": "103", "line": 18, "column": 74, "nodeType": "104", "messageId": "105", "endLine": 18, "endColumn": 77, "suggestions": "109"}, {"ruleId": "102", "severity": 1, "message": "103", "line": 123, "column": 23, "nodeType": "104", "messageId": "105", "endLine": 123, "endColumn": 26, "suggestions": "110"}, {"ruleId": "98", "severity": 1, "message": "111", "line": 15, "column": 11, "nodeType": null, "messageId": "100", "endLine": 15, "endColumn": 15}, {"ruleId": "98", "severity": 1, "message": "111", "line": 51, "column": 11, "nodeType": null, "messageId": "100", "endLine": 51, "endColumn": 15}, {"ruleId": "98", "severity": 1, "message": "112", "line": 9, "column": 3, "nodeType": null, "messageId": "100", "endLine": 9, "endColumn": 14}, {"ruleId": "98", "severity": 1, "message": "113", "line": 10, "column": 20, "nodeType": null, "messageId": "100", "endLine": 10, "endColumn": 38}, "@typescript-eslint/no-unused-vars", "'_request' is defined but never used.", "unusedVar", "'chaptersAPI' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["114", "115"], ["116", "117"], ["118", "119"], ["120", "121"], ["122", "123"], "'slug' is assigned a value but never used.", "'ApiResponse' is defined but never used.", "'TypedRequestConfig' is defined but never used.", {"messageId": "124", "fix": "125", "desc": "126"}, {"messageId": "127", "fix": "128", "desc": "129"}, {"messageId": "124", "fix": "130", "desc": "126"}, {"messageId": "127", "fix": "131", "desc": "129"}, {"messageId": "124", "fix": "132", "desc": "126"}, {"messageId": "127", "fix": "133", "desc": "129"}, {"messageId": "124", "fix": "134", "desc": "126"}, {"messageId": "127", "fix": "135", "desc": "129"}, {"messageId": "124", "fix": "136", "desc": "126"}, {"messageId": "127", "fix": "137", "desc": "129"}, "suggestUnknown", {"range": "138", "text": "139"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "140", "text": "141"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "142", "text": "139"}, {"range": "143", "text": "141"}, {"range": "144", "text": "139"}, {"range": "145", "text": "141"}, {"range": "146", "text": "139"}, {"range": "147", "text": "141"}, {"range": "148", "text": "139"}, {"range": "149", "text": "141"}, [217, 220], "unknown", [217, 220], "never", [231, 234], [231, 234], [470, 473], [470, 473], [483, 486], [483, 486], [3959, 3962], [3959, 3962]]