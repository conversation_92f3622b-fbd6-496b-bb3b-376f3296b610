import { defineConfig, devices } from '@playwright/test'

/**
 * Playwright E2E 測試配置
 * 
 * 測試完整的用戶流程：首頁 → 小說詳情 → 章節閱讀
 */
export default defineConfig({
  testDir: './e2e',
  /* 並行運行測試 */
  fullyParallel: true,
  /* 在 CI 中失敗時不重試 */
  retries: process.env.CI ? 2 : 0,
  /* 在 CI 中選擇退出並行 */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: 'html',
  /* 共享設定 */
  use: {
    /* 基礎 URL */
    baseURL: 'http://localhost:3001',
    /* 收集失敗測試的追蹤 */
    trace: 'on-first-retry',
    /* 截圖設定 */
    screenshot: 'only-on-failure',
    /* 視頻設定 */
    video: 'retain-on-failure',
  },

  /* 配置專案 */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    /* 移動端測試 */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  /* 在測試開始前啟動開發伺服器 */
  webServer: {
    command: 'pnpm dev',
    url: 'http://localhost:3001',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },
})
