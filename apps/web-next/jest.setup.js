import "@testing-library/jest-dom";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return "/";
  },
}));

// Mock Next.js image
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props) => {
    return <img {...props} />;
  },
}));

// Mock Next.js link
jest.mock("next/link", () => ({
  __esModule: true,
  default: ({ children, href, ...props }) => {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    );
  },
}));

// Mock environment variables
process.env.NEXT_PUBLIC_API_URL = "http://localhost:8000/api/v1";
process.env.NEXT_PUBLIC_BASE_URL = "http://localhost:3001";
process.env.NEXT_PUBLIC_APP_NAME = "NovelWebsite Test";

// Mock fetch for API tests
global.fetch = jest.fn();

// Setup for testing library
beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks();

  // Reset fetch mock
  fetch.mockClear();
});

// Custom matchers
expect.extend({
  toBeInTheDocument: require("@testing-library/jest-dom/matchers")
    .toBeInTheDocument,
});

// Suppress console errors during tests (optional)
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === "string" &&
      args[0].includes("Warning: ReactDOM.render is no longer supported")
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
