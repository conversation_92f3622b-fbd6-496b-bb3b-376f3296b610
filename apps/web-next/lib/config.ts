/**
 * 應用程式配置
 * 集中管理環境相關的配置設定
 */

/**
 * 取得 API 基礎 URL
 * 根據不同環境返回對應的 API 端點
 *
 * 優先順序：
 * 1. NEXT_PUBLIC_API_URL 環境變數（.env.local, .env.test, .env.production）
 * 2. NODE_ENV === "test" 時的默認測試 URL
 * 3. 生產環境使用 Next.js API Proxy
 *
 * @example
 * // .env.local (開發環境直連後端)
 * NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
 *
 * // .env.test (測試環境)
 * NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
 *
 * // .env.production (生產環境使用代理)
 * # 不設定 NEXT_PUBLIC_API_URL，使用默認的 /api/proxy
 *
 * @returns API 基礎 URL
 */
export const getBaseURL = (): string => {
  // 優先使用環境變數設定
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL;
  }

  // 測試環境默認值（當 .env.test 未設定時的 fallback）
  if (process.env.NODE_ENV === "test") {
    return "http://localhost:8000/api/v1";
  }

  // 生產環境使用 Next.js API 代理
  // 優點：隱藏後端實際地址、統一 CORS 處理、可添加認證層
  // TODO: 未來支持多站點或 /api/v2 時可從這裡擴充
  return "/api/proxy";
};

/**
 * API 配置
 */
export const API_CONFIG = {
  baseURL: getBaseURL(),
  timeout: 30000, // 30 秒超時
  retries: 3, // 重試次數
} as const;

/**
 * 應用程式配置
 */
export const APP_CONFIG = {
  name: process.env.NEXT_PUBLIC_APP_NAME || "NovelWebsite",
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001",
  isDevelopment: process.env.NODE_ENV === "development",
  isProduction: process.env.NODE_ENV === "production",
  isTest: process.env.NODE_ENV === "test",
} as const;
