# Django 配置完整性檢查機制

> **Issue #184** - 實現 Django 配置完整性檢查機制

## 🎯 項目概述

Django 配置完整性檢查機制提供了全面的 Django 配置驗證和健康檢查功能，幫助開發者識別和修復配置問題，確保應用程序的安全性、性能和穩定性。

## ✨ 核心功能

### 📋 配置檢查類別

- **🔒 安全配置檢查**
  - HTTPS 強制和 HSTS 設置
  - Session 和 CSRF Cookie 安全
  - 密碼驗證器配置
  - 文件上傳安全限制

- **⚡ 性能配置檢查**
  - 數據庫連接池設置
  - 緩存配置優化
  - 中間件順序檢查
  - 模板快取配置

- **📁 媒體和靜態文件檢查**
  - MEDIA_ROOT 和 STATIC_ROOT 配置
  - 文件 URL 設置
  - 靜態文件目錄存在性檢查

- **📝 日誌配置檢查**
  - 處理器和格式化器配置
  - 日誌級別適當性
  - 生產環境文件日誌

- **🌐 API 配置檢查**
  - Django REST Framework 設置
  - CORS 配置安全性
  - 認證和權限檢查

- **🌍 國際化配置檢查**
  - 時區和語言設置
  - 本地化配置

### 🎯 問題分級系統

- **❌ 錯誤 (Errors)**: 必須修復的嚴重問題
- **⚠️ 警告 (Warnings)**: 建議修復的重要問題
- **💡 建議 (Suggestions)**: 可選的改進建議

### 🏥 健康評分

- **分數範圍**: 0-100 分
- **評分權重**: 錯誤 (×10) > 警告 (×5) > 建議 (×1)
- **優先問題**: 自動識別需要立即處理的安全相關問題

## 🚀 快速開始

### 安裝和設置

1. **將配置模塊添加到你的 Django 項目**:
   ```bash
   # 確保 config 模塊在你的 Python 路徑中
   # 或將其複製到你的項目目錄
   ```

2. **在 Django 設置中註冊** (可選):
   ```python
   # settings.py
   INSTALLED_APPS = [
       # ... 其他應用
       'config',
   ]
   ```

### 基本使用

```bash
# 執行完整配置檢查
python manage.py check_config

# 只顯示摘要信息
python manage.py check_config --summary-only

# 只顯示健康分數
python manage.py check_config --health-score-only

# JSON 格式輸出
python manage.py check_config --format=json

# 檢查特定類別
python manage.py check_config --category=security

# 輸出到文件
python manage.py check_config --output=config-report.txt

# 導出詳細報告
python manage.py check_config --export-report

# 在 CI/CD 中使用（發現錯誤時退出）
python manage.py check_config --fail-on-error
```

## 📖 詳細使用指南

### 命令行選項

| 選項 | 說明 | 默認值 |
|------|------|---------|
| `--format` | 輸出格式 (`text`, `json`) | `text` |
| `--output` | 輸出文件路徑 | 標準輸出 |
| `--summary-only` | 只顯示摘要信息 | `False` |
| `--health-score-only` | 只顯示健康分數 | `False` |
| `--category` | 檢查特定類別 | 全部類別 |
| `--fail-on-error` | 發現錯誤時退出 | `False` |
| `--export-report` | 導出詳細 JSON 報告 | `False` |
| `--verbose` | 顯示詳細過程信息 | `False` |

### 程序化使用

```python
from config.enhanced_validation import (
    validate_enhanced_configuration,
    get_configuration_report,
    EnhancedConfigValidator
)

# 執行配置檢查
result = validate_enhanced_configuration()

print(f"發現 {result.total_issues} 個問題")
print(f"錯誤: {len(result.errors)}")
print(f"警告: {len(result.warnings)}")
print(f"建議: {len(result.suggestions)}")

# 獲取格式化報告
report = get_configuration_report()
print(report)

# 使用驗證器類別
validator = EnhancedConfigValidator()
result = validator.validate_all()
health_score = validator.get_health_score()
priority_issues = validator.get_priority_issues()

print(f"健康分數: {health_score}/100")
print(f"優先問題: {len(priority_issues)}")
```

## 🔧 配置示例

### 建議的生產環境設置

```python
# settings/prod.py

# 安全設置
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000  # 1 年
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_AGE = 1209600  # 2 週

CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True

# 媒體和靜態文件
MEDIA_ROOT = '/var/www/media'
STATIC_ROOT = '/var/www/static'
MEDIA_URL = '/media/'
STATIC_URL = '/static/'

# 數據庫連接池
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'your_db',
        'CONN_MAX_AGE': 600,  # 10 分鐘
        # ... 其他設置
    }
}

# 緩存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}

# 日誌配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# 密碼驗證器
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {'min_length': 8,}
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]
```

## 🧪 測試

### 運行測試

```bash
# 運行所有配置相關測試
python manage.py test config.tests

# 運行特定測試類別
python manage.py test config.tests.test_enhanced_validation
python manage.py test config.tests.test_management_commands

# 詳細測試輸出
python manage.py test config.tests --verbosity=2
```

### 測試覆蓋率

```bash
# 安裝 coverage (如果未安裝)
pip install coverage

# 運行覆蓋率測試
coverage run --source='config' manage.py test config.tests
coverage report
coverage html  # 生成 HTML 報告
```

## 🔄 CI/CD 集成

### GitHub Actions 示例

```yaml
name: Django 配置檢查

on: [push, pull_request]

jobs:
  config-check:
    runs-on: ubuntu-latest
<<<<<<< HEAD
<<<<<<< HEAD

    steps:
    - uses: actions/checkout@v2

=======
    
    steps:
    - uses: actions/checkout@v2
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    steps:
    - uses: actions/checkout@v2

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
<<<<<<< HEAD
<<<<<<< HEAD

    - name: Install dependencies
      run: |
        pip install -r requirements.txt

    - name: Run Django config check
      run: |
        python manage.py check_config --fail-on-error --verbose

    - name: Export config report
      run: |
        python manage.py check_config --export-report

=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    - name: Install dependencies
      run: |
        pip install -r requirements.txt

    - name: Run Django config check
      run: |
        python manage.py check_config --fail-on-error --verbose

    - name: Export config report
      run: |
        python manage.py check_config --export-report
<<<<<<< HEAD
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    - name: Upload config report
      uses: actions/upload-artifact@v2
      with:
        name: config-check-report
        path: config-check-report.json
```

### GitLab CI 示例

```yaml
config_check:
  stage: test
  script:
    - python manage.py check_config --fail-on-error
    - python manage.py check_config --export-report
  artifacts:
    paths:
      - config-check-report.json
    expire_in: 1 week
  only:
    - merge_requests
    - master
```

## 📊 輸出示例

### 文字格式報告

```
============================================================
Django 配置完整性檢查報告
============================================================

🏥 總體健康分數: 85/100
📊 問題統計:
   ❌ 錯誤: 2
   ⚠️  警告: 3
   💡 建議: 5

🚨 需要優先處理的問題:
   ❌ [SECURITY] 生產環境未強制 HTTPS
      💊 解決方案: 設置 SECURE_SSL_REDIRECT = True

❌ 錯誤詳情:
   • [security] 生產環境未強制 HTTPS
     💊 設置 SECURE_SSL_REDIRECT = True
   • [media] MEDIA_ROOT 未設置
     💊 設置 MEDIA_ROOT = os.path.join(BASE_DIR, "media")

⚠️ 警告詳情:
   • [performance] 使用 DummyCache 後端
     💊 在生產環境使用 Redis 或 Memcached
   • [logging] 生產環境建議配置文件日誌處理器
     💊 添加 RotatingFileHandler 以持久化日誌

💡 改進建議:
   • [performance] 數據庫連接未啟用持久化
     💊 設置 CONN_MAX_AGE = 600 以提升性能
   • [api] API 未配置限流
     💊 添加限流配置以防止濫用

📈 問題分佈:
   security: 2
   performance: 3
   media: 1
   logging: 1
   api: 3

============================================================
檢查完成
============================================================
```

### JSON 格式報告

```json
{
  "summary": {
    "total_issues": 10,
    "errors": 2,
    "warnings": 3,
    "suggestions": 5,
    "health_score": 85,
    "priority_issues_count": 1,
    "categories": {
      "security": 2,
      "performance": 3,
      "media": 1,
      "logging": 1,
      "api": 3
    }
  },
  "errors": [
    {
      "level": "error",
      "category": "security",
      "message": "生產環境未強制 HTTPS",
      "fix_suggestion": "設置 SECURE_SSL_REDIRECT = True",
      "setting_name": "SECURE_SSL_REDIRECT"
    }
  ],
  "warnings": [...],
  "suggestions": [...],
  "priority_issues": [...]
}
```

## 🛠️ 擴展開發

### 添加新的檢查類別

1. **在 `EnhancedConfigValidator` 中添加新方法**:
   ```python
   def _check_custom_category(self):
       """檢查自定義類別"""
       # 實現檢查邏輯
       if condition:
           self._add_issue(
<<<<<<< HEAD
<<<<<<< HEAD
               'error', 'custom',
=======
               'error', 'custom', 
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
               'error', 'custom',
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
               '問題描述',
               '修復建議',
               'SETTING_NAME'
           )
   ```

2. **在 `validate_all()` 中調用新方法**:
   ```python
   def validate_all(self) -> ValidationResult:
       # ... 現有檢查
       self._check_custom_category()
       return self.result
   ```

3. **添加相應的測試**:
   ```python
   def test_custom_category_check(self):
       """測試自定義類別檢查"""
       # 實現測試邏輯
   ```

### 自定義驗證規則

```python
from config.enhanced_validation import EnhancedConfigValidator

class CustomConfigValidator(EnhancedConfigValidator):
    """自定義配置驗證器"""
<<<<<<< HEAD
<<<<<<< HEAD

    def validate_all(self) -> ValidationResult:
        # 調用父類方法
        result = super().validate_all()

        # 添加自定義檢查
        self._check_project_specific_settings()

        return self.result

=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def validate_all(self) -> ValidationResult:
        # 調用父類方法
        result = super().validate_all()

        # 添加自定義檢查
        self._check_project_specific_settings()

        return self.result
<<<<<<< HEAD
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def _check_project_specific_settings(self):
        """檢查項目特定設置"""
        # 實現自定義邏輯
        pass
```

## 🤝 貢獻指南

1. **Fork 項目**
2. **創建功能分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'Add amazing feature'`)
4. **推送到分支** (`git push origin feature/amazing-feature`)
5. **打開 Pull Request**

## 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 🙏 致謝

- Django 社區提供的優秀框架
- 所有貢獻者的辛勤工作
- 配置管理最佳實踐的啟發

---

<<<<<<< HEAD
<<<<<<< HEAD
**Issue #184** - Django 配置完整性檢查機制實現完成 ✅
=======
**Issue #184** - Django 配置完整性檢查機制實現完成 ✅
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
**Issue #184** - Django 配置完整性檢查機制實現完成 ✅
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
