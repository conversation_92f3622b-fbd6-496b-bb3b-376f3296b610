"""
Django 配置完整性檢查機制 - Issue #184
=====================================

提供全面的 Django 配置驗證和健康檢查功能，包含：
- 媒體和靜態文件配置檢查
- 日誌配置完整性檢查
- 性能相關配置檢查
- 增強安全配置檢查
- API 配置檢查
- 國際化配置檢查
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path

from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

from .validation import _is_test_environment

logger = logging.getLogger(__name__)


@dataclass
class ValidationIssue:
    """配置驗證問題數據類"""
<<<<<<< HEAD

=======
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    level: str  # 'error', 'warning', 'suggestion'
    category: str
    message: str
    fix_suggestion: Optional[str] = None
    setting_name: Optional[str] = None


<<<<<<< HEAD
@dataclass
class ValidationResult:
    """配置驗證結果數據類"""

    errors: List[ValidationIssue] = field(default_factory=list)
    warnings: List[ValidationIssue] = field(default_factory=list)
    suggestions: List[ValidationIssue] = field(default_factory=list)

    @property
    def has_errors(self) -> bool:
        return len(self.errors) > 0

    @property
    def has_warnings(self) -> bool:
        return len(self.warnings) > 0

=======
@dataclass  
class ValidationResult:
    """配置驗證結果數據類"""
    errors: List[ValidationIssue] = field(default_factory=list)
    warnings: List[ValidationIssue] = field(default_factory=list)
    suggestions: List[ValidationIssue] = field(default_factory=list)
    
    @property
    def has_errors(self) -> bool:
        return len(self.errors) > 0
    
    @property
    def has_warnings(self) -> bool:
        return len(self.warnings) > 0
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @property
    def total_issues(self) -> int:
        return len(self.errors) + len(self.warnings) + len(self.suggestions)


class EnhancedConfigValidator:
    """增強的 Django 配置驗證器"""
<<<<<<< HEAD

    def __init__(self):
        self.result = ValidationResult()

    def _add_issue(
        self,
        level: str,
        category: str,
        message: str,
        fix_suggestion: str = None,
        setting_name: str = None,
    ):
        """添加驗證問題"""
        issue = ValidationIssue(
            level=level,
            category=category,
            message=message,
            fix_suggestion=fix_suggestion,
            setting_name=setting_name,
        )

        if level == "error":
            self.result.errors.append(issue)
        elif level == "warning":
            self.result.warnings.append(issue)
        else:
            self.result.suggestions.append(issue)

    def validate_all(self) -> ValidationResult:
        """執行所有配置檢查"""
        logger.info("開始執行增強配置完整性檢查...")

        # 重置結果
        self.result = ValidationResult()

=======
    
    def __init__(self):
        self.result = ValidationResult()
    
    def _add_issue(self, level: str, category: str, message: str, 
                  fix_suggestion: str = None, setting_name: str = None):
        """添加驗證問題"""
        issue = ValidationIssue(
            level=level,
            category=category, 
            message=message,
            fix_suggestion=fix_suggestion,
            setting_name=setting_name
        )
        
        if level == 'error':
            self.result.errors.append(issue)
        elif level == 'warning':
            self.result.warnings.append(issue)
        else:
            self.result.suggestions.append(issue)
    
    def validate_all(self) -> ValidationResult:
        """執行所有配置檢查"""
        logger.info("開始執行增強配置完整性檢查...")
        
        # 重置結果
        self.result = ValidationResult()
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        # 執行各項檢查
        self._check_media_and_static()
        self._check_logging_config()
        self._check_performance_settings()
        self._check_security_enhanced()
        self._check_api_configuration()
        self._check_internationalization()
<<<<<<< HEAD

        logger.info(f"配置檢查完成: {self.result.total_issues} 個問題發現")
        return self.result

    def _check_media_and_static(self):
        """檢查媒體和靜態文件配置"""
        # MEDIA_ROOT 檢查
        media_root = getattr(settings, "MEDIA_ROOT", None)
        if not media_root:
            self._add_issue(
                "error",
                "media",
                "MEDIA_ROOT 未設置",
                '設置 MEDIA_ROOT = os.path.join(BASE_DIR, "media")',
                "MEDIA_ROOT",
            )
        elif not os.path.isabs(media_root):
            self._add_issue(
                "warning",
                "media",
                "MEDIA_ROOT 不是絕對路徑",
                "使用絕對路徑以避免部署問題",
                "MEDIA_ROOT",
            )

        # STATIC_ROOT 檢查
        static_root = getattr(settings, "STATIC_ROOT", None)
        if not static_root and not settings.DEBUG:
            self._add_issue(
                "error",
                "static",
                "生產環境未設置 STATIC_ROOT",
                '設置 STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")',
                "STATIC_ROOT",
            )

        # STATICFILES_DIRS 檢查
        staticfiles_dirs = getattr(settings, "STATICFILES_DIRS", [])
=======
        
        logger.info(f"配置檢查完成: {self.result.total_issues} 個問題發現")
        return self.result
    
    def _check_media_and_static(self):
        """檢查媒體和靜態文件配置"""
        # MEDIA_ROOT 檢查
        media_root = getattr(settings, 'MEDIA_ROOT', None)
        if not media_root:
            self._add_issue(
                'error', 'media', 
                'MEDIA_ROOT 未設置',
                '設置 MEDIA_ROOT = os.path.join(BASE_DIR, "media")',
                'MEDIA_ROOT'
            )
        elif not os.path.isabs(media_root):
            self._add_issue(
                'warning', 'media',
                'MEDIA_ROOT 不是絕對路徑',
                '使用絕對路徑以避免部署問題',
                'MEDIA_ROOT'
            )
        
        # STATIC_ROOT 檢查
        static_root = getattr(settings, 'STATIC_ROOT', None)
        if not static_root and not settings.DEBUG:
            self._add_issue(
                'error', 'static',
                '生產環境未設置 STATIC_ROOT',
                '設置 STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")',
                'STATIC_ROOT'
            )
        
        # STATICFILES_DIRS 檢查
        staticfiles_dirs = getattr(settings, 'STATICFILES_DIRS', [])
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        if staticfiles_dirs:
            for static_dir in staticfiles_dirs:
                if isinstance(static_dir, tuple):
                    static_dir = static_dir[1]
                if not os.path.exists(static_dir):
                    self._add_issue(
<<<<<<< HEAD
                        "warning",
                        "static",
                        f"STATICFILES_DIRS 中的目錄不存在: {static_dir}",
                        "檢查路徑是否正確或創建缺失的目錄",
                        "STATICFILES_DIRS",
                    )

        # 媒體 URL 檢查
        media_url = getattr(settings, "MEDIA_URL", None)
        if not media_url:
            self._add_issue(
                "error",
                "media",
                "MEDIA_URL 未設置",
                '設置 MEDIA_URL = "/media/"',
                "MEDIA_URL",
            )
        elif not media_url.endswith("/"):
            self._add_issue(
                "suggestion",
                "media",
                'MEDIA_URL 應該以 "/" 結尾',
                '修改為 MEDIA_URL = "/media/"',
                "MEDIA_URL",
            )

        # 文件上傳大小檢查
        max_upload_size = getattr(
            settings, "FILE_UPLOAD_MAX_MEMORY_SIZE", 2621440
        )  # 2.5MB default
        if max_upload_size > 10 * 1024 * 1024:  # 10MB
            self._add_issue(
                "warning",
                "media",
                f"FILE_UPLOAD_MAX_MEMORY_SIZE 過大: {max_upload_size / 1024 / 1024:.1f}MB",
                "考慮降低到 5MB 以下以提升性能",
                "FILE_UPLOAD_MAX_MEMORY_SIZE",
            )

    def _check_logging_config(self):
        """檢查日誌配置完整性"""
        logging_config = getattr(settings, "LOGGING", {})

        if not logging_config:
            self._add_issue(
                "error",
                "logging",
                "缺少 LOGGING 配置",
                "添加基本的日誌配置以便錯誤追蹤",
                "LOGGING",
            )
            return

        # 檢查 handlers
        handlers = logging_config.get("handlers", {})
        if not handlers:
            self._add_issue(
                "warning",
                "logging",
                "LOGGING 配置缺少處理器 (handlers)",
                "添加文件和控制台處理器",
                "LOGGING.handlers",
            )

        # 檢查是否有文件處理器
        has_file_handler = any(
            handler.get("class")
            in ["logging.FileHandler", "logging.handlers.RotatingFileHandler"]
            for handler in handlers.values()
        )

        if not has_file_handler and not settings.DEBUG:
            self._add_issue(
                "warning",
                "logging",
                "生產環境建議配置文件日誌處理器",
                "添加 RotatingFileHandler 以持久化日誌",
                "LOGGING.handlers",
            )

        # 檢查 loggers
        loggers = logging_config.get("loggers", {})
        important_loggers = ["django", "django.request", "django.security"]

        for logger_name in important_loggers:
            if logger_name not in loggers:
                self._add_issue(
                    "suggestion",
                    "logging",
                    f"建議添加 {logger_name} 日誌器配置",
                    f"添加 {logger_name} 日誌器以更好地追蹤框架事件",
                    f"LOGGING.loggers.{logger_name}",
                )

        # 檢查日誌級別
        root_level = logging_config.get("root", {}).get("level", "INFO")
        if root_level == "DEBUG" and not settings.DEBUG:
            self._add_issue(
                "warning",
                "logging",
                "生產環境使用 DEBUG 日誌級別",
                "將日誌級別設置為 INFO 或 WARNING",
                "LOGGING.root.level",
            )

    def _check_performance_settings(self):
        """檢查性能相關配置"""
        # 數據庫連接檢查
        databases = getattr(settings, "DATABASES", {})
        default_db = databases.get("default", {})

        if default_db:
            # 連接池配置
            conn_max_age = default_db.get("CONN_MAX_AGE", 0)
            if conn_max_age == 0:
                self._add_issue(
                    "suggestion",
                    "performance",
                    "數據庫連接未啟用持久化",
                    "設置 CONN_MAX_AGE = 600 以提升性能",
                    "DATABASES.default.CONN_MAX_AGE",
                )
            elif conn_max_age > 3600:
                self._add_issue(
                    "warning",
                    "performance",
                    f"數據庫連接持久化時間過長: {conn_max_age}秒",
                    "建議設置為 600-1800 秒之間",
                    "DATABASES.default.CONN_MAX_AGE",
                )

        # 緩存配置檢查
        caches = getattr(settings, "CACHES", {})
        default_cache = caches.get("default", {})

        if (
            default_cache.get("BACKEND")
            == "django.core.cache.backends.dummy.DummyCache"
        ):
            self._add_issue(
                "warning",
                "performance",
                "使用 DummyCache 後端",
                "在生產環境使用 Redis 或 Memcached",
                "CACHES.default.BACKEND",
            )

        # 中間件順序檢查
        middleware = getattr(settings, "MIDDLEWARE", [])
        if middleware:
            # SecurityMiddleware 應該在前面
            security_index = next(
                (i for i, m in enumerate(middleware) if "SecurityMiddleware" in m), None
            )
            if security_index is None:
                self._add_issue(
                    "warning",
                    "security",
                    "缺少 SecurityMiddleware",
                    "添加 django.middleware.security.SecurityMiddleware",
                    "MIDDLEWARE",
                )
            elif security_index > 2:
                self._add_issue(
                    "suggestion",
                    "performance",
                    "SecurityMiddleware 位置較後",
                    "將 SecurityMiddleware 移到中間件列表前面",
                    "MIDDLEWARE",
                )

            # GZip 中間件檢查
            has_gzip = any("GZipMiddleware" in m for m in middleware)
            if not has_gzip and not settings.DEBUG:
                self._add_issue(
                    "suggestion",
                    "performance",
                    "缺少 GZipMiddleware",
                    "添加 django.middleware.gzip.GZipMiddleware 以壓縮回應",
                    "MIDDLEWARE",
                )

        # 模板配置檢查
        templates = getattr(settings, "TEMPLATES", [])
        for template_config in templates:
            options = template_config.get("OPTIONS", {})
            if not options.get("loaders") and settings.DEBUG:
                self._add_issue(
                    "suggestion",
                    "performance",
                    "開發環境可考慮配置模板快取載入器",
                    "在 DEBUG=False 時自動啟用快取載入器",
                    "TEMPLATES.OPTIONS.loaders",
                )

=======
                        'warning', 'static',
                        f'STATICFILES_DIRS 中的目錄不存在: {static_dir}',
                        '檢查路徑是否正確或創建缺失的目錄',
                        'STATICFILES_DIRS'
                    )
        
        # 媒體 URL 檢查
        media_url = getattr(settings, 'MEDIA_URL', None)
        if not media_url:
            self._add_issue(
                'error', 'media',
                'MEDIA_URL 未設置',
                '設置 MEDIA_URL = "/media/"',
                'MEDIA_URL'
            )
        elif not media_url.endswith('/'):
            self._add_issue(
                'suggestion', 'media',
                'MEDIA_URL 應該以 "/" 結尾',
                '修改為 MEDIA_URL = "/media/"',
                'MEDIA_URL'
            )
        
        # 文件上傳大小檢查
        max_upload_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 2621440)  # 2.5MB default
        if max_upload_size > 10 * 1024 * 1024:  # 10MB
            self._add_issue(
                'warning', 'media',
                f'FILE_UPLOAD_MAX_MEMORY_SIZE 過大: {max_upload_size / 1024 / 1024:.1f}MB',
                '考慮降低到 5MB 以下以提升性能',
                'FILE_UPLOAD_MAX_MEMORY_SIZE'
            )
    
    def _check_logging_config(self):
        """檢查日誌配置完整性"""
        logging_config = getattr(settings, 'LOGGING', {})
        
        if not logging_config:
            self._add_issue(
                'error', 'logging',
                '缺少 LOGGING 配置',
                '添加基本的日誌配置以便錯誤追蹤',
                'LOGGING'
            )
            return
        
        # 檢查 handlers
        handlers = logging_config.get('handlers', {})
        if not handlers:
            self._add_issue(
                'warning', 'logging',
                'LOGGING 配置缺少處理器 (handlers)',
                '添加文件和控制台處理器',
                'LOGGING.handlers'
            )
        
        # 檢查是否有文件處理器
        has_file_handler = any(
            handler.get('class') in ['logging.FileHandler', 'logging.handlers.RotatingFileHandler']
            for handler in handlers.values()
        )
        
        if not has_file_handler and not settings.DEBUG:
            self._add_issue(
                'warning', 'logging',
                '生產環境建議配置文件日誌處理器',
                '添加 RotatingFileHandler 以持久化日誌',
                'LOGGING.handlers'
            )
        
        # 檢查 loggers
        loggers = logging_config.get('loggers', {})
        important_loggers = ['django', 'django.request', 'django.security']
        
        for logger_name in important_loggers:
            if logger_name not in loggers:
                self._add_issue(
                    'suggestion', 'logging',
                    f'建議添加 {logger_name} 日誌器配置',
                    f'添加 {logger_name} 日誌器以更好地追蹤框架事件',
                    f'LOGGING.loggers.{logger_name}'
                )
        
        # 檢查日誌級別
        root_level = logging_config.get('root', {}).get('level', 'INFO')
        if root_level == 'DEBUG' and not settings.DEBUG:
            self._add_issue(
                'warning', 'logging',
                '生產環境使用 DEBUG 日誌級別',
                '將日誌級別設置為 INFO 或 WARNING',
                'LOGGING.root.level'
            )
    
    def _check_performance_settings(self):
        """檢查性能相關配置"""
        # 數據庫連接檢查
        databases = getattr(settings, 'DATABASES', {})
        default_db = databases.get('default', {})
        
        if default_db:
            # 連接池配置
            conn_max_age = default_db.get('CONN_MAX_AGE', 0)
            if conn_max_age == 0:
                self._add_issue(
                    'suggestion', 'performance',
                    '數據庫連接未啟用持久化',
                    '設置 CONN_MAX_AGE = 600 以提升性能',
                    'DATABASES.default.CONN_MAX_AGE'
                )
            elif conn_max_age > 3600:
                self._add_issue(
                    'warning', 'performance',
                    f'數據庫連接持久化時間過長: {conn_max_age}秒',
                    '建議設置為 600-1800 秒之間',
                    'DATABASES.default.CONN_MAX_AGE'
                )
        
        # 緩存配置檢查
        caches = getattr(settings, 'CACHES', {})
        default_cache = caches.get('default', {})
        
        if default_cache.get('BACKEND') == 'django.core.cache.backends.dummy.DummyCache':
            self._add_issue(
                'warning', 'performance',
                '使用 DummyCache 後端',
                '在生產環境使用 Redis 或 Memcached',
                'CACHES.default.BACKEND'
            )
        
        # 中間件順序檢查
        middleware = getattr(settings, 'MIDDLEWARE', [])
        if middleware:
            # SecurityMiddleware 應該在前面
            security_index = next((i for i, m in enumerate(middleware) 
                                 if 'SecurityMiddleware' in m), None)
            if security_index is None:
                self._add_issue(
                    'warning', 'security',
                    '缺少 SecurityMiddleware',
                    '添加 django.middleware.security.SecurityMiddleware',
                    'MIDDLEWARE'
                )
            elif security_index > 2:
                self._add_issue(
                    'suggestion', 'performance',
                    'SecurityMiddleware 位置較後',
                    '將 SecurityMiddleware 移到中間件列表前面',
                    'MIDDLEWARE'
                )
            
            # GZip 中間件檢查
            has_gzip = any('GZipMiddleware' in m for m in middleware)
            if not has_gzip and not settings.DEBUG:
                self._add_issue(
                    'suggestion', 'performance',
                    '缺少 GZipMiddleware',
                    '添加 django.middleware.gzip.GZipMiddleware 以壓縮回應',
                    'MIDDLEWARE'
                )
        
        # 模板配置檢查
        templates = getattr(settings, 'TEMPLATES', [])
        for template_config in templates:
            options = template_config.get('OPTIONS', {})
            if not options.get('loaders') and settings.DEBUG:
                self._add_issue(
                    'suggestion', 'performance',
                    '開發環境可考慮配置模板快取載入器',
                    '在 DEBUG=False 時自動啟用快取載入器',
                    'TEMPLATES.OPTIONS.loaders'
                )
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def _check_security_enhanced(self):
        """增強安全配置檢查"""
        # HTTPS 設置檢查
        if not settings.DEBUG:
            # SECURE_SSL_REDIRECT 檢查
<<<<<<< HEAD
            if not getattr(settings, "SECURE_SSL_REDIRECT", False):
                self._add_issue(
                    "error",
                    "security",
                    "生產環境未強制 HTTPS",
                    "設置 SECURE_SSL_REDIRECT = True",
                    "SECURE_SSL_REDIRECT",
                )

            # HSTS 設置
            hsts_seconds = getattr(settings, "SECURE_HSTS_SECONDS", 0)
            if hsts_seconds == 0:
                self._add_issue(
                    "warning",
                    "security",
                    "未設置 HSTS (HTTP Strict Transport Security)",
                    "設置 SECURE_HSTS_SECONDS = 31536000",
                    "SECURE_HSTS_SECONDS",
                )
            elif hsts_seconds < 86400:  # 1 day
                self._add_issue(
                    "suggestion",
                    "security",
                    f"HSTS 持續時間較短: {hsts_seconds}秒",
                    "建議設置為至少 31536000 (1年)",
                    "SECURE_HSTS_SECONDS",
                )

            # HSTS 相關設置
            if not getattr(settings, "SECURE_HSTS_INCLUDE_SUBDOMAINS", False):
                self._add_issue(
                    "suggestion",
                    "security",
                    "未啟用 HSTS 子域名保護",
                    "設置 SECURE_HSTS_INCLUDE_SUBDOMAINS = True",
                    "SECURE_HSTS_INCLUDE_SUBDOMAINS",
                )

        # Session 安全檢查
        if not getattr(settings, "SESSION_COOKIE_SECURE", False) and not settings.DEBUG:
            self._add_issue(
                "error",
                "security",
                "Session Cookie 未設置 Secure 標記",
                "設置 SESSION_COOKIE_SECURE = True",
                "SESSION_COOKIE_SECURE",
            )

        if not getattr(settings, "SESSION_COOKIE_HTTPONLY", True):
            self._add_issue(
                "error",
                "security",
                "Session Cookie 未設置 HttpOnly 標記",
                "設置 SESSION_COOKIE_HTTPONLY = True",
                "SESSION_COOKIE_HTTPONLY",
            )

        session_age = getattr(
            settings, "SESSION_COOKIE_AGE", 1209600
        )  # 2 weeks default
        if session_age > 2592000:  # 30 days
            self._add_issue(
                "warning",
                "security",
                f"Session 過期時間過長: {session_age / 86400:.1f}天",
                "考慮縮短為 7-14 天",
                "SESSION_COOKIE_AGE",
            )

        # CSRF 保護檢查
        if not getattr(settings, "CSRF_COOKIE_SECURE", False) and not settings.DEBUG:
            self._add_issue(
                "error",
                "security",
                "CSRF Cookie 未設置 Secure 標記",
                "設置 CSRF_COOKIE_SECURE = True",
                "CSRF_COOKIE_SECURE",
            )

        # 文件上傳安全檢查
        upload_handlers = getattr(settings, "FILE_UPLOAD_HANDLERS", [])
        if "django.core.files.uploadhandler.MemoryFileUploadHandler" in upload_handlers:
            max_size = getattr(settings, "FILE_UPLOAD_MAX_MEMORY_SIZE", 2621440)
            if max_size > 10 * 1024 * 1024:  # 10MB
                self._add_issue(
                    "warning",
                    "security",
                    "記憶體文件上傳大小限制過大",
                    "降低 FILE_UPLOAD_MAX_MEMORY_SIZE 以防止 DoS 攻擊",
                    "FILE_UPLOAD_MAX_MEMORY_SIZE",
                )

        # 密碼驗證器檢查
        auth_password_validators = getattr(settings, "AUTH_PASSWORD_VALIDATORS", [])
        if not auth_password_validators:
            self._add_issue(
                "error",
                "security",
                "缺少密碼驗證器",
                "添加 Django 默認密碼驗證器",
                "AUTH_PASSWORD_VALIDATORS",
            )
        else:
            validator_classes = [v.get("NAME", "") for v in auth_password_validators]
            required_validators = [
                "UserAttributeSimilarityValidator",
                "MinimumLengthValidator",
                "CommonPasswordValidator",
                "NumericPasswordValidator",
            ]

            for validator in required_validators:
                if not any(validator in vc for vc in validator_classes):
                    self._add_issue(
                        "suggestion",
                        "security",
                        f"建議添加 {validator} 密碼驗證器",
                        f"添加 django.contrib.auth.password_validation.{validator}",
                        "AUTH_PASSWORD_VALIDATORS",
                    )

    def _check_api_configuration(self):
        """檢查 API 配置"""
        # Django REST Framework 檢查
        if "rest_framework" in getattr(settings, "INSTALLED_APPS", []):
            rest_settings = getattr(settings, "REST_FRAMEWORK", {})

            # 認證檢查
            auth_classes = rest_settings.get("DEFAULT_AUTHENTICATION_CLASSES", [])
            if not auth_classes:
                self._add_issue(
                    "warning",
                    "api",
                    "API 未配置認證類別",
                    "添加適當的認證類別如 TokenAuthentication",
                    "REST_FRAMEWORK.DEFAULT_AUTHENTICATION_CLASSES",
                )

            # 權限檢查
            permission_classes = rest_settings.get("DEFAULT_PERMISSION_CLASSES", [])
            if not permission_classes or "AllowAny" in str(permission_classes):
                self._add_issue(
                    "warning",
                    "api",
                    "API 權限配置過於寬鬆",
                    "設置適當的權限類別，避免使用 AllowAny",
                    "REST_FRAMEWORK.DEFAULT_PERMISSION_CLASSES",
                )

            # 頁數限制檢查
            pagination = rest_settings.get("DEFAULT_PAGINATION_CLASS")
            if not pagination:
                self._add_issue(
                    "suggestion",
                    "api",
                    "API 未配置分頁",
                    "添加分頁配置以提升性能",
                    "REST_FRAMEWORK.DEFAULT_PAGINATION_CLASS",
                )

            # 限流檢查
            throttle = rest_settings.get("DEFAULT_THROTTLE_CLASSES", [])
            if not throttle:
                self._add_issue(
                    "suggestion",
                    "api",
                    "API 未配置限流",
                    "添加限流配置以防止濫用",
                    "REST_FRAMEWORK.DEFAULT_THROTTLE_CLASSES",
                )

        # CORS 檢查
        if "corsheaders" in getattr(settings, "INSTALLED_APPS", []):
            cors_origins = getattr(settings, "CORS_ALLOWED_ORIGINS", [])
            cors_allow_all = getattr(settings, "CORS_ALLOW_ALL_ORIGINS", False)

            if cors_allow_all and not settings.DEBUG:
                self._add_issue(
                    "error",
                    "security",
                    "生產環境允許所有 CORS 來源",
                    "設置具體的 CORS_ALLOWED_ORIGINS",
                    "CORS_ALLOW_ALL_ORIGINS",
                )
            elif not cors_origins and not cors_allow_all:
                self._add_issue(
                    "warning",
                    "api",
                    "CORS 配置過於嚴格",
                    "設置適當的 CORS_ALLOWED_ORIGINS",
                    "CORS_ALLOWED_ORIGINS",
                )

    def _check_internationalization(self):
        """檢查國際化和時區配置"""
        # 時區設置檢查
        timezone = getattr(settings, "TIME_ZONE", "UTC")
        use_tz = getattr(settings, "USE_TZ", False)

        if not use_tz:
            self._add_issue(
                "warning", "i18n", "未啟用時區支持", "設置 USE_TZ = True", "USE_TZ"
            )

        if timezone == "UTC" and use_tz:
            self._add_issue(
                "suggestion",
                "i18n",
                "使用 UTC 時區",
                "考慮設置為本地時區，如 Asia/Taipei",
                "TIME_ZONE",
            )

        # 語言設置檢查
        language_code = getattr(settings, "LANGUAGE_CODE", "en-us")
        use_i18n = getattr(settings, "USE_I18N", True)

        if use_i18n:
            languages = getattr(settings, "LANGUAGES", [])
            if not languages and language_code != "en-us":
                self._add_issue(
                    "suggestion",
                    "i18n",
                    "啟用了國際化但未定義支持的語言",
                    "設置 LANGUAGES 元組定義支持的語言",
                    "LANGUAGES",
                )

        # 本地化設置
        use_l10n = getattr(settings, "USE_L10N", True)
        if not use_l10n and use_i18n:
            self._add_issue(
                "suggestion",
                "i18n",
                "啟用了國際化但未啟用本地化",
                "設置 USE_L10N = True",
                "USE_L10N",
            )

=======
            if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
                self._add_issue(
                    'error', 'security',
                    '生產環境未強制 HTTPS',
                    '設置 SECURE_SSL_REDIRECT = True',
                    'SECURE_SSL_REDIRECT'
                )
            
            # HSTS 設置
            hsts_seconds = getattr(settings, 'SECURE_HSTS_SECONDS', 0)
            if hsts_seconds == 0:
                self._add_issue(
                    'warning', 'security',
                    '未設置 HSTS (HTTP Strict Transport Security)',
                    '設置 SECURE_HSTS_SECONDS = 31536000',
                    'SECURE_HSTS_SECONDS'
                )
            elif hsts_seconds < 86400:  # 1 day
                self._add_issue(
                    'suggestion', 'security',
                    f'HSTS 持續時間較短: {hsts_seconds}秒',
                    '建議設置為至少 31536000 (1年)',
                    'SECURE_HSTS_SECONDS'
                )
            
            # HSTS 相關設置
            if not getattr(settings, 'SECURE_HSTS_INCLUDE_SUBDOMAINS', False):
                self._add_issue(
                    'suggestion', 'security',
                    '未啟用 HSTS 子域名保護',
                    '設置 SECURE_HSTS_INCLUDE_SUBDOMAINS = True',
                    'SECURE_HSTS_INCLUDE_SUBDOMAINS'
                )
        
        # Session 安全檢查
        if not getattr(settings, 'SESSION_COOKIE_SECURE', False) and not settings.DEBUG:
            self._add_issue(
                'error', 'security',
                'Session Cookie 未設置 Secure 標記',
                '設置 SESSION_COOKIE_SECURE = True',
                'SESSION_COOKIE_SECURE'
            )
        
        if not getattr(settings, 'SESSION_COOKIE_HTTPONLY', True):
            self._add_issue(
                'error', 'security',
                'Session Cookie 未設置 HttpOnly 標記',
                '設置 SESSION_COOKIE_HTTPONLY = True',
                'SESSION_COOKIE_HTTPONLY'
            )
        
        session_age = getattr(settings, 'SESSION_COOKIE_AGE', 1209600)  # 2 weeks default
        if session_age > 2592000:  # 30 days
            self._add_issue(
                'warning', 'security',
                f'Session 過期時間過長: {session_age / 86400:.1f}天',
                '考慮縮短為 7-14 天',
                'SESSION_COOKIE_AGE'
            )
        
        # CSRF 保護檢查
        if not getattr(settings, 'CSRF_COOKIE_SECURE', False) and not settings.DEBUG:
            self._add_issue(
                'error', 'security',
                'CSRF Cookie 未設置 Secure 標記',
                '設置 CSRF_COOKIE_SECURE = True',
                'CSRF_COOKIE_SECURE'
            )
        
        # 文件上傳安全檢查
        upload_handlers = getattr(settings, 'FILE_UPLOAD_HANDLERS', [])
        if 'django.core.files.uploadhandler.MemoryFileUploadHandler' in upload_handlers:
            max_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 2621440)
            if max_size > 10 * 1024 * 1024:  # 10MB
                self._add_issue(
                    'warning', 'security',
                    '記憶體文件上傳大小限制過大',
                    '降低 FILE_UPLOAD_MAX_MEMORY_SIZE 以防止 DoS 攻擊',
                    'FILE_UPLOAD_MAX_MEMORY_SIZE'
                )
        
        # 密碼驗證器檢查
        auth_password_validators = getattr(settings, 'AUTH_PASSWORD_VALIDATORS', [])
        if not auth_password_validators:
            self._add_issue(
                'error', 'security',
                '缺少密碼驗證器',
                '添加 Django 默認密碼驗證器',
                'AUTH_PASSWORD_VALIDATORS'
            )
        else:
            validator_classes = [v.get('NAME', '') for v in auth_password_validators]
            required_validators = [
                'UserAttributeSimilarityValidator',
                'MinimumLengthValidator', 
                'CommonPasswordValidator',
                'NumericPasswordValidator'
            ]
            
            for validator in required_validators:
                if not any(validator in vc for vc in validator_classes):
                    self._add_issue(
                        'suggestion', 'security',
                        f'建議添加 {validator} 密碼驗證器',
                        f'添加 django.contrib.auth.password_validation.{validator}',
                        'AUTH_PASSWORD_VALIDATORS'
                    )
    
    def _check_api_configuration(self):
        """檢查 API 配置"""
        # Django REST Framework 檢查
        if 'rest_framework' in getattr(settings, 'INSTALLED_APPS', []):
            rest_settings = getattr(settings, 'REST_FRAMEWORK', {})
            
            # 認證檢查
            auth_classes = rest_settings.get('DEFAULT_AUTHENTICATION_CLASSES', [])
            if not auth_classes:
                self._add_issue(
                    'warning', 'api',
                    'API 未配置認證類別',
                    '添加適當的認證類別如 TokenAuthentication',
                    'REST_FRAMEWORK.DEFAULT_AUTHENTICATION_CLASSES'
                )
            
            # 權限檢查
            permission_classes = rest_settings.get('DEFAULT_PERMISSION_CLASSES', [])
            if not permission_classes or 'AllowAny' in str(permission_classes):
                self._add_issue(
                    'warning', 'api',
                    'API 權限配置過於寬鬆',
                    '設置適當的權限類別，避免使用 AllowAny',
                    'REST_FRAMEWORK.DEFAULT_PERMISSION_CLASSES'
                )
            
            # 頁數限制檢查
            pagination = rest_settings.get('DEFAULT_PAGINATION_CLASS')
            if not pagination:
                self._add_issue(
                    'suggestion', 'api',
                    'API 未配置分頁',
                    '添加分頁配置以提升性能',
                    'REST_FRAMEWORK.DEFAULT_PAGINATION_CLASS'
                )
            
            # 限流檢查
            throttle = rest_settings.get('DEFAULT_THROTTLE_CLASSES', [])
            if not throttle:
                self._add_issue(
                    'suggestion', 'api',
                    'API 未配置限流',
                    '添加限流配置以防止濫用',
                    'REST_FRAMEWORK.DEFAULT_THROTTLE_CLASSES'
                )
        
        # CORS 檢查
        if 'corsheaders' in getattr(settings, 'INSTALLED_APPS', []):
            cors_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
            cors_allow_all = getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', False)
            
            if cors_allow_all and not settings.DEBUG:
                self._add_issue(
                    'error', 'security',
                    '生產環境允許所有 CORS 來源',
                    '設置具體的 CORS_ALLOWED_ORIGINS',
                    'CORS_ALLOW_ALL_ORIGINS'
                )
            elif not cors_origins and not cors_allow_all:
                self._add_issue(
                    'warning', 'api',
                    'CORS 配置過於嚴格',
                    '設置適當的 CORS_ALLOWED_ORIGINS',
                    'CORS_ALLOWED_ORIGINS'
                )
    
    def _check_internationalization(self):
        """檢查國際化和時區配置"""
        # 時區設置檢查
        timezone = getattr(settings, 'TIME_ZONE', 'UTC')
        use_tz = getattr(settings, 'USE_TZ', False)
        
        if not use_tz:
            self._add_issue(
                'warning', 'i18n',
                '未啟用時區支持',
                '設置 USE_TZ = True',
                'USE_TZ'
            )
        
        if timezone == 'UTC' and use_tz:
            self._add_issue(
                'suggestion', 'i18n',
                '使用 UTC 時區',
                '考慮設置為本地時區，如 Asia/Taipei',
                'TIME_ZONE'
            )
        
        # 語言設置檢查
        language_code = getattr(settings, 'LANGUAGE_CODE', 'en-us')
        use_i18n = getattr(settings, 'USE_I18N', True)
        
        if use_i18n:
            languages = getattr(settings, 'LANGUAGES', [])
            if not languages and language_code != 'en-us':
                self._add_issue(
                    'suggestion', 'i18n',
                    '啟用了國際化但未定義支持的語言',
                    '設置 LANGUAGES 元組定義支持的語言',
                    'LANGUAGES'
                )
        
        # 本地化設置
        use_l10n = getattr(settings, 'USE_L10N', True)
        if not use_l10n and use_i18n:
            self._add_issue(
                'suggestion', 'i18n',
                '啟用了國際化但未啟用本地化',
                '設置 USE_L10N = True',
                'USE_L10N'
            )
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def get_health_score(self) -> int:
        """計算配置健康分數 (0-100)"""
        if self.result.total_issues == 0:
            return 100
<<<<<<< HEAD

        # 權重設置：錯誤影響最大，警告中等，建議最小
        error_weight = 10
        warning_weight = 5
        suggestion_weight = 1

        total_penalty = (
            len(self.result.errors) * error_weight
            + len(self.result.warnings) * warning_weight
            + len(self.result.suggestions) * suggestion_weight
        )

        # 基礎分數 100，根據問題扣除
        max_possible_penalty = 50  # 最大扣除分數
        actual_penalty = min(total_penalty, max_possible_penalty)

        return max(0, 100 - actual_penalty)

    def get_priority_issues(self) -> List[ValidationIssue]:
        """獲取需要優先處理的問題（安全相關錯誤）"""
        priority_issues = []

        for error in self.result.errors:
            if error.category in ["security", "media", "static"]:
                priority_issues.append(error)

        return priority_issues

    def generate_summary(self) -> Dict[str, Any]:
        """生成檢查摘要"""
        return {
            "total_issues": self.result.total_issues,
            "errors": len(self.result.errors),
            "warnings": len(self.result.warnings),
            "suggestions": len(self.result.suggestions),
            "health_score": self.get_health_score(),
            "priority_issues_count": len(self.get_priority_issues()),
            "categories": self._get_category_breakdown(),
        }

    def _get_category_breakdown(self) -> Dict[str, int]:
        """獲取問題類別分佈"""
        categories = {}

        all_issues = self.result.errors + self.result.warnings + self.result.suggestions
        for issue in all_issues:
            categories[issue.category] = categories.get(issue.category, 0) + 1

=======
        
        # 權重設置：錯誤影響最大，警告中等，建議最小
        error_weight = 10
        warning_weight = 5  
        suggestion_weight = 1
        
        total_penalty = (
            len(self.result.errors) * error_weight +
            len(self.result.warnings) * warning_weight +
            len(self.result.suggestions) * suggestion_weight
        )
        
        # 基礎分數 100，根據問題扣除
        max_possible_penalty = 50  # 最大扣除分數
        actual_penalty = min(total_penalty, max_possible_penalty)
        
        return max(0, 100 - actual_penalty)
    
    def get_priority_issues(self) -> List[ValidationIssue]:
        """獲取需要優先處理的問題（安全相關錯誤）"""
        priority_issues = []
        
        for error in self.result.errors:
            if error.category in ['security', 'media', 'static']:
                priority_issues.append(error)
        
        return priority_issues
    
    def generate_summary(self) -> Dict[str, Any]:
        """生成檢查摘要"""
        return {
            'total_issues': self.result.total_issues,
            'errors': len(self.result.errors),
            'warnings': len(self.result.warnings), 
            'suggestions': len(self.result.suggestions),
            'health_score': self.get_health_score(),
            'priority_issues_count': len(self.get_priority_issues()),
            'categories': self._get_category_breakdown()
        }
    
    def _get_category_breakdown(self) -> Dict[str, int]:
        """獲取問題類別分佈"""
        categories = {}
        
        all_issues = self.result.errors + self.result.warnings + self.result.suggestions
        for issue in all_issues:
            categories[issue.category] = categories.get(issue.category, 0) + 1
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        return categories


def validate_enhanced_configuration() -> ValidationResult:
    """執行增強配置驗證的主要入口點"""
    validator = EnhancedConfigValidator()
    return validator.validate_all()


def get_configuration_report() -> str:
    """獲取格式化的配置報告"""
    validator = EnhancedConfigValidator()
    result = validator.validate_all()
    summary = validator.generate_summary()
    priority_issues = validator.get_priority_issues()
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    report = []
    report.append("=" * 60)
    report.append("Django 配置完整性檢查報告")
    report.append("=" * 60)
    report.append("")
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    # 摘要信息
    report.append(f"🏥 總體健康分數: {summary['health_score']}/100")
    report.append(f"📊 問題統計:")
    report.append(f"   ❌ 錯誤: {summary['errors']}")
    report.append(f"   ⚠️  警告: {summary['warnings']}")
    report.append(f"   💡 建議: {summary['suggestions']}")
    report.append("")
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    # 優先處理問題
    if priority_issues:
        report.append("🚨 需要優先處理的問題:")
        for issue in priority_issues:
            report.append(f"   ❌ [{issue.category.upper()}] {issue.message}")
            if issue.fix_suggestion:
                report.append(f"      💊 解決方案: {issue.fix_suggestion}")
        report.append("")
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    # 分類別詳細問題
    if result.errors:
        report.append("❌ 錯誤詳情:")
        for error in result.errors:
            report.append(f"   • [{error.category}] {error.message}")
            if error.fix_suggestion:
                report.append(f"     💊 {error.fix_suggestion}")
        report.append("")
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    if result.warnings:
        report.append("⚠️ 警告詳情:")
        for warning in result.warnings:
            report.append(f"   • [{warning.category}] {warning.message}")
            if warning.fix_suggestion:
                report.append(f"     💊 {warning.fix_suggestion}")
        report.append("")
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    if result.suggestions:
        report.append("💡 改進建議:")
        for suggestion in result.suggestions:
            report.append(f"   • [{suggestion.category}] {suggestion.message}")
            if suggestion.fix_suggestion:
                report.append(f"     💊 {suggestion.fix_suggestion}")
        report.append("")
<<<<<<< HEAD

    # 類別分佈
    if summary["categories"]:
        report.append("📈 問題分佈:")
        for category, count in summary["categories"].items():
            report.append(f"   {category}: {count}")
        report.append("")

    report.append("=" * 60)
    report.append("檢查完成")
    report.append("=" * 60)

    return "\n".join(report)
=======
    
    # 類別分佈
    if summary['categories']:
        report.append("📈 問題分佈:")
        for category, count in summary['categories'].items():
            report.append(f"   {category}: {count}")
        report.append("")
    
    report.append("=" * 60)
    report.append("檢查完成")
    report.append("=" * 60)
    
    return "\n".join(report)
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
