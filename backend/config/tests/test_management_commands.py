"""
Django 管理命令測試 - Issue #184
==============================

測試 check_config 管理命令的各項功能
"""

import json
import tempfile
from io import StringIO
from pathlib import Path
from unittest.mock import patch, MagicMock

from django.test import TestCase, override_settings
from django.core.management import call_command
from django.core.management.base import CommandError

from config.enhanced_validation import ValidationResult, ValidationIssue


class CheckConfigCommandTest(TestCase):
    """測試 check_config 管理命令"""
<<<<<<< HEAD
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def setUp(self):
        """測試設置"""
        self.out = StringIO()
        self.err = StringIO()
<<<<<<< HEAD
<<<<<<< HEAD

    def test_basic_command_execution(self):
        """測試基本命令執行"""
        call_command("check_config", stdout=self.out, stderr=self.err)

        output = self.out.getvalue()
        self.assertIn("Django 配置完整性檢查報告", output)
        self.assertIn("健康分數", output)

    def test_summary_only_option(self):
        """測試只顯示摘要選項"""
        call_command("check_config", "--summary-only", stdout=self.out)

        output = self.out.getvalue()
        self.assertIn("健康分數", output)
        self.assertIn("總問題數", output)
        self.assertNotIn("詳情", output)

    def test_health_score_only_option(self):
        """測試只顯示健康分數選項"""
        call_command("check_config", "--health-score-only", stdout=self.out)

=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_basic_command_execution(self):
        """測試基本命令執行"""
        call_command("check_config", stdout=self.out, stderr=self.err)

        output = self.out.getvalue()
        self.assertIn("Django 配置完整性檢查報告", output)
        self.assertIn("健康分數", output)

    def test_summary_only_option(self):
        """測試只顯示摘要選項"""
        call_command("check_config", "--summary-only", stdout=self.out)

        output = self.out.getvalue()
        self.assertIn("健康分數", output)
        self.assertIn("總問題數", output)
        self.assertNotIn("詳情", output)

    def test_health_score_only_option(self):
        """測試只顯示健康分數選項"""
<<<<<<< HEAD
        call_command('check_config', '--health-score-only', stdout=self.out)
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        call_command("check_config", "--health-score-only", stdout=self.out)

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        output = self.out.getvalue().strip()
        # 輸出應該只是一個數字
        self.assertTrue(output.isdigit())
        health_score = int(output)
        self.assertGreaterEqual(health_score, 0)
        self.assertLessEqual(health_score, 100)
<<<<<<< HEAD
<<<<<<< HEAD

    def test_json_format_output(self):
        """測試 JSON 格式輸出"""
        call_command("check_config", "--format=json", stdout=self.out)

        output = self.out.getvalue()
        try:
            data = json.loads(output)
            self.assertIn("summary", data)
            self.assertIn("errors", data)
            self.assertIn("warnings", data)
            self.assertIn("suggestions", data)
            self.assertIn("priority_issues", data)
        except json.JSONDecodeError:
            self.fail("輸出不是有效的 JSON 格式")

    def test_output_to_file(self):
        """測試輸出到文件"""
        with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".txt") as f:
            temp_file = f.name

        try:
            call_command("check_config", f"--output={temp_file}", stdout=self.out)

            # 檢查文件是否創建並包含內容
            output_path = Path(temp_file)
            self.assertTrue(output_path.exists())

            content = output_path.read_text(encoding="utf-8")
            self.assertIn("Django 配置完整性檢查報告", content)

            # 檢查標準輸出包含成功消息
            stdout_content = self.out.getvalue()
            self.assertIn("輸出已保存到", stdout_content)

        finally:
            # 清理臨時文件
            Path(temp_file).unlink(missing_ok=True)

    def test_export_report_option(self):
        """測試導出報告選項"""
        # 確保報告文件不存在
        report_file = Path("config-check-report.json")
        report_file.unlink(missing_ok=True)

        try:
            call_command("check_config", "--export-report", stdout=self.out)

            # 檢查報告文件是否創建
            self.assertTrue(report_file.exists())

            # 檢查文件內容
            with open(report_file, "r", encoding="utf-8") as f:
                report_data = json.load(f)

            expected_keys = [
                "timestamp",
                "django_version",
                "settings_module",
                "debug_mode",
                "summary",
                "detailed_results",
                "priority_issues",
                "recommendations",
            ]

            for key in expected_keys:
                self.assertIn(key, report_data)

            # 檢查標準輸出包含成功消息
            stdout_content = self.out.getvalue()
            self.assertIn("詳細報告已導出到", stdout_content)

        finally:
            # 清理報告文件
            report_file.unlink(missing_ok=True)

    def test_category_filter(self):
        """測試類別過濾"""
        call_command(
            "check_config", "--category=security", "--format=json", stdout=self.out
        )

        output = self.out.getvalue()
        data = json.loads(output)

        # 檢查所有問題都屬於 security 類別
        all_issues = data["errors"] + data["warnings"] + data["suggestions"]
        for issue in all_issues:
            self.assertEqual(issue["category"], "security")

    def test_verbose_option(self):
        """測試詳細模式"""
        call_command("check_config", "--verbose", stdout=self.out)

        output = self.out.getvalue()
        self.assertIn("開始執行 Django 配置完整性檢查", output)
        self.assertIn("配置檢查完成", output)

    @patch("config.enhanced_validation.EnhancedConfigValidator.validate_all")
=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_json_format_output(self):
        """測試 JSON 格式輸出"""
        call_command("check_config", "--format=json", stdout=self.out)

        output = self.out.getvalue()
        try:
            data = json.loads(output)
            self.assertIn("summary", data)
            self.assertIn("errors", data)
            self.assertIn("warnings", data)
            self.assertIn("suggestions", data)
            self.assertIn("priority_issues", data)
        except json.JSONDecodeError:
            self.fail("輸出不是有效的 JSON 格式")

    def test_output_to_file(self):
        """測試輸出到文件"""
        with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".txt") as f:
            temp_file = f.name

        try:
            call_command("check_config", f"--output={temp_file}", stdout=self.out)

            # 檢查文件是否創建並包含內容
            output_path = Path(temp_file)
            self.assertTrue(output_path.exists())

            content = output_path.read_text(encoding="utf-8")
            self.assertIn("Django 配置完整性檢查報告", content)

            # 檢查標準輸出包含成功消息
            stdout_content = self.out.getvalue()
            self.assertIn("輸出已保存到", stdout_content)

        finally:
            # 清理臨時文件
            Path(temp_file).unlink(missing_ok=True)

    def test_export_report_option(self):
        """測試導出報告選項"""
        # 確保報告文件不存在
        report_file = Path("config-check-report.json")
        report_file.unlink(missing_ok=True)

        try:
            call_command("check_config", "--export-report", stdout=self.out)

            # 檢查報告文件是否創建
            self.assertTrue(report_file.exists())

            # 檢查文件內容
            with open(report_file, "r", encoding="utf-8") as f:
                report_data = json.load(f)

            expected_keys = [
                "timestamp",
                "django_version",
                "settings_module",
                "debug_mode",
                "summary",
                "detailed_results",
                "priority_issues",
                "recommendations",
            ]

            for key in expected_keys:
                self.assertIn(key, report_data)

            # 檢查標準輸出包含成功消息
            stdout_content = self.out.getvalue()
            self.assertIn("詳細報告已導出到", stdout_content)

        finally:
            # 清理報告文件
            report_file.unlink(missing_ok=True)

    def test_category_filter(self):
        """測試類別過濾"""
        call_command(
            "check_config", "--category=security", "--format=json", stdout=self.out
        )

        output = self.out.getvalue()
        data = json.loads(output)

        # 檢查所有問題都屬於 security 類別
        all_issues = data["errors"] + data["warnings"] + data["suggestions"]
        for issue in all_issues:
            self.assertEqual(issue["category"], "security")

    def test_verbose_option(self):
        """測試詳細模式"""
        call_command("check_config", "--verbose", stdout=self.out)

        output = self.out.getvalue()
<<<<<<< HEAD
        self.assertIn('開始執行 Django 配置完整性檢查', output)
        self.assertIn('配置檢查完成', output)
    
    @patch('config.enhanced_validation.EnhancedConfigValidator.validate_all')
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        self.assertIn("開始執行 Django 配置完整性檢查", output)
        self.assertIn("配置檢查完成", output)

    @patch("config.enhanced_validation.EnhancedConfigValidator.validate_all")
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_fail_on_error_with_errors(self, mock_validate):
        """測試在有錯誤時失敗退出"""
        # 模擬有錯誤的結果
        mock_result = ValidationResult()
<<<<<<< HEAD
<<<<<<< HEAD
        mock_result.errors = [ValidationIssue("error", "security", "測試錯誤")]
        mock_validate.return_value = mock_result

        with self.assertRaises(SystemExit) as cm:
            call_command(
                "check_config", "--fail-on-error", stdout=self.out, stderr=self.err
            )

        self.assertEqual(cm.exception.code, 1)

    @patch("config.enhanced_validation.EnhancedConfigValidator.validate_all")
=======
        mock_result.errors = [ValidationIssue('error', 'security', '測試錯誤')]
=======
        mock_result.errors = [ValidationIssue("error", "security", "測試錯誤")]
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        mock_validate.return_value = mock_result

        with self.assertRaises(SystemExit) as cm:
            call_command(
                "check_config", "--fail-on-error", stdout=self.out, stderr=self.err
            )

        self.assertEqual(cm.exception.code, 1)
<<<<<<< HEAD
    
    @patch('config.enhanced_validation.EnhancedConfigValidator.validate_all')
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    @patch("config.enhanced_validation.EnhancedConfigValidator.validate_all")
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_fail_on_error_without_errors(self, mock_validate):
        """測試沒有錯誤時正常退出"""
        # 模擬沒有錯誤的結果
        mock_result = ValidationResult()
<<<<<<< HEAD
<<<<<<< HEAD
        mock_result.warnings = [ValidationIssue("warning", "performance", "測試警告")]
        mock_validate.return_value = mock_result

        # 不應該拋出 SystemExit
        try:
            call_command(
                "check_config", "--fail-on-error", stdout=self.out, stderr=self.err
            )
        except SystemExit:
            self.fail("命令不應該在沒有錯誤時退出")

    @patch("config.enhanced_validation.EnhancedConfigValidator.validate_all")
    def test_exception_handling(self, mock_validate):
        """測試異常處理"""
        # 模擬驗證過程中的異常
        mock_validate.side_effect = Exception("測試異常")

        with self.assertRaises(CommandError):
            call_command("check_config", stdout=self.out, stderr=self.err)

        error_output = self.err.getvalue()
        self.assertIn("配置檢查執行失敗", error_output)

    @patch("config.enhanced_validation.EnhancedConfigValidator.validate_all")
    def test_exception_with_fail_on_error(self, mock_validate):
        """測試異常處理與失敗退出"""
        mock_validate.side_effect = Exception("測試異常")

        with self.assertRaises(SystemExit) as cm:
            call_command(
                "check_config", "--fail-on-error", stdout=self.out, stderr=self.err
            )

=======
        mock_result.warnings = [ValidationIssue('warning', 'performance', '測試警告')]
=======
        mock_result.warnings = [ValidationIssue("warning", "performance", "測試警告")]
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        mock_validate.return_value = mock_result

        # 不應該拋出 SystemExit
        try:
            call_command(
                "check_config", "--fail-on-error", stdout=self.out, stderr=self.err
            )
        except SystemExit:
            self.fail("命令不應該在沒有錯誤時退出")

    @patch("config.enhanced_validation.EnhancedConfigValidator.validate_all")
    def test_exception_handling(self, mock_validate):
        """測試異常處理"""
        # 模擬驗證過程中的異常
        mock_validate.side_effect = Exception("測試異常")

        with self.assertRaises(CommandError):
            call_command("check_config", stdout=self.out, stderr=self.err)

        error_output = self.err.getvalue()
        self.assertIn("配置檢查執行失敗", error_output)

    @patch("config.enhanced_validation.EnhancedConfigValidator.validate_all")
    def test_exception_with_fail_on_error(self, mock_validate):
        """測試異常處理與失敗退出"""
        mock_validate.side_effect = Exception("測試異常")

        with self.assertRaises(SystemExit) as cm:
<<<<<<< HEAD
            call_command('check_config', '--fail-on-error', stdout=self.out, stderr=self.err)
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
            call_command(
                "check_config", "--fail-on-error", stdout=self.out, stderr=self.err
            )

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        self.assertEqual(cm.exception.code, 1)


class CommandOptionsValidationTest(TestCase):
    """測試命令選項驗證"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.out = StringIO()
        self.err = StringIO()

    def test_invalid_format_option(self):
        """測試無效的格式選項"""
        with self.assertRaises(SystemExit):
            call_command(
                "check_config", "--format=invalid", stdout=self.out, stderr=self.err
            )

    def test_invalid_category_option(self):
        """測試無效的類別選項"""
        with self.assertRaises(SystemExit):
            call_command(
                "check_config", "--category=invalid", stdout=self.out, stderr=self.err
            )

=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def setUp(self):
        self.out = StringIO()
        self.err = StringIO()

    def test_invalid_format_option(self):
        """測試無效的格式選項"""
        with self.assertRaises(SystemExit):
            call_command(
                "check_config", "--format=invalid", stdout=self.out, stderr=self.err
            )

    def test_invalid_category_option(self):
        """測試無效的類別選項"""
        with self.assertRaises(SystemExit):
<<<<<<< HEAD
            call_command('check_config', '--category=invalid', stdout=self.out, stderr=self.err)
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
            call_command(
                "check_config", "--category=invalid", stdout=self.out, stderr=self.err
            )

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_conflicting_options(self):
        """測試衝突的選項"""
        # summary-only 和 health-score-only 不應該同時使用
        # 但命令本身不檢查這個衝突，只是後者會覆蓋前者
        call_command(
<<<<<<< HEAD
<<<<<<< HEAD
            "check_config", "--summary-only", "--health-score-only", stdout=self.out
        )

=======
            'check_config', 
            '--summary-only', 
            '--health-score-only',
            stdout=self.out
        )
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
            "check_config", "--summary-only", "--health-score-only", stdout=self.out
        )

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        output = self.out.getvalue().strip()
        # 應該只輸出健康分數
        self.assertTrue(output.isdigit())


class CommandOutputFormattingTest(TestCase):
    """測試命令輸出格式化"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.out = StringIO()

    @patch("config.enhanced_validation.EnhancedConfigValidator")
=======
    
    def setUp(self):
        self.out = StringIO()
    
    @patch('config.enhanced_validation.EnhancedConfigValidator')
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    def setUp(self):
        self.out = StringIO()

    @patch("config.enhanced_validation.EnhancedConfigValidator")
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_json_output_structure(self, mock_validator_class):
        """測試 JSON 輸出結構"""
        # 模擬驗證器和結果
        mock_validator = MagicMock()
        mock_validator_class.return_value = mock_validator
<<<<<<< HEAD
<<<<<<< HEAD

        mock_result = ValidationResult()
        mock_result.errors = [
            ValidationIssue("error", "security", "測試錯誤", "修復建議", "TEST_SETTING")
        ]
        mock_result.warnings = [ValidationIssue("warning", "performance", "測試警告")]
        mock_result.suggestions = [ValidationIssue("suggestion", "api", "測試建議")]

        mock_validator.validate_all.return_value = mock_result
        mock_validator.generate_summary.return_value = {
            "total_issues": 3,
            "errors": 1,
            "warnings": 1,
            "suggestions": 1,
            "health_score": 85,
            "priority_issues_count": 1,
            "categories": {"security": 1, "performance": 1, "api": 1},
        }
        mock_validator.get_priority_issues.return_value = [mock_result.errors[0]]

        call_command("check_config", "--format=json", stdout=self.out)

        output = self.out.getvalue()
        data = json.loads(output)

        # 檢查頂級結構
        self.assertIn("summary", data)
        self.assertIn("errors", data)
        self.assertIn("warnings", data)
        self.assertIn("suggestions", data)
        self.assertIn("priority_issues", data)

        # 檢查摘要結構
        summary = data["summary"]
        self.assertEqual(summary["total_issues"], 3)
        self.assertEqual(summary["health_score"], 85)

        # 檢查問題結構
        self.assertEqual(len(data["errors"]), 1)
        error = data["errors"][0]
        self.assertEqual(error["level"], "error")
        self.assertEqual(error["category"], "security")
        self.assertEqual(error["message"], "測試錯誤")
        self.assertEqual(error["fix_suggestion"], "修復建議")
        self.assertEqual(error["setting_name"], "TEST_SETTING")

    @patch("config.enhanced_validation.EnhancedConfigValidator")
=======
        
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        mock_result = ValidationResult()
        mock_result.errors = [
            ValidationIssue("error", "security", "測試錯誤", "修復建議", "TEST_SETTING")
        ]
        mock_result.warnings = [ValidationIssue("warning", "performance", "測試警告")]
        mock_result.suggestions = [ValidationIssue("suggestion", "api", "測試建議")]

        mock_validator.validate_all.return_value = mock_result
        mock_validator.generate_summary.return_value = {
            "total_issues": 3,
            "errors": 1,
            "warnings": 1,
            "suggestions": 1,
            "health_score": 85,
            "priority_issues_count": 1,
            "categories": {"security": 1, "performance": 1, "api": 1},
        }
        mock_validator.get_priority_issues.return_value = [mock_result.errors[0]]

        call_command("check_config", "--format=json", stdout=self.out)

        output = self.out.getvalue()
        data = json.loads(output)

        # 檢查頂級結構
        self.assertIn("summary", data)
        self.assertIn("errors", data)
        self.assertIn("warnings", data)
        self.assertIn("suggestions", data)
        self.assertIn("priority_issues", data)

        # 檢查摘要結構
        summary = data["summary"]
        self.assertEqual(summary["total_issues"], 3)
        self.assertEqual(summary["health_score"], 85)

        # 檢查問題結構
<<<<<<< HEAD
        self.assertEqual(len(data['errors']), 1)
        error = data['errors'][0]
        self.assertEqual(error['level'], 'error')
        self.assertEqual(error['category'], 'security')
        self.assertEqual(error['message'], '測試錯誤')
        self.assertEqual(error['fix_suggestion'], '修復建議')
        self.assertEqual(error['setting_name'], 'TEST_SETTING')
    
    @patch('config.enhanced_validation.EnhancedConfigValidator')
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        self.assertEqual(len(data["errors"]), 1)
        error = data["errors"][0]
        self.assertEqual(error["level"], "error")
        self.assertEqual(error["category"], "security")
        self.assertEqual(error["message"], "測試錯誤")
        self.assertEqual(error["fix_suggestion"], "修復建議")
        self.assertEqual(error["setting_name"], "TEST_SETTING")

    @patch("config.enhanced_validation.EnhancedConfigValidator")
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_text_output_formatting(self, mock_validator_class):
        """測試文字輸出格式化"""
        mock_validator = MagicMock()
        mock_validator_class.return_value = mock_validator
<<<<<<< HEAD
<<<<<<< HEAD

=======
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        # 模擬 get_configuration_report 返回
        mock_report = """
========================================
Django 配置完整性檢查報告
========================================

🏥 總體健康分數: 85/100
📊 問題統計:
   ❌ 錯誤: 1
   ⚠️  警告: 1
   💡 建議: 1

❌ 錯誤詳情:
   • [security] 測試錯誤
     💊 修復建議

========================================
檢查完成
========================================
        """.strip()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        with patch(
            "config.enhanced_validation.get_configuration_report",
            return_value=mock_report,
        ):
            call_command("check_config", stdout=self.out)

<<<<<<< HEAD
        output = self.out.getvalue()
        self.assertIn("Django 配置完整性檢查報告", output)
        self.assertIn("健康分數: 85/100", output)
        self.assertIn("錯誤: 1", output)
=======
        
        with patch('config.enhanced_validation.get_configuration_report', return_value=mock_report):
            call_command('check_config', stdout=self.out)
        
        output = self.out.getvalue()
        self.assertIn('Django 配置完整性檢查報告', output)
        self.assertIn('健康分數: 85/100', output)
        self.assertIn('錯誤: 1', output)
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        output = self.out.getvalue()
        self.assertIn("Django 配置完整性檢查報告", output)
        self.assertIn("健康分數: 85/100", output)
        self.assertIn("錯誤: 1", output)
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))


class CommandIntegrationTest(TestCase):
    """命令集成測試"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.out = StringIO()

    def test_real_configuration_check(self):
        """測試真實配置檢查"""
        # 這個測試使用真實的配置檢查，不使用模擬
        call_command("check_config", "--verbose", stdout=self.out)

        output = self.out.getvalue()

        # 基本輸出檢查
        self.assertIn("開始執行 Django 配置完整性檢查", output)
        self.assertIn("配置檢查完成", output)
        self.assertIn("Django 配置完整性檢查報告", output)
        self.assertIn("健康分數", output)

    def test_json_output_validity(self):
        """測試 JSON 輸出有效性"""
        call_command("check_config", "--format=json", stdout=self.out)

        output = self.out.getvalue()

=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def setUp(self):
        self.out = StringIO()

    def test_real_configuration_check(self):
        """測試真實配置檢查"""
        # 這個測試使用真實的配置檢查，不使用模擬
        call_command("check_config", "--verbose", stdout=self.out)

        output = self.out.getvalue()

        # 基本輸出檢查
        self.assertIn("開始執行 Django 配置完整性檢查", output)
        self.assertIn("配置檢查完成", output)
        self.assertIn("Django 配置完整性檢查報告", output)
        self.assertIn("健康分數", output)

    def test_json_output_validity(self):
        """測試 JSON 輸出有效性"""
        call_command("check_config", "--format=json", stdout=self.out)

        output = self.out.getvalue()
<<<<<<< HEAD
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        # 驗證 JSON 格式
        try:
            data = json.loads(output)
        except json.JSONDecodeError as e:
<<<<<<< HEAD
<<<<<<< HEAD
            self.fail(f"JSON 輸出無效: {e}")

        # 驗證必需的字段
        required_fields = [
            "summary",
            "errors",
            "warnings",
            "suggestions",
            "priority_issues",
        ]
        for field in required_fields:
            self.assertIn(field, data, f"缺少必需字段: {field}")

        # 驗證摘要字段
        summary_fields = [
            "total_issues",
            "errors",
            "warnings",
            "suggestions",
            "health_score",
        ]
        for field in summary_fields:
            self.assertIn(field, data["summary"], f"摘要中缺少字段: {field}")

    def test_category_filtering_effectiveness(self):
        """測試類別過濾效果"""
        # 測試不同類別過濾
        categories = ["security", "performance", "media", "logging", "api"]

        for category in categories:
            with self.subTest(category=category):
                out = StringIO()

                call_command(
                    "check_config",
                    f"--category={category}",
                    "--format=json",
                    stdout=out,
                )

                output = out.getvalue()
                data = json.loads(output)

                # 檢查所有問題都屬於指定類別
                all_issues = data["errors"] + data["warnings"] + data["suggestions"]
                for issue in all_issues:
                    self.assertEqual(
                        issue["category"],
                        category,
                        f"發現不屬於 {category} 類別的問題: {issue}",
=======
            self.fail(f'JSON 輸出無效: {e}')
        
=======
            self.fail(f"JSON 輸出無效: {e}")

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        # 驗證必需的字段
        required_fields = [
            "summary",
            "errors",
            "warnings",
            "suggestions",
            "priority_issues",
        ]
        for field in required_fields:
            self.assertIn(field, data, f"缺少必需字段: {field}")

        # 驗證摘要字段
        summary_fields = [
            "total_issues",
            "errors",
            "warnings",
            "suggestions",
            "health_score",
        ]
        for field in summary_fields:
            self.assertIn(field, data["summary"], f"摘要中缺少字段: {field}")

    def test_category_filtering_effectiveness(self):
        """測試類別過濾效果"""
        # 測試不同類別過濾
        categories = ["security", "performance", "media", "logging", "api"]

        for category in categories:
            with self.subTest(category=category):
                out = StringIO()

                call_command(
                    "check_config",
                    f"--category={category}",
                    "--format=json",
                    stdout=out,
                )

                output = out.getvalue()
                data = json.loads(output)

                # 檢查所有問題都屬於指定類別
                all_issues = data["errors"] + data["warnings"] + data["suggestions"]
                for issue in all_issues:
                    self.assertEqual(
                        issue["category"],
                        category,
<<<<<<< HEAD
                        f'發現不屬於 {category} 類別的問題: {issue}'
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
                        f"發現不屬於 {category} 類別的問題: {issue}",
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
                    )


class CommandErrorHandlingTest(TestCase):
    """命令錯誤處理測試"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.out = StringIO()
        self.err = StringIO()

=======
    
    def setUp(self):
        self.out = StringIO()
        self.err = StringIO()
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    def setUp(self):
        self.out = StringIO()
        self.err = StringIO()

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_output_file_permission_error(self):
        """測試輸出文件權限錯誤"""
        # 嘗試寫入一個不可寫的位置（如果在 Unix 系統上）
        import os
<<<<<<< HEAD
<<<<<<< HEAD

        if os.name == "posix":  # Unix-like systems
            with self.assertRaises(CommandError):
                call_command(
                    "check_config",
                    "--output=/root/no-permission.txt",
                    stdout=self.out,
                    stderr=self.err,
                )

    @patch("builtins.open")
    def test_export_report_file_error(self, mock_open):
        """測試導出報告文件錯誤"""
        mock_open.side_effect = PermissionError("無法寫入文件")

        with self.assertRaises(CommandError):
            call_command(
                "check_config", "--export-report", stdout=self.out, stderr=self.err
            )

    @patch("json.dumps")
    def test_json_serialization_error(self, mock_dumps):
        """測試 JSON 序列化錯誤"""
        mock_dumps.side_effect = TypeError("無法序列化對象")

        with self.assertRaises(CommandError):
            call_command(
                "check_config", "--format=json", stdout=self.out, stderr=self.err
            )
=======
        if os.name == 'posix':  # Unix-like systems
=======

        if os.name == "posix":  # Unix-like systems
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
            with self.assertRaises(CommandError):
                call_command(
                    "check_config",
                    "--output=/root/no-permission.txt",
                    stdout=self.out,
                    stderr=self.err,
                )

    @patch("builtins.open")
    def test_export_report_file_error(self, mock_open):
        """測試導出報告文件錯誤"""
        mock_open.side_effect = PermissionError("無法寫入文件")

        with self.assertRaises(CommandError):
            call_command(
                "check_config", "--export-report", stdout=self.out, stderr=self.err
            )

    @patch("json.dumps")
    def test_json_serialization_error(self, mock_dumps):
        """測試 JSON 序列化錯誤"""
        mock_dumps.side_effect = TypeError("無法序列化對象")

        with self.assertRaises(CommandError):
<<<<<<< HEAD
            call_command('check_config', '--format=json', stdout=self.out, stderr=self.err)
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
            call_command(
                "check_config", "--format=json", stdout=self.out, stderr=self.err
            )
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
