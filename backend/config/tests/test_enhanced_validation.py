"""
Django 配置增強驗證測試 - Issue #184
===============================

測試 EnhancedConfigValidator 的各項功能
"""

import os
import tempfile
from unittest.mock import patch, MagicMock
from pathlib import Path

from django.test import TestCase, override_settings
from django.core.exceptions import ImproperlyConfigured

from config.enhanced_validation import (
    EnhancedConfigValidator,
    ValidationResult,
    ValidationIssue,
    validate_enhanced_configuration,
<<<<<<< HEAD
<<<<<<< HEAD
    get_configuration_report,
=======
    get_configuration_report
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
    get_configuration_report,
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
)


class ValidationIssueTest(TestCase):
    """測試 ValidationIssue 數據類"""
<<<<<<< HEAD
<<<<<<< HEAD

    def test_validation_issue_creation(self):
        """測試 ValidationIssue 基本創建"""
        issue = ValidationIssue(
            level="error",
            category="security",
            message="測試錯誤消息",
            fix_suggestion="修復建議",
            setting_name="TEST_SETTING",
        )

        self.assertEqual(issue.level, "error")
        self.assertEqual(issue.category, "security")
        self.assertEqual(issue.message, "測試錯誤消息")
        self.assertEqual(issue.fix_suggestion, "修復建議")
        self.assertEqual(issue.setting_name, "TEST_SETTING")

    def test_validation_issue_optional_fields(self):
        """測試 ValidationIssue 可選字段"""
        issue = ValidationIssue(
            level="warning", category="performance", message="測試警告"
        )

=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_validation_issue_creation(self):
        """測試 ValidationIssue 基本創建"""
        issue = ValidationIssue(
            level="error",
            category="security",
            message="測試錯誤消息",
            fix_suggestion="修復建議",
            setting_name="TEST_SETTING",
        )

        self.assertEqual(issue.level, "error")
        self.assertEqual(issue.category, "security")
        self.assertEqual(issue.message, "測試錯誤消息")
        self.assertEqual(issue.fix_suggestion, "修復建議")
        self.assertEqual(issue.setting_name, "TEST_SETTING")

    def test_validation_issue_optional_fields(self):
        """測試 ValidationIssue 可選字段"""
        issue = ValidationIssue(
            level="warning", category="performance", message="測試警告"
        )
<<<<<<< HEAD
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        self.assertIsNone(issue.fix_suggestion)
        self.assertIsNone(issue.setting_name)


class ValidationResultTest(TestCase):
    """測試 ValidationResult 數據類"""
<<<<<<< HEAD
<<<<<<< HEAD

    def test_empty_result(self):
        """測試空結果"""
        result = ValidationResult()

        self.assertFalse(result.has_errors)
        self.assertFalse(result.has_warnings)
        self.assertEqual(result.total_issues, 0)

    def test_result_with_issues(self):
        """測試有問題的結果"""
        result = ValidationResult()

        error = ValidationIssue("error", "security", "錯誤")
        warning = ValidationIssue("warning", "performance", "警告")
        suggestion = ValidationIssue("suggestion", "api", "建議")

        result.errors.append(error)
        result.warnings.append(warning)
        result.suggestions.append(suggestion)

=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_empty_result(self):
        """測試空結果"""
        result = ValidationResult()

        self.assertFalse(result.has_errors)
        self.assertFalse(result.has_warnings)
        self.assertEqual(result.total_issues, 0)

    def test_result_with_issues(self):
        """測試有問題的結果"""
        result = ValidationResult()

        error = ValidationIssue("error", "security", "錯誤")
        warning = ValidationIssue("warning", "performance", "警告")
        suggestion = ValidationIssue("suggestion", "api", "建議")

        result.errors.append(error)
        result.warnings.append(warning)
        result.suggestions.append(suggestion)
<<<<<<< HEAD
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        self.assertTrue(result.has_errors)
        self.assertTrue(result.has_warnings)
        self.assertEqual(result.total_issues, 3)


class EnhancedConfigValidatorTest(TestCase):
    """測試 EnhancedConfigValidator 主要功能"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        """測試設置"""
        self.validator = EnhancedConfigValidator()

=======
    
    def setUp(self):
        """測試設置"""
        self.validator = EnhancedConfigValidator()
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    def setUp(self):
        """測試設置"""
        self.validator = EnhancedConfigValidator()

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_validator_initialization(self):
        """測試驗證器初始化"""
        self.assertIsInstance(self.validator.result, ValidationResult)
        self.assertEqual(self.validator.result.total_issues, 0)
<<<<<<< HEAD
<<<<<<< HEAD

    def test_add_issue_error(self):
        """測試添加錯誤問題"""
        self.validator._add_issue(
            "error", "security", "測試錯誤", "修復建議", "TEST_SETTING"
        )

        self.assertEqual(len(self.validator.result.errors), 1)
        self.assertEqual(len(self.validator.result.warnings), 0)
        self.assertEqual(len(self.validator.result.suggestions), 0)

        error = self.validator.result.errors[0]
        self.assertEqual(error.level, "error")
        self.assertEqual(error.category, "security")
        self.assertEqual(error.message, "測試錯誤")

    def test_add_issue_warning(self):
        """測試添加警告問題"""
        self.validator._add_issue("warning", "performance", "測試警告")

        self.assertEqual(len(self.validator.result.warnings), 1)
        self.assertEqual(len(self.validator.result.errors), 0)

    def test_add_issue_suggestion(self):
        """測試添加建議問題"""
        self.validator._add_issue("suggestion", "api", "測試建議")

=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_add_issue_error(self):
        """測試添加錯誤問題"""
        self.validator._add_issue(
            "error", "security", "測試錯誤", "修復建議", "TEST_SETTING"
        )

        self.assertEqual(len(self.validator.result.errors), 1)
        self.assertEqual(len(self.validator.result.warnings), 0)
        self.assertEqual(len(self.validator.result.suggestions), 0)

        error = self.validator.result.errors[0]
        self.assertEqual(error.level, "error")
        self.assertEqual(error.category, "security")
        self.assertEqual(error.message, "測試錯誤")

    def test_add_issue_warning(self):
        """測試添加警告問題"""
        self.validator._add_issue("warning", "performance", "測試警告")

        self.assertEqual(len(self.validator.result.warnings), 1)
        self.assertEqual(len(self.validator.result.errors), 0)

    def test_add_issue_suggestion(self):
        """測試添加建議問題"""
<<<<<<< HEAD
        self.validator._add_issue('suggestion', 'api', '測試建議')
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        self.validator._add_issue("suggestion", "api", "測試建議")

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        self.assertEqual(len(self.validator.result.suggestions), 1)
        self.assertEqual(len(self.validator.result.errors), 0)


class MediaStaticConfigTest(TestCase):
    """測試媒體和靜態文件配置檢查"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.validator = EnhancedConfigValidator()

    @override_settings(MEDIA_ROOT="")
    def test_missing_media_root(self):
        """測試缺少 MEDIA_ROOT"""
        self.validator._check_media_and_static()

        errors = [e for e in self.validator.result.errors if e.category == "media"]
        self.assertTrue(any("MEDIA_ROOT 未設置" in e.message for e in errors))

    @override_settings(MEDIA_ROOT="relative/path")
    def test_relative_media_root(self):
        """測試相對路徑 MEDIA_ROOT"""
        self.validator._check_media_and_static()

        warnings = [w for w in self.validator.result.warnings if w.category == "media"]
        self.assertTrue(any("不是絕對路徑" in w.message for w in warnings))

    @override_settings(MEDIA_URL="")
    def test_missing_media_url(self):
        """測試缺少 MEDIA_URL"""
        self.validator._check_media_and_static()

        errors = [e for e in self.validator.result.errors if e.category == "media"]
        self.assertTrue(any("MEDIA_URL 未設置" in e.message for e in errors))

    @override_settings(MEDIA_URL="/media")
    def test_media_url_without_trailing_slash(self):
        """測試 MEDIA_URL 沒有結尾斜線"""
        self.validator._check_media_and_static()

        suggestions = [
            s for s in self.validator.result.suggestions if s.category == "media"
        ]
        self.assertTrue(any('應該以 "/" 結尾' in s.message for s in suggestions))

    @override_settings(DEBUG=False, STATIC_ROOT="")
    def test_missing_static_root_in_production(self):
        """測試生產環境缺少 STATIC_ROOT"""
        self.validator._check_media_and_static()

        errors = [e for e in self.validator.result.errors if e.category == "static"]
        self.assertTrue(any("生產環境未設置 STATIC_ROOT" in e.message for e in errors))

    @override_settings(FILE_UPLOAD_MAX_MEMORY_SIZE=20 * 1024 * 1024)  # 20MB
    def test_large_upload_size(self):
        """測試過大的文件上傳限制"""
        self.validator._check_media_and_static()

        warnings = [w for w in self.validator.result.warnings if w.category == "media"]
        self.assertTrue(
            any("FILE_UPLOAD_MAX_MEMORY_SIZE 過大" in w.message for w in warnings)
        )
=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def setUp(self):
        self.validator = EnhancedConfigValidator()

    @override_settings(MEDIA_ROOT="")
    def test_missing_media_root(self):
        """測試缺少 MEDIA_ROOT"""
        self.validator._check_media_and_static()

        errors = [e for e in self.validator.result.errors if e.category == "media"]
        self.assertTrue(any("MEDIA_ROOT 未設置" in e.message for e in errors))

    @override_settings(MEDIA_ROOT="relative/path")
    def test_relative_media_root(self):
        """測試相對路徑 MEDIA_ROOT"""
        self.validator._check_media_and_static()

        warnings = [w for w in self.validator.result.warnings if w.category == "media"]
        self.assertTrue(any("不是絕對路徑" in w.message for w in warnings))

    @override_settings(MEDIA_URL="")
    def test_missing_media_url(self):
        """測試缺少 MEDIA_URL"""
        self.validator._check_media_and_static()

        errors = [e for e in self.validator.result.errors if e.category == "media"]
        self.assertTrue(any("MEDIA_URL 未設置" in e.message for e in errors))

    @override_settings(MEDIA_URL="/media")
    def test_media_url_without_trailing_slash(self):
        """測試 MEDIA_URL 沒有結尾斜線"""
        self.validator._check_media_and_static()

        suggestions = [
            s for s in self.validator.result.suggestions if s.category == "media"
        ]
        self.assertTrue(any('應該以 "/" 結尾' in s.message for s in suggestions))

    @override_settings(DEBUG=False, STATIC_ROOT="")
    def test_missing_static_root_in_production(self):
        """測試生產環境缺少 STATIC_ROOT"""
        self.validator._check_media_and_static()

        errors = [e for e in self.validator.result.errors if e.category == "static"]
        self.assertTrue(any("生產環境未設置 STATIC_ROOT" in e.message for e in errors))

    @override_settings(FILE_UPLOAD_MAX_MEMORY_SIZE=20 * 1024 * 1024)  # 20MB
    def test_large_upload_size(self):
        """測試過大的文件上傳限制"""
        self.validator._check_media_and_static()
<<<<<<< HEAD
        
        warnings = [w for w in self.validator.result.warnings if w.category == 'media']
        self.assertTrue(any('FILE_UPLOAD_MAX_MEMORY_SIZE 過大' in w.message for w in warnings))
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        warnings = [w for w in self.validator.result.warnings if w.category == "media"]
        self.assertTrue(
            any("FILE_UPLOAD_MAX_MEMORY_SIZE 過大" in w.message for w in warnings)
        )
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))


class LoggingConfigTest(TestCase):
    """測試日誌配置檢查"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.validator = EnhancedConfigValidator()

=======
    
    def setUp(self):
        self.validator = EnhancedConfigValidator()
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    def setUp(self):
        self.validator = EnhancedConfigValidator()

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @override_settings(LOGGING={})
    def test_empty_logging_config(self):
        """測試空日誌配置"""
        self.validator._check_logging_config()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        errors = [e for e in self.validator.result.errors if e.category == "logging"]
        self.assertTrue(any("缺少 LOGGING 配置" in e.message for e in errors))

    @override_settings(LOGGING={"handlers": {}})
<<<<<<< HEAD
    def test_missing_handlers(self):
        """測試缺少處理器"""
        self.validator._check_logging_config()

        warnings = [
            w for w in self.validator.result.warnings if w.category == "logging"
        ]
        self.assertTrue(any("缺少處理器" in w.message for w in warnings))

    @override_settings(
        DEBUG=False,
        LOGGING={"handlers": {"console": {"class": "logging.StreamHandler"}}},
=======
        
        errors = [e for e in self.validator.result.errors if e.category == 'logging']
        self.assertTrue(any('缺少 LOGGING 配置' in e.message for e in errors))
    
    @override_settings(LOGGING={'handlers': {}})
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_missing_handlers(self):
        """測試缺少處理器"""
        self.validator._check_logging_config()

        warnings = [
            w for w in self.validator.result.warnings if w.category == "logging"
        ]
        self.assertTrue(any("缺少處理器" in w.message for w in warnings))

    @override_settings(
        DEBUG=False,
<<<<<<< HEAD
        LOGGING={
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler'
                }
            }
        }
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        LOGGING={"handlers": {"console": {"class": "logging.StreamHandler"}}},
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    )
    def test_missing_file_handler_in_production(self):
        """測試生產環境缺少文件處理器"""
        self.validator._check_logging_config()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        warnings = [
            w for w in self.validator.result.warnings if w.category == "logging"
        ]
        self.assertTrue(any("建議配置文件日誌處理器" in w.message for w in warnings))

<<<<<<< HEAD
    @override_settings(
        DEBUG=False,
        LOGGING={
            "root": {"level": "DEBUG"},
            "handlers": {"console": {"class": "logging.StreamHandler"}},
        },
=======
        
        warnings = [w for w in self.validator.result.warnings if w.category == 'logging']
        self.assertTrue(any('建議配置文件日誌處理器' in w.message for w in warnings))
    
    @override_settings(
        DEBUG=False,
        LOGGING={
            'root': {'level': 'DEBUG'},
            'handlers': {'console': {'class': 'logging.StreamHandler'}}
        }
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
    @override_settings(
        DEBUG=False,
        LOGGING={
            "root": {"level": "DEBUG"},
            "handlers": {"console": {"class": "logging.StreamHandler"}},
        },
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    )
    def test_debug_level_in_production(self):
        """測試生產環境使用 DEBUG 級別"""
        self.validator._check_logging_config()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        warnings = [
            w for w in self.validator.result.warnings if w.category == "logging"
        ]
        self.assertTrue(
            any("生產環境使用 DEBUG 日誌級別" in w.message for w in warnings)
        )
<<<<<<< HEAD
=======
        
        warnings = [w for w in self.validator.result.warnings if w.category == 'logging']
        self.assertTrue(any('生產環境使用 DEBUG 日誌級別' in w.message for w in warnings))
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))


class SecurityConfigTest(TestCase):
    """測試安全配置檢查"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.validator = EnhancedConfigValidator()

=======
    
    def setUp(self):
        self.validator = EnhancedConfigValidator()
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    def setUp(self):
        self.validator = EnhancedConfigValidator()

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @override_settings(DEBUG=False, SECURE_SSL_REDIRECT=False)
    def test_missing_ssl_redirect(self):
        """測試缺少 SSL 重定向"""
        self.validator._check_security_enhanced()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        errors = [e for e in self.validator.result.errors if e.category == "security"]
        self.assertTrue(any("未強制 HTTPS" in e.message for e in errors))

<<<<<<< HEAD
=======
        
        errors = [e for e in self.validator.result.errors if e.category == 'security']
        self.assertTrue(any('未強制 HTTPS' in e.message for e in errors))
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @override_settings(DEBUG=False, SECURE_HSTS_SECONDS=0)
    def test_missing_hsts(self):
        """測試缺少 HSTS"""
        self.validator._check_security_enhanced()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        warnings = [
            w for w in self.validator.result.warnings if w.category == "security"
        ]
        self.assertTrue(any("未設置 HSTS" in w.message for w in warnings))

<<<<<<< HEAD
=======
        
        warnings = [w for w in self.validator.result.warnings if w.category == 'security']
        self.assertTrue(any('未設置 HSTS' in w.message for w in warnings))
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @override_settings(DEBUG=False, SECURE_HSTS_SECONDS=3600)  # 1 hour
    def test_short_hsts_duration(self):
        """測試 HSTS 持續時間過短"""
        self.validator._check_security_enhanced()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        suggestions = [
            s for s in self.validator.result.suggestions if s.category == "security"
        ]
        self.assertTrue(any("HSTS 持續時間較短" in s.message for s in suggestions))

<<<<<<< HEAD
=======
        
        suggestions = [s for s in self.validator.result.suggestions if s.category == 'security']
        self.assertTrue(any('HSTS 持續時間較短' in s.message for s in suggestions))
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @override_settings(DEBUG=False, SESSION_COOKIE_SECURE=False)
    def test_insecure_session_cookie(self):
        """測試不安全的 Session Cookie"""
        self.validator._check_security_enhanced()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        errors = [e for e in self.validator.result.errors if e.category == "security"]
        self.assertTrue(
            any("Session Cookie 未設置 Secure 標記" in e.message for e in errors)
        )

<<<<<<< HEAD
=======
        
        errors = [e for e in self.validator.result.errors if e.category == 'security']
        self.assertTrue(any('Session Cookie 未設置 Secure 標記' in e.message for e in errors))
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @override_settings(SESSION_COOKIE_HTTPONLY=False)
    def test_session_cookie_not_httponly(self):
        """測試 Session Cookie 不是 HttpOnly"""
        self.validator._check_security_enhanced()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        errors = [e for e in self.validator.result.errors if e.category == "security"]
        self.assertTrue(
            any("Session Cookie 未設置 HttpOnly 標記" in e.message for e in errors)
        )

<<<<<<< HEAD
=======
        
        errors = [e for e in self.validator.result.errors if e.category == 'security']
        self.assertTrue(any('Session Cookie 未設置 HttpOnly 標記' in e.message for e in errors))
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @override_settings(AUTH_PASSWORD_VALIDATORS=[])
    def test_missing_password_validators(self):
        """測試缺少密碼驗證器"""
        self.validator._check_security_enhanced()
<<<<<<< HEAD
<<<<<<< HEAD

        errors = [e for e in self.validator.result.errors if e.category == "security"]
        self.assertTrue(any("缺少密碼驗證器" in e.message for e in errors))
=======
        
        errors = [e for e in self.validator.result.errors if e.category == 'security']
        self.assertTrue(any('缺少密碼驗證器' in e.message for e in errors))
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        errors = [e for e in self.validator.result.errors if e.category == "security"]
        self.assertTrue(any("缺少密碼驗證器" in e.message for e in errors))
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))


class PerformanceConfigTest(TestCase):
    """測試性能配置檢查"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.validator = EnhancedConfigValidator()

    @override_settings(DATABASES={"default": {"CONN_MAX_AGE": 0}})
    def test_no_connection_pooling(self):
        """測試沒有連接池"""
        self.validator._check_performance_settings()

        suggestions = [
            s for s in self.validator.result.suggestions if s.category == "performance"
        ]
        self.assertTrue(any("數據庫連接未啟用持久化" in s.message for s in suggestions))

    @override_settings(DATABASES={"default": {"CONN_MAX_AGE": 7200}})  # 2 hours
    def test_long_connection_age(self):
        """測試連接時間過長"""
        self.validator._check_performance_settings()

        warnings = [
            w for w in self.validator.result.warnings if w.category == "performance"
        ]
        self.assertTrue(any("數據庫連接持久化時間過長" in w.message for w in warnings))

    @override_settings(
        CACHES={"default": {"BACKEND": "django.core.cache.backends.dummy.DummyCache"}}
=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def setUp(self):
        self.validator = EnhancedConfigValidator()

    @override_settings(DATABASES={"default": {"CONN_MAX_AGE": 0}})
    def test_no_connection_pooling(self):
        """測試沒有連接池"""
        self.validator._check_performance_settings()

        suggestions = [
            s for s in self.validator.result.suggestions if s.category == "performance"
        ]
        self.assertTrue(any("數據庫連接未啟用持久化" in s.message for s in suggestions))

    @override_settings(DATABASES={"default": {"CONN_MAX_AGE": 7200}})  # 2 hours
    def test_long_connection_age(self):
        """測試連接時間過長"""
        self.validator._check_performance_settings()

        warnings = [
            w for w in self.validator.result.warnings if w.category == "performance"
        ]
        self.assertTrue(any("數據庫連接持久化時間過長" in w.message for w in warnings))

    @override_settings(
<<<<<<< HEAD
        CACHES={
            'default': {
                'BACKEND': 'django.core.cache.backends.dummy.DummyCache'
            }
        }
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        CACHES={"default": {"BACKEND": "django.core.cache.backends.dummy.DummyCache"}}
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    )
    def test_dummy_cache_backend(self):
        """測試使用 DummyCache 後端"""
        self.validator._check_performance_settings()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        warnings = [
            w for w in self.validator.result.warnings if w.category == "performance"
        ]
        self.assertTrue(any("使用 DummyCache 後端" in w.message for w in warnings))

<<<<<<< HEAD
=======
        
        warnings = [w for w in self.validator.result.warnings if w.category == 'performance']
        self.assertTrue(any('使用 DummyCache 後端' in w.message for w in warnings))
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    @override_settings(MIDDLEWARE=[])
    def test_missing_security_middleware(self):
        """測試缺少安全中間件"""
        self.validator._check_performance_settings()
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))

        warnings = [
            w for w in self.validator.result.warnings if w.category == "security"
        ]
        self.assertTrue(any("缺少 SecurityMiddleware" in w.message for w in warnings))
<<<<<<< HEAD
=======
        
        warnings = [w for w in self.validator.result.warnings if w.category == 'security']
        self.assertTrue(any('缺少 SecurityMiddleware' in w.message for w in warnings))
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))


class APIConfigTest(TestCase):
    """測試 API 配置檢查"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.validator = EnhancedConfigValidator()

    @override_settings(INSTALLED_APPS=["rest_framework"], REST_FRAMEWORK={})
    def test_empty_rest_framework_config(self):
        """測試空的 REST Framework 配置"""
        self.validator._check_api_configuration()

        warnings = [w for w in self.validator.result.warnings if w.category == "api"]
        auth_warnings = [w for w in warnings if "認證類別" in w.message]
        perm_warnings = [w for w in warnings if "權限配置" in w.message]

        self.assertTrue(len(auth_warnings) > 0)
        self.assertTrue(len(perm_warnings) > 0)

    @override_settings(
        DEBUG=False, INSTALLED_APPS=["corsheaders"], CORS_ALLOW_ALL_ORIGINS=True
=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def setUp(self):
        self.validator = EnhancedConfigValidator()

    @override_settings(INSTALLED_APPS=["rest_framework"], REST_FRAMEWORK={})
    def test_empty_rest_framework_config(self):
        """測試空的 REST Framework 配置"""
        self.validator._check_api_configuration()

        warnings = [w for w in self.validator.result.warnings if w.category == "api"]
        auth_warnings = [w for w in warnings if "認證類別" in w.message]
        perm_warnings = [w for w in warnings if "權限配置" in w.message]

        self.assertTrue(len(auth_warnings) > 0)
        self.assertTrue(len(perm_warnings) > 0)

    @override_settings(
<<<<<<< HEAD
        DEBUG=False,
        INSTALLED_APPS=['corsheaders'],
        CORS_ALLOW_ALL_ORIGINS=True
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        DEBUG=False, INSTALLED_APPS=["corsheaders"], CORS_ALLOW_ALL_ORIGINS=True
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    )
    def test_permissive_cors_in_production(self):
        """測試生產環境過於寬鬆的 CORS"""
        self.validator._check_api_configuration()
<<<<<<< HEAD
<<<<<<< HEAD

        errors = [e for e in self.validator.result.errors if e.category == "security"]
        self.assertTrue(any("允許所有 CORS 來源" in e.message for e in errors))
=======
        
        errors = [e for e in self.validator.result.errors if e.category == 'security']
        self.assertTrue(any('允許所有 CORS 來源' in e.message for e in errors))
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        errors = [e for e in self.validator.result.errors if e.category == "security"]
        self.assertTrue(any("允許所有 CORS 來源" in e.message for e in errors))
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))


class HealthScoreTest(TestCase):
    """測試健康分數計算"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.validator = EnhancedConfigValidator()

=======
    
    def setUp(self):
        self.validator = EnhancedConfigValidator()
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    def setUp(self):
        self.validator = EnhancedConfigValidator()

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_perfect_health_score(self):
        """測試完美健康分數"""
        health_score = self.validator.get_health_score()
        self.assertEqual(health_score, 100)
<<<<<<< HEAD
<<<<<<< HEAD

    def test_health_score_with_issues(self):
        """測試有問題時的健康分數"""
        # 添加一些問題
        self.validator._add_issue("error", "security", "錯誤")
        self.validator._add_issue("warning", "performance", "警告")
        self.validator._add_issue("suggestion", "api", "建議")

        health_score = self.validator.get_health_score()
        self.assertLess(health_score, 100)
        self.assertGreaterEqual(health_score, 0)

    def test_priority_issues(self):
        """測試優先問題識別"""
        # 添加安全錯誤（優先問題）
        self.validator._add_issue("error", "security", "安全錯誤")
        self.validator._add_issue("error", "api", "API 錯誤")

        priority_issues = self.validator.get_priority_issues()
        self.assertEqual(len(priority_issues), 1)
        self.assertEqual(priority_issues[0].category, "security")
=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_health_score_with_issues(self):
        """測試有問題時的健康分數"""
        # 添加一些問題
        self.validator._add_issue("error", "security", "錯誤")
        self.validator._add_issue("warning", "performance", "警告")
        self.validator._add_issue("suggestion", "api", "建議")

        health_score = self.validator.get_health_score()
        self.assertLess(health_score, 100)
        self.assertGreaterEqual(health_score, 0)

    def test_priority_issues(self):
        """測試優先問題識別"""
        # 添加安全錯誤（優先問題）
        self.validator._add_issue("error", "security", "安全錯誤")
        self.validator._add_issue("error", "api", "API 錯誤")

        priority_issues = self.validator.get_priority_issues()
        self.assertEqual(len(priority_issues), 1)
<<<<<<< HEAD
        self.assertEqual(priority_issues[0].category, 'security')
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        self.assertEqual(priority_issues[0].category, "security")
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))


class IntegrationTest(TestCase):
    """集成測試"""
<<<<<<< HEAD
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_validate_enhanced_configuration(self):
        """測試主要入口函數"""
        result = validate_enhanced_configuration()
        self.assertIsInstance(result, ValidationResult)
<<<<<<< HEAD
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_get_configuration_report(self):
        """測試配置報告生成"""
        report = get_configuration_report()
        self.assertIsInstance(report, str)
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        self.assertIn("Django 配置完整性檢查報告", report)
        self.assertIn("健康分數", report)

    @patch("django.conf.settings")
<<<<<<< HEAD
=======
        self.assertIn('Django 配置完整性檢查報告', report)
        self.assertIn('健康分數', report)
    
    @patch('django.conf.settings')
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_configuration_with_mock_settings(self, mock_settings):
        """測試使用模擬設置的配置檢查"""
        # 模擬有問題的設置
        mock_settings.DEBUG = False
<<<<<<< HEAD
<<<<<<< HEAD
        mock_settings.MEDIA_ROOT = ""
        mock_settings.SECURE_SSL_REDIRECT = False

        result = validate_enhanced_configuration()

        # 應該發現錯誤
        self.assertTrue(result.has_errors)

        # 檢查特定錯誤
        media_errors = [e for e in result.errors if e.category == "media"]
        security_errors = [e for e in result.errors if e.category == "security"]

=======
        mock_settings.MEDIA_ROOT = ''
=======
        mock_settings.MEDIA_ROOT = ""
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        mock_settings.SECURE_SSL_REDIRECT = False

        result = validate_enhanced_configuration()

        # 應該發現錯誤
        self.assertTrue(result.has_errors)

        # 檢查特定錯誤
<<<<<<< HEAD
        media_errors = [e for e in result.errors if e.category == 'media']
        security_errors = [e for e in result.errors if e.category == 'security']
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
        media_errors = [e for e in result.errors if e.category == "media"]
        security_errors = [e for e in result.errors if e.category == "security"]

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        self.assertTrue(len(media_errors) > 0)
        self.assertTrue(len(security_errors) > 0)


class EdgeCasesTest(TestCase):
    """邊界情況測試"""
<<<<<<< HEAD
<<<<<<< HEAD

    def setUp(self):
        self.validator = EnhancedConfigValidator()

=======
    
    def setUp(self):
        self.validator = EnhancedConfigValidator()
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

    def setUp(self):
        self.validator = EnhancedConfigValidator()

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_multiple_validations(self):
        """測試多次驗證"""
        # 第一次驗證
        result1 = self.validator.validate_all()
        issues_count1 = result1.total_issues
<<<<<<< HEAD
<<<<<<< HEAD

        # 第二次驗證（應該重置結果）
        result2 = self.validator.validate_all()
        issues_count2 = result2.total_issues

        # 問題數量應該相同（結果被重置）
        self.assertEqual(issues_count1, issues_count2)

    @override_settings(STATICFILES_DIRS=["/nonexistent/path"])
    def test_nonexistent_static_dirs(self):
        """測試不存在的靜態文件目錄"""
        self.validator._check_media_and_static()

        warnings = [w for w in self.validator.result.warnings if w.category == "static"]
        self.assertTrue(any("目錄不存在" in w.message for w in warnings))

=======
        
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        # 第二次驗證（應該重置結果）
        result2 = self.validator.validate_all()
        issues_count2 = result2.total_issues

        # 問題數量應該相同（結果被重置）
        self.assertEqual(issues_count1, issues_count2)

    @override_settings(STATICFILES_DIRS=["/nonexistent/path"])
    def test_nonexistent_static_dirs(self):
        """測試不存在的靜態文件目錄"""
        self.validator._check_media_and_static()
<<<<<<< HEAD
        
        warnings = [w for w in self.validator.result.warnings if w.category == 'static']
        self.assertTrue(any('目錄不存在' in w.message for w in warnings))
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        warnings = [w for w in self.validator.result.warnings if w.category == "static"]
        self.assertTrue(any("目錄不存在" in w.message for w in warnings))

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_empty_category_breakdown(self):
        """測試空類別分佈"""
        breakdown = self.validator._get_category_breakdown()
        self.assertEqual(breakdown, {})
<<<<<<< HEAD
<<<<<<< HEAD

    def test_generate_summary_empty(self):
        """測試生成空摘要"""
        summary = self.validator.generate_summary()

        expected_keys = [
            "total_issues",
            "errors",
            "warnings",
            "suggestions",
            "health_score",
            "priority_issues_count",
            "categories",
        ]

        for key in expected_keys:
            self.assertIn(key, summary)

        self.assertEqual(summary["total_issues"], 0)
        self.assertEqual(summary["health_score"], 100)
=======
    
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def test_generate_summary_empty(self):
        """測試生成空摘要"""
        summary = self.validator.generate_summary()

        expected_keys = [
            "total_issues",
            "errors",
            "warnings",
            "suggestions",
            "health_score",
            "priority_issues_count",
            "categories",
        ]

        for key in expected_keys:
            self.assertIn(key, summary)
<<<<<<< HEAD
        
        self.assertEqual(summary['total_issues'], 0)
        self.assertEqual(summary['health_score'], 100)
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        self.assertEqual(summary["total_issues"], 0)
        self.assertEqual(summary["health_score"], 100)
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
