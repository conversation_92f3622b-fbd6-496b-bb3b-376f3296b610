"""
Django 配置完整性檢查管理命令 - Issue #184
========================================

使用方法:
python manage.py check_config [選項]

選項:
  --format {text,json}     輸出格式 (默認: text)
  --output FILE           輸出到文件 (默認: 標準輸出)
  --fail-on-error         發現錯誤時以非零狀態碼退出
  --summary-only          只顯示摘要信息
  --category CATEGORY     只檢查特定類別的問題
  --health-score-only     只顯示健康分數
  --export-report         導出詳細報告到文件
"""

import json
import sys
from pathlib import Path
from typing import Optional

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from config.enhanced_validation import (
    EnhancedConfigValidator,
    validate_enhanced_configuration,
<<<<<<< HEAD
<<<<<<< HEAD
    get_configuration_report,
=======
    get_configuration_report
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
    get_configuration_report,
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
)


class Command(BaseCommand):
<<<<<<< HEAD
<<<<<<< HEAD
    help = "執行 Django 配置完整性檢查"

    def add_arguments(self, parser):
        """添加命令行參數"""
        parser.add_argument(
            "--format",
            choices=["text", "json"],
            default="text",
            help="輸出格式 (默認: text)",
        )

        parser.add_argument("--output", type=str, help="輸出到文件路徑")

        parser.add_argument(
            "--fail-on-error", action="store_true", help="發現錯誤時以非零狀態碼退出"
        )

        parser.add_argument(
            "--summary-only", action="store_true", help="只顯示摘要信息"
        )

        parser.add_argument(
            "--category",
            choices=[
                "security",
                "performance",
                "media",
                "static",
                "logging",
                "api",
                "i18n",
            ],
            help="只檢查特定類別的問題",
        )

        parser.add_argument(
            "--health-score-only", action="store_true", help="只顯示健康分數"
        )

        parser.add_argument(
            "--export-report",
            action="store_true",
            help="導出詳細報告到 config-check-report.json",
        )

        parser.add_argument(
            "--verbose", action="store_true", help="顯示詳細的檢查過程信息"
        )

    def handle(self, *args, **options):
        """主要命令處理邏輯"""
        try:
            if options["verbose"]:
                self.stdout.write(
                    self.style.SUCCESS("🚀 開始執行 Django 配置完整性檢查...")
                )

            # 執行配置檢查
            validator = EnhancedConfigValidator()
            result = validator.validate_all()

            # 類別過濾
            if options["category"]:
                result = self._filter_by_category(result, options["category"])

            # 處理不同的輸出選項
            if options["health_score_only"]:
                self._output_health_score_only(validator, options)
            elif options["summary_only"]:
                self._output_summary_only(validator, options)
            elif options["format"] == "json":
                self._output_json(validator, result, options)
            else:
                self._output_text(validator, result, options)

            # 導出報告
            if options["export_report"]:
                self._export_detailed_report(validator, result)

            # 錯誤處理
            if options["fail_on_error"] and result.has_errors:
                if options["verbose"]:
                    self.stdout.write(
                        self.style.ERROR(
                            f"❌ 發現 {len(result.errors)} 個錯誤，以錯誤狀態退出"
                        )
                    )
                sys.exit(1)

            if options["verbose"]:
                self.stdout.write(self.style.SUCCESS("✅ 配置檢查完成"))

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"❌ 配置檢查執行失敗: {str(e)}"))
            if options["fail_on_error"]:
                sys.exit(1)
            raise CommandError(f"配置檢查失敗: {str(e)}")

=======
    help = '執行 Django 配置完整性檢查'
    
=======
    help = "執行 Django 配置完整性檢查"

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def add_arguments(self, parser):
        """添加命令行參數"""
        parser.add_argument(
            "--format",
            choices=["text", "json"],
            default="text",
            help="輸出格式 (默認: text)",
        )

        parser.add_argument("--output", type=str, help="輸出到文件路徑")

        parser.add_argument(
            "--fail-on-error", action="store_true", help="發現錯誤時以非零狀態碼退出"
        )

        parser.add_argument(
            "--summary-only", action="store_true", help="只顯示摘要信息"
        )

        parser.add_argument(
            "--category",
            choices=[
                "security",
                "performance",
                "media",
                "static",
                "logging",
                "api",
                "i18n",
            ],
            help="只檢查特定類別的問題",
        )

        parser.add_argument(
            "--health-score-only", action="store_true", help="只顯示健康分數"
        )

        parser.add_argument(
            "--export-report",
            action="store_true",
            help="導出詳細報告到 config-check-report.json",
        )

        parser.add_argument(
            "--verbose", action="store_true", help="顯示詳細的檢查過程信息"
        )

    def handle(self, *args, **options):
        """主要命令處理邏輯"""
        try:
            if options["verbose"]:
                self.stdout.write(
                    self.style.SUCCESS("🚀 開始執行 Django 配置完整性檢查...")
                )

            # 執行配置檢查
            validator = EnhancedConfigValidator()
            result = validator.validate_all()

            # 類別過濾
            if options["category"]:
                result = self._filter_by_category(result, options["category"])

            # 處理不同的輸出選項
            if options["health_score_only"]:
                self._output_health_score_only(validator, options)
            elif options["summary_only"]:
                self._output_summary_only(validator, options)
            elif options["format"] == "json":
                self._output_json(validator, result, options)
            else:
                self._output_text(validator, result, options)

            # 導出報告
            if options["export_report"]:
                self._export_detailed_report(validator, result)

            # 錯誤處理
            if options["fail_on_error"] and result.has_errors:
                if options["verbose"]:
                    self.stdout.write(
                        self.style.ERROR(
                            f"❌ 發現 {len(result.errors)} 個錯誤，以錯誤狀態退出"
                        )
                    )
                sys.exit(1)

            if options["verbose"]:
                self.stdout.write(self.style.SUCCESS("✅ 配置檢查完成"))

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"❌ 配置檢查執行失敗: {str(e)}"))
            if options["fail_on_error"]:
                sys.exit(1)
<<<<<<< HEAD
            raise CommandError(f'配置檢查失敗: {str(e)}')
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
            raise CommandError(f"配置檢查失敗: {str(e)}")

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def _filter_by_category(self, result, category):
        """根據類別過濾結果"""
        filtered_errors = [e for e in result.errors if e.category == category]
        filtered_warnings = [w for w in result.warnings if w.category == category]
        filtered_suggestions = [s for s in result.suggestions if s.category == category]
<<<<<<< HEAD
<<<<<<< HEAD

        from config.enhanced_validation import ValidationResult

        filtered_result = ValidationResult()
        filtered_result.errors = filtered_errors
        filtered_result.warnings = filtered_warnings
        filtered_result.suggestions = filtered_suggestions

        return filtered_result

=======
        
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        from config.enhanced_validation import ValidationResult

        filtered_result = ValidationResult()
        filtered_result.errors = filtered_errors
        filtered_result.warnings = filtered_warnings
        filtered_result.suggestions = filtered_suggestions

        return filtered_result
<<<<<<< HEAD
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def _output_health_score_only(self, validator, options):
        """只輸出健康分數"""
        health_score = validator.get_health_score()
        output = str(health_score)
<<<<<<< HEAD
<<<<<<< HEAD

        self._write_output(output, options)

    def _output_summary_only(self, validator, options):
        """只輸出摘要信息"""
        summary = validator.generate_summary()

=======
        
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        self._write_output(output, options)

    def _output_summary_only(self, validator, options):
        """只輸出摘要信息"""
        summary = validator.generate_summary()
<<<<<<< HEAD
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        output_lines = [
            f"🏥 健康分數: {summary['health_score']}/100",
            f"📊 總問題數: {summary['total_issues']}",
            f"   ❌ 錯誤: {summary['errors']}",
            f"   ⚠️  警告: {summary['warnings']}",
<<<<<<< HEAD
<<<<<<< HEAD
            f"   💡 建議: {summary['suggestions']}",
        ]

        if summary["priority_issues_count"] > 0:
            output_lines.append(f"🚨 優先問題: {summary['priority_issues_count']}")

        output = "\n".join(output_lines)
        self._write_output(output, options)

=======
            f"   💡 建議: {summary['suggestions']}"
=======
            f"   💡 建議: {summary['suggestions']}",
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        ]

        if summary["priority_issues_count"] > 0:
            output_lines.append(f"🚨 優先問題: {summary['priority_issues_count']}")

        output = "\n".join(output_lines)
        self._write_output(output, options)
<<<<<<< HEAD
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def _output_json(self, validator, result, options):
        """JSON 格式輸出"""
        summary = validator.generate_summary()
        priority_issues = validator.get_priority_issues()
<<<<<<< HEAD
<<<<<<< HEAD

        data = {
            "summary": summary,
            "errors": [self._issue_to_dict(issue) for issue in result.errors],
            "warnings": [self._issue_to_dict(issue) for issue in result.warnings],
            "suggestions": [self._issue_to_dict(issue) for issue in result.suggestions],
            "priority_issues": [
                self._issue_to_dict(issue) for issue in priority_issues
            ],
        }

        output = json.dumps(data, indent=2, ensure_ascii=False)
        self._write_output(output, options)

=======
        
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        data = {
            "summary": summary,
            "errors": [self._issue_to_dict(issue) for issue in result.errors],
            "warnings": [self._issue_to_dict(issue) for issue in result.warnings],
            "suggestions": [self._issue_to_dict(issue) for issue in result.suggestions],
            "priority_issues": [
                self._issue_to_dict(issue) for issue in priority_issues
            ],
        }

        output = json.dumps(data, indent=2, ensure_ascii=False)
        self._write_output(output, options)
<<<<<<< HEAD
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def _output_text(self, validator, result, options):
        """文字格式輸出"""
        report = get_configuration_report()
        self._write_output(report, options)
<<<<<<< HEAD
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
    def _export_detailed_report(self, validator, result):
        """導出詳細報告到文件"""
        summary = validator.generate_summary()
        priority_issues = validator.get_priority_issues()
<<<<<<< HEAD
<<<<<<< HEAD

        report_data = {
            "timestamp": self._get_timestamp(),
            "django_version": self._get_django_version(),
            "settings_module": getattr(settings, "SETTINGS_MODULE", "unknown"),
            "debug_mode": settings.DEBUG,
            "summary": summary,
            "detailed_results": {
                "errors": [self._issue_to_dict(issue) for issue in result.errors],
                "warnings": [self._issue_to_dict(issue) for issue in result.warnings],
                "suggestions": [
                    self._issue_to_dict(issue) for issue in result.suggestions
                ],
            },
            "priority_issues": [
                self._issue_to_dict(issue) for issue in priority_issues
            ],
            "recommendations": self._generate_recommendations(validator, result),
        }

        output_file = Path("config-check-report.json")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        self.stdout.write(
            self.style.SUCCESS(f"📄 詳細報告已導出到: {output_file.absolute()}")
        )

    def _issue_to_dict(self, issue):
        """將 ValidationIssue 轉換為字典"""
        return {
            "level": issue.level,
            "category": issue.category,
            "message": issue.message,
            "fix_suggestion": issue.fix_suggestion,
            "setting_name": issue.setting_name,
        }

    def _write_output(self, content: str, options: dict):
        """寫入輸出內容"""
        if options["output"]:
            output_path = Path(options["output"])
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(content)
            self.stdout.write(
                self.style.SUCCESS(f"📄 輸出已保存到: {output_path.absolute()}")
            )
        else:
            self.stdout.write(content)

    def _get_timestamp(self):
        """獲取當前時間戳"""
        from datetime import datetime

        return datetime.now().isoformat()

    def _get_django_version(self):
        """獲取 Django 版本"""
        import django

        return django.get_version()

    def _generate_recommendations(self, validator, result):
        """生成改進建議"""
        recommendations = []

=======
        
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        report_data = {
            "timestamp": self._get_timestamp(),
            "django_version": self._get_django_version(),
            "settings_module": getattr(settings, "SETTINGS_MODULE", "unknown"),
            "debug_mode": settings.DEBUG,
            "summary": summary,
            "detailed_results": {
                "errors": [self._issue_to_dict(issue) for issue in result.errors],
                "warnings": [self._issue_to_dict(issue) for issue in result.warnings],
                "suggestions": [
                    self._issue_to_dict(issue) for issue in result.suggestions
                ],
            },
            "priority_issues": [
                self._issue_to_dict(issue) for issue in priority_issues
            ],
            "recommendations": self._generate_recommendations(validator, result),
        }

        output_file = Path("config-check-report.json")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        self.stdout.write(
            self.style.SUCCESS(f"📄 詳細報告已導出到: {output_file.absolute()}")
        )

    def _issue_to_dict(self, issue):
        """將 ValidationIssue 轉換為字典"""
        return {
            "level": issue.level,
            "category": issue.category,
            "message": issue.message,
            "fix_suggestion": issue.fix_suggestion,
            "setting_name": issue.setting_name,
        }

    def _write_output(self, content: str, options: dict):
        """寫入輸出內容"""
        if options["output"]:
            output_path = Path(options["output"])
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(content)
            self.stdout.write(
                self.style.SUCCESS(f"📄 輸出已保存到: {output_path.absolute()}")
            )
        else:
            self.stdout.write(content)

    def _get_timestamp(self):
        """獲取當前時間戳"""
        from datetime import datetime

        return datetime.now().isoformat()

    def _get_django_version(self):
        """獲取 Django 版本"""
        import django

        return django.get_version()

    def _generate_recommendations(self, validator, result):
        """生成改進建議"""
        recommendations = []
<<<<<<< HEAD
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        if result.has_errors:
            recommendations.append(
                "🚨 發現配置錯誤，建議立即修復以確保應用程序安全性和穩定性"
            )
<<<<<<< HEAD
<<<<<<< HEAD

        if len(result.warnings) > 5:
            recommendations.append("⚠️ 發現多個配置警告，建議檢查並逐步改進")

=======
        
        if len(result.warnings) > 5:
            recommendations.append(
                "⚠️ 發現多個配置警告，建議檢查並逐步改進"
            )
        
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        if len(result.warnings) > 5:
            recommendations.append("⚠️ 發現多個配置警告，建議檢查並逐步改進")

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        health_score = validator.get_health_score()
        if health_score < 70:
            recommendations.append(
                f"🏥 配置健康分數較低 ({health_score}/100)，建議優先處理錯誤和警告"
            )
        elif health_score < 90:
            recommendations.append(
                f"🏥 配置健康分數良好 ({health_score}/100)，可考慮採納改進建議"
            )
        else:
            recommendations.append(
                f"🏥 配置健康分數優秀 ({health_score}/100)，繼續保持！"
            )
<<<<<<< HEAD
<<<<<<< HEAD

        # 安全相關建議
        security_issues = [
            issue
            for issue in (result.errors + result.warnings)
            if issue.category == "security"
=======
        
        # 安全相關建議
        security_issues = [
            issue for issue in (result.errors + result.warnings)
            if issue.category == 'security'
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        # 安全相關建議
        security_issues = [
            issue
            for issue in (result.errors + result.warnings)
            if issue.category == "security"
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        ]
        if security_issues:
            recommendations.append(
                f"🔒 發現 {len(security_issues)} 個安全相關問題，強烈建議優先處理"
            )
<<<<<<< HEAD
<<<<<<< HEAD

        # 性能相關建議
        perf_issues = [
            issue
            for issue in (result.errors + result.warnings + result.suggestions)
            if issue.category == "performance"
=======
        
        # 性能相關建議
        perf_issues = [
            issue for issue in (result.errors + result.warnings + result.suggestions)
            if issue.category == 'performance'
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        # 性能相關建議
        perf_issues = [
            issue
            for issue in (result.errors + result.warnings + result.suggestions)
            if issue.category == "performance"
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
        ]
        if len(perf_issues) > 3:
            recommendations.append(
                f"⚡ 發現 {len(perf_issues)} 個性能相關問題，建議優化以提升系統響應速度"
            )
<<<<<<< HEAD
<<<<<<< HEAD

        return recommendations
=======
        
        return recommendations
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

        return recommendations
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
