# Django 配置完整性檢查 - 使用指南

## 🚀 快速開始

### 基本檢查

```bash
# 執行完整配置檢查
python manage.py check_config
```

這將顯示完整的配置報告，包括所有發現的問題和建議。

### 常用命令

```bash
# 只看摘要
python manage.py check_config --summary-only

# 只看健康分數
python manage.py check_config --health-score-only

# JSON 格式輸出
python manage.py check_config --format=json

# 檢查特定類別（如安全性）
python manage.py check_config --category=security

# 輸出到文件
python manage.py check_config --output=report.txt

# CI/CD 中使用（有錯誤時退出）
python manage.py check_config --fail-on-error
```

## 📋 檢查類別說明

### 🔒 security (安全)
- HTTPS 重定向設置
- HSTS 配置
- Session/CSRF Cookie 安全
- 密碼驗證器
- CORS 設置

### ⚡ performance (性能)
- 數據庫連接池
- 緩存配置
- 中間件順序
- 模板設置

### 📁 media (媒體文件)
- MEDIA_ROOT/MEDIA_URL 設置
- 文件上傳限制

### 📁 static (靜態文件)
- STATIC_ROOT/STATIC_URL 設置
- STATICFILES_DIRS 檢查

### 📝 logging (日誌)
- 日誌處理器配置
- 日誌級別適當性
- 生產環境文件日誌

### 🌐 api (API)
- Django REST Framework 設置
- 認證和權限配置
- 分頁和限流設置

### 🌍 i18n (國際化)
- 時區設置
- 語言配置
- 本地化設置

## 💡 問題級別

- **❌ 錯誤**: 必須修復的嚴重問題
<<<<<<< HEAD
<<<<<<< HEAD
- **⚠️ 警告**: 建議修復的重要問題
=======
- **⚠️ 警告**: 建議修復的重要問題  
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
- **⚠️ 警告**: 建議修復的重要問題
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
- **💡 建議**: 可選的改進建議

## 🏥 健康分數解讀

- **90-100**: 優秀 - 配置非常健康
- **80-89**: 良好 - 有少量改進空間
- **70-79**: 中等 - 需要一些改進
- **60-69**: 較差 - 需要重點關注
- **< 60**: 危險 - 需要立即處理

## 📊 輸出格式

### 文字格式 (默認)
```
============================================================
Django 配置完整性檢查報告
============================================================

🏥 總體健康分數: 85/100
📊 問題統計:
   ❌ 錯誤: 2
   ⚠️  警告: 3
   💡 建議: 5
```

### JSON 格式
```json
{
  "summary": {
    "health_score": 85,
    "total_issues": 10,
    "errors": 2,
    "warnings": 3,
    "suggestions": 5
  },
  "errors": [...],
  "warnings": [...],
  "suggestions": [...]
}
```

## 🔧 程序化使用

```python
from config.enhanced_validation import validate_enhanced_configuration

# 執行檢查
result = validate_enhanced_configuration()

# 檢查結果
if result.has_errors:
    print(f"發現 {len(result.errors)} 個錯誤")

# 遍歷問題
for error in result.errors:
    print(f"錯誤: {error.message}")
    if error.fix_suggestion:
        print(f"解決方案: {error.fix_suggestion}")
```

## 🔄 常見問題修復

### 安全問題

```python
# settings.py 或 settings/prod.py

# 強制 HTTPS
SECURE_SSL_REDIRECT = True

# HSTS 設置
SECURE_HSTS_SECONDS = 31536000  # 1年
SECURE_HSTS_INCLUDE_SUBDOMAINS = True

# Cookie 安全
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = True
```

### 性能問題

```python
# 數據庫連接池
DATABASES = {
    'default': {
        # ... 其他設置
        'CONN_MAX_AGE': 600,  # 10分鐘
    }
}

# Redis 緩存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}
```

### 媒體文件問題

```python
# 媒體設置
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
MEDIA_URL = '/media/'

# 靜態文件設置
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATIC_URL = '/static/'
```

### 日誌問題

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 🚨 CI/CD 集成

### 在 CI 中使用

```bash
# 檢查配置並在有錯誤時失敗
python manage.py check_config --fail-on-error

# 生成報告
python manage.py check_config --export-report
```

### GitHub Actions 示例

```yaml
- name: Django 配置檢查
  run: |
    python manage.py check_config --fail-on-error --verbose
<<<<<<< HEAD
<<<<<<< HEAD

=======
    
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======

>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
- name: 上傳配置報告
  uses: actions/upload-artifact@v2
  with:
    name: config-report
    path: config-check-report.json
```

## 📈 監控建議

### 定期檢查
- 在開發過程中定期運行
- CI/CD 管道中自動檢查
- 生產部署前必須檢查

### 健康分數追蹤
- 設定健康分數目標（如 ≥ 85）
- 追蹤分數變化趨勢
- 在分數下降時觸發警報

### 錯誤處理
- 零容忍安全錯誤
- 及時修復性能警告
- 逐步改進建議項目

## 🛠️ 故障排除

### 常見錯誤

1. **模塊導入錯誤**
   ```bash
   # 確保配置模塊在 Python 路徑中
   export PYTHONPATH="${PYTHONPATH}:."
   ```

2. **設置模塊問題**
   ```bash
   # 確保 DJANGO_SETTINGS_MODULE 正確設置
   export DJANGO_SETTINGS_MODULE=myproject.settings
   ```

3. **權限問題**
   ```bash
   # 確保有寫入權限（導出報告時）
   chmod 755 .
   ```

### 調試選項

```bash
# 詳細輸出
python manage.py check_config --verbose

# 檢查特定類別
python manage.py check_config --category=security --verbose

# 輸出到文件便於分析
python manage.py check_config --output=debug.txt --verbose
<<<<<<< HEAD
<<<<<<< HEAD
```
=======
```
>>>>>>> 72b169e84 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
=======
```
>>>>>>> 3cb42e3f0 (feat(backend): 實現 Django 配置完整性檢查機制 (#184))
