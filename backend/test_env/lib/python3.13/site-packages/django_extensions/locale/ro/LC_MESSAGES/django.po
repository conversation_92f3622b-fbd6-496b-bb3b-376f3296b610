# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: django-extensions\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2011-02-02 11:43+0100\n"
"PO-Revision-Date: 2011-02-02 10:38+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : (n==0 || (n%100 > 0 && n%100 < "
"20)) ? 1 : 2)\n"

#: admin/__init__.py:121
msgid "and"
msgstr "și"

#: admin/__init__.py:123
#, python-format
msgid ""
"Use the left field to do %(model_name)s lookups in the fields %(field_list)s."
msgstr ""
"Folosește câmpul din stânga pentru a efectua căutări de %(model_name)s în "
"câmpurile %(field_list)s."

#: db/models.py:15
msgid "created"
msgstr "creat"

#: db/models.py:16
msgid "modified"
msgstr "modificat"

#: db/models.py:26
msgid "title"
msgstr "Titlu"

#: db/models.py:27
msgid "slug"
msgstr "Slug"

#: db/models.py:28
msgid "description"
msgstr "Descriere"

#: db/models.py:50
msgid "Inactive"
msgstr "Inactiv"

#: db/models.py:51
msgid "Active"
msgstr "Activ"

#: db/models.py:53
msgid "status"
msgstr "Stare"

#: db/models.py:56
msgid "keep empty for an immediate activation"
msgstr "A se lăsa gol pentru activare imediată"

#: db/models.py:58
msgid "keep empty for indefinite activation"
msgstr "A se lăsa gol pentru activare nelimitată"

#: management/commands/show_urls.py:34
#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s nu pare să fie un obiect urlpattern"

#: templates/django_extensions/widgets/foreignkey_searchinput.html:4
msgid "Lookup"
msgstr "Căutare"
