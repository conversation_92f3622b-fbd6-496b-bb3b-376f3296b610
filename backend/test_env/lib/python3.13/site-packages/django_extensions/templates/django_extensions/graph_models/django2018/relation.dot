{% for model in graph.models %}{% for relation in model.relations %}{% if relation.needs_node %}  {{ relation.target_app }}_{{ relation.target }} [label=<
  <TABLE BGCOLOR="white" BORDER="0" CELLBORDER="0" CELLSPACING="0">
  <TR><TD COLSPAN="2" CELLPADDING="4" ALIGN="CENTER" BGCOLOR="#1b563f">
  <FONT FACE="Roboto" POINT-SIZE="12" COLOR="white">{{ relation.target }}</FONT>
  </TD></TR>
  </TABLE>
  >]{% endif %}
  {{ model.app_name }}_{{ model.name }} -> {{ relation.target_app }}_{{ relation.target }}
  [label=" {{ relation.label }}"] {{ relation.arrows }};
{% endfor %}{% endfor %}
