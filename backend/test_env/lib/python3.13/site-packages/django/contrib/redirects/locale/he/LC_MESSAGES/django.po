# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2014-2015,2020,2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-12-04 18:32+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2014-2015,2020,2023\n"
"Language-Team: Hebrew (http://app.transifex.com/django/django/language/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % "
"1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

msgid "Redirects"
msgstr "הפניות"

msgid "site"
msgstr "אתר"

msgid "redirect from"
msgstr "הפניה מ"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr "זה אמור להיות נתיב מלא, ללא שם המתחם. לדוגמה: “/events/search/”."

msgid "redirect to"
msgstr "הפניה אל"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"זה יכול להיות נתיב מלא (כמו לעיל) או כתובת URL מלאה שמתחילה בסכימה כגון "
"\"https://\"."

msgid "redirect"
msgstr "הפניה"

msgid "redirects"
msgstr "הפניות"
