django_extensions-4.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
django_extensions-4.1.dist-info/METADATA,sha256=Io0sR9z1a9ryd8sJ3W8y3ukkMjYfwlLKoQtl0r1d1ZA,6128
django_extensions-4.1.dist-info/RECORD,,
django_extensions-4.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions-4.1.dist-info/WHEEL,sha256=CmyFI0kx5cdEMTLiONQRbGQwjIoR1aIYB7eCAQ4KPJ0,91
django_extensions-4.1.dist-info/licenses/LICENSE,sha256=hfh-J08r7s6vlJVWdNgyPZ_B69b8NdSvzdOLVEygyyA,1057
django_extensions-4.1.dist-info/top_level.txt,sha256=a-Shg8eC0Rl6_AoTRvqIUhzOFzQeCFU1Z7ee7myIYMg,18
django_extensions/__init__.py,sha256=1buxjH-04dCr3atSCJ4SLw1U0jsesMeqEKcujgH1nWs,138
django_extensions/__pycache__/__init__.cpython-313.pyc,,
django_extensions/__pycache__/apps.cpython-313.pyc,,
django_extensions/__pycache__/collision_resolvers.cpython-313.pyc,,
django_extensions/__pycache__/compat.cpython-313.pyc,,
django_extensions/__pycache__/import_subclasses.cpython-313.pyc,,
django_extensions/__pycache__/models.cpython-313.pyc,,
django_extensions/__pycache__/settings.cpython-313.pyc,,
django_extensions/__pycache__/validators.cpython-313.pyc,,
django_extensions/admin/__init__.py,sha256=ihqhcGHxN5I82DgBQ4DLCieJ9zscQUiXtPq4eiRYI4M,7316
django_extensions/admin/__pycache__/__init__.cpython-313.pyc,,
django_extensions/admin/__pycache__/filter.cpython-313.pyc,,
django_extensions/admin/__pycache__/widgets.cpython-313.pyc,,
django_extensions/admin/filter.py,sha256=TOK2bF9GjYhwl49L9Lfmsg_aGDENKzzgYtJ8EqeoNGo,1902
django_extensions/admin/widgets.py,sha256=xy3hYTe8p89tu7IIm0L4T7RVAWWYjZiVBEncxiWyoyw,3361
django_extensions/apps.py,sha256=sK4VOcpveKq7ODjBojfJLpr5yYHMvdYL_T106o8ibVk,171
django_extensions/auth/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/auth/__pycache__/__init__.cpython-313.pyc,,
django_extensions/auth/__pycache__/mixins.cpython-313.pyc,,
django_extensions/auth/mixins.py,sha256=x8mBHy8mzQ2o7arKCTFN6Ku4Qufk6s8DltgvN17FlNg,488
django_extensions/collision_resolvers.py,sha256=ksvGapdiBMRL-011fl15V0ExcsYdKTsm2hQnXbwpZgU,11109
django_extensions/compat.py,sha256=KtFhHKGo116vlku5HzS7Y3cs9qe0e2WlzXy8Lv-VV_Q,1929
django_extensions/conf/app_template/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/app_template/forms.py.tmpl,sha256=_K9nXjI1BEn-aPQYmNM9mcBwp21EnzAvtHF6lXeLQmY,55
django_extensions/conf/app_template/migrations/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/app_template/models.py.tmpl,sha256=Vjc0p2XbAPgE6HyTF6vll98A4eDhA5AvaQqsc4kQ9AQ,57
django_extensions/conf/app_template/urls.py.tmpl,sha256=nzK9G5Qi-8ECvgQ-5A5UhVYB9nmKTuWxKkrqWYgSzS4,69
django_extensions/conf/app_template/views.py.tmpl,sha256=F42JXgnqFqK0fajXeutyJJxwOszRxoLMNkIhfc4Z7KI,26
django_extensions/conf/command_template/management/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/command_template/management/commands/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/command_template/management/commands/sample.py.tmpl,sha256=VWqndBmkpZ5jw_3DrisYjXD76Si5lVSVcZlkifG57gs,306
django_extensions/conf/jobs_template/jobs/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/jobs_template/jobs/daily/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/jobs_template/jobs/hourly/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/jobs_template/jobs/monthly/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/jobs_template/jobs/sample.py.tmpl,sha256=r2cd8E0jNTKIJYQmPULuxjZFxzg1yrv72IHsipWkWtY,178
django_extensions/conf/jobs_template/jobs/weekly/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/jobs_template/jobs/yearly/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/template_tags_template/templatetags/__init__.py.tmpl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/conf/template_tags_template/templatetags/sample.py.tmpl,sha256=IOMcdXaX3IBAawoGoteRYqF5Y2ggxsLweR5XZqxfpMk,59
django_extensions/db/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/db/__pycache__/__init__.cpython-313.pyc,,
django_extensions/db/__pycache__/models.cpython-313.pyc,,
django_extensions/db/fields/__init__.py,sha256=Z6UD1GhVBvCXGxVobeC_dVoK3TynLLvT6iZjvQbsLhs,21363
django_extensions/db/fields/__pycache__/__init__.cpython-313.pyc,,
django_extensions/db/fields/__pycache__/json.cpython-313.pyc,,
django_extensions/db/fields/json.py,sha256=UJm8E7VHqqW2dyuY2VzUvLOuAFx9XshQvB-mrBCbrUg,3168
django_extensions/db/models.py,sha256=h-2AGZfP8r47ieUfTcK4Yg7qo9kLu1V4wO_GVi_Ufgg,3928
django_extensions/import_subclasses.py,sha256=ViDGIEoRIrfIv1GVfG1PJmJs2WMRrPwKG_StAulNqic,2343
django_extensions/jobs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/jobs/__pycache__/__init__.cpython-313.pyc,,
django_extensions/jobs/daily/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/jobs/daily/__pycache__/__init__.cpython-313.pyc,,
django_extensions/jobs/daily/__pycache__/cache_cleanup.cpython-313.pyc,,
django_extensions/jobs/daily/__pycache__/daily_cleanup.cpython-313.pyc,,
django_extensions/jobs/daily/cache_cleanup.py,sha256=am0xy2aejVodGr7063ijNYQuiGhdg_PayflHte3_22w,646
django_extensions/jobs/daily/daily_cleanup.py,sha256=hSLNGhiI9A-wKpxj8ie3wjmMTGOMq_kWjE-kt3_JqAU,389
django_extensions/jobs/hourly/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/jobs/hourly/__pycache__/__init__.cpython-313.pyc,,
django_extensions/jobs/minutely/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/jobs/minutely/__pycache__/__init__.cpython-313.pyc,,
django_extensions/jobs/monthly/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/jobs/monthly/__pycache__/__init__.cpython-313.pyc,,
django_extensions/jobs/weekly/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/jobs/weekly/__pycache__/__init__.cpython-313.pyc,,
django_extensions/jobs/yearly/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/jobs/yearly/__pycache__/__init__.cpython-313.pyc,,
django_extensions/locale/ar/LC_MESSAGES/django.po,sha256=j23ombvXVqaqcz5Zr3XRs9NuEkbBI1FiZlsmdeiaPWU,3126
django_extensions/locale/da/LC_MESSAGES/django.mo,sha256=R7WNKaXc0q4iM1cUsgzdbdRZ08r7m14EmM1znFTo1FI,797
django_extensions/locale/da/LC_MESSAGES/django.po,sha256=Zl33Wn5Sz6JsDUcbR_2aMtFCTqmDYz1XpXXsrY2GBl0,1667
django_extensions/locale/de/LC_MESSAGES/django.mo,sha256=kuhHiXWrfazxsvFzvENWfY5sN3XpLvy1-AKQ0jKOnAs,1227
django_extensions/locale/de/LC_MESSAGES/django.po,sha256=OLFLDJbZLPk3oK5DUcJ-V7eeBKZcHjlu_Vl0WTHW9F4,1755
django_extensions/locale/el/LC_MESSAGES/django.mo,sha256=0CafRFBnuy4QdqtoaipoKpONaVMvtfP1J_4eMBB2gAg,1581
django_extensions/locale/el/LC_MESSAGES/django.po,sha256=UC2b1GCXVnUteg1ZFqooRp6wkcxBufQGWCSZW8Hxndw,2116
django_extensions/locale/en/LC_MESSAGES/django.mo,sha256=9JJOWscsqQUH_P7IWH5P5MEJPDJjDGzGl-Zz5-xGDFo,367
django_extensions/locale/en/LC_MESSAGES/django.po,sha256=l27VRI3peRt_aKdlaQ7zVXj03wR2PfIex2X3SWrrSBc,2229
django_extensions/locale/es/LC_MESSAGES/django.mo,sha256=SH8ojro4wqhcR8yKM2vn9JVxTMbke7zwUjsc_W60jfA,1260
django_extensions/locale/es/LC_MESSAGES/django.po,sha256=euh9NBu3f-f-CuNgPGaJDebN0TbalfKKJ_X5q55VqA8,1788
django_extensions/locale/fr/LC_MESSAGES/django.mo,sha256=XIMBOSYt8pHAhP3pUBs1N-iKLn3e0LRgTNYe_ippr58,743
django_extensions/locale/fr/LC_MESSAGES/django.po,sha256=xKyEMPuZ_jFx7_7kY0plfHZV_gB8gUr2nvKdU_X3CLY,1931
django_extensions/locale/hu/LC_MESSAGES/django.mo,sha256=7rWzOkIurHDcvi4uCgh4hkQjUpV182FSyjKZz6mIBFU,1242
django_extensions/locale/hu/LC_MESSAGES/django.po,sha256=SOHXX186PGybAII05VA5QRZvSjtXR9fLJpgS2acxwt8,1770
django_extensions/locale/id/LC_MESSAGES/django.mo,sha256=X3tKDCM5qiuVi5dYOnzxAxx6mQ3w-wTJBvP7_ENnHhg,1508
django_extensions/locale/id/LC_MESSAGES/django.po,sha256=a4dguUsySnXLdDDafyXcq2lXFmYN-DS6uoEOQQJGEV4,2243
django_extensions/locale/it/LC_MESSAGES/django.mo,sha256=y3dS8jT30b2P9il5kxQaCj_JgaLLCCkR_vLEllX8L0g,1247
django_extensions/locale/it/LC_MESSAGES/django.po,sha256=AENMGV_gkuUqp2gVWnENI5hlCtJipNykZkAWcvlRia0,1775
django_extensions/locale/ja/LC_MESSAGES/django.mo,sha256=5fTQjN83bExfQbkaAMq3zve2B3fEWkf6rF-QYGZf9fA,1397
django_extensions/locale/ja/LC_MESSAGES/django.po,sha256=CGrMk9hH64qBE_6NF-qPMwHpdfW57FwY3PlF0g0_g0M,1925
django_extensions/locale/pl/LC_MESSAGES/django.mo,sha256=G3yZYzIwUHJ0PK14VhRXxJaYSXRkBQWa4yfFwJyhSBs,2002
django_extensions/locale/pl/LC_MESSAGES/django.po,sha256=hVRdMxQmgRhtruCm66bZQVY-OIfSSYVBSJViuZNHB_4,2788
django_extensions/locale/pt/LC_MESSAGES/django.mo,sha256=F_q92e6dFwPbjvYWHNBvCjgd5mIj3_ezrHvCOFeUZCw,1262
django_extensions/locale/pt/LC_MESSAGES/django.po,sha256=oKucDPxqIFZAOeVa_mbvOsmXXwyTydd82_Z_pXpkfvI,1790
django_extensions/locale/pt_BR/LC_MESSAGES/django.mo,sha256=bN2RG97zI3S6qEuMmvbDvPCo4YSZ_KEY5UxviD9WzlA,1310
django_extensions/locale/pt_BR/LC_MESSAGES/django.po,sha256=VDIRUodyxJr4PDcgiOuR6o3k1Ss_4ge5rx0DZgk5QwY,2082
django_extensions/locale/ro/LC_MESSAGES/django.mo,sha256=8-8B-I7iFCGZKBj1XKMbMqQLl6Yg2W1IEG39miSI8Hk,1352
django_extensions/locale/ro/LC_MESSAGES/django.po,sha256=CWaWS2C08-8lNWMCtPSPvDj4xONYrD3UGx4QSWXuWgg,1891
django_extensions/locale/ru/LC_MESSAGES/django.mo,sha256=C_kjCXvZuZ2ZdiU8ffcjKwcnA-d5IiUTgpglX7JdD-U,2009
django_extensions/locale/ru/LC_MESSAGES/django.po,sha256=luenXP4hypDODQUVWowDSCkYW9VMF_9NBlTUVkAmB3o,3820
django_extensions/logging/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/logging/__pycache__/__init__.cpython-313.pyc,,
django_extensions/logging/__pycache__/filters.cpython-313.pyc,,
django_extensions/logging/filters.py,sha256=sESzvZ3U6V4lH5kUKBl7zc0D9IFpCWND9flxBKVc7vA,1126
django_extensions/management/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/management/__pycache__/__init__.cpython-313.pyc,,
django_extensions/management/__pycache__/base.cpython-313.pyc,,
django_extensions/management/__pycache__/color.cpython-313.pyc,,
django_extensions/management/__pycache__/debug_cursor.cpython-313.pyc,,
django_extensions/management/__pycache__/email_notifications.cpython-313.pyc,,
django_extensions/management/__pycache__/jobs.cpython-313.pyc,,
django_extensions/management/__pycache__/modelviz.cpython-313.pyc,,
django_extensions/management/__pycache__/mysql.cpython-313.pyc,,
django_extensions/management/__pycache__/notebook_extension.cpython-313.pyc,,
django_extensions/management/__pycache__/shells.cpython-313.pyc,,
django_extensions/management/__pycache__/signals.cpython-313.pyc,,
django_extensions/management/__pycache__/technical_response.cpython-313.pyc,,
django_extensions/management/__pycache__/utils.cpython-313.pyc,,
django_extensions/management/base.py,sha256=HUiUTdXwYkvjwwgxkTfcNw5TWbWwlLFNIP4kp_Cb1hg,1431
django_extensions/management/color.py,sha256=cnoHNtMDJN_bXfBYyACWW_CQxiX-LU13-2CHxxjw4t8,907
django_extensions/management/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/management/commands/__pycache__/__init__.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/admin_generator.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/clean_pyc.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/clear_cache.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/compile_pyc.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/create_command.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/create_jobs.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/create_template_tags.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/delete_squashed_migrations.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/describe_form.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/drop_test_database.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/dumpscript.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/export_emails.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/find_template.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/generate_password.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/generate_secret_key.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/graph_models.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/list_model_info.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/list_signals.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/mail_debug.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/managestate.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/merge_model_instances.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/notes.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/print_settings.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/print_user_for_session.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/raise_test_exception.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/reset_db.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/reset_schema.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/runjob.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/runjobs.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/runprofileserver.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/runscript.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/runserver_plus.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/set_default_site.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/set_fake_emails.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/set_fake_passwords.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/shell_plus.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/show_permissions.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/show_template_tags.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/show_urls.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/sqlcreate.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/sqldiff.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/sqldsn.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/sync_s3.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/syncdata.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/unreferenced_files.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/update_permissions.cpython-313.pyc,,
django_extensions/management/commands/__pycache__/validate_templates.cpython-313.pyc,,
django_extensions/management/commands/admin_generator.py,sha256=bnp8Y0huCsBID-aKcAvhoOBSWqtDY6y8KI2XpDiHjvE,12188
django_extensions/management/commands/clean_pyc.py,sha256=S4udDKSlcF-MvVKZBcI3oPfi-TCxXeG68EHWqeKuP9E,1671
django_extensions/management/commands/clear_cache.py,sha256=3xsdc0twEN99Ll6u2bW8UZwKVjIZVnePYCUVLLlffbc,1514
django_extensions/management/commands/compile_pyc.py,sha256=7EvZLJR3PhLYFkbcGkFJH0WnYFztX-R6WrFx3GlXThw,1346
django_extensions/management/commands/create_command.py,sha256=Sg6_SJgPeP2qUkfyWJ2zEbPnOzQ3woF2t7pIjHQjzyk,4068
django_extensions/management/commands/create_jobs.py,sha256=jCvjlf9g-aduJx7PCIb0BKlOQ4dGc7ISzpu2fz_lSpM,2537
django_extensions/management/commands/create_template_tags.py,sha256=PiKRh6P0_byU37Ni_tGYqd9Jog3CNfECyu0xAvuWVNQ,2981
django_extensions/management/commands/delete_squashed_migrations.py,sha256=9zEI6Tqxmv5dI7k2DZOeTdHAhlqM6bS6shjotgv7urs,7711
django_extensions/management/commands/describe_form.py,sha256=RvSaaYkiW-IT2PEdtethWQ4LNeVsL5Gcgilj4uNUW9w,2981
django_extensions/management/commands/drop_test_database.py,sha256=ghYtG4HX3htOCJxYHeZEqHKHFF6xLakYal2P88ILdu0,9292
django_extensions/management/commands/dumpscript.py,sha256=gnS8hzRO0fD9WuZnaT63Mx9JqX9xqgnp7_I_DUKt4yc,29150
django_extensions/management/commands/export_emails.py,sha256=qXZPOt0XDGstS91piUoKUDAR2xX2doRsU5zcU66jl8w,6314
django_extensions/management/commands/find_template.py,sha256=GImM8uPJ_3wNUDwOVTZNvWcyun4pa5aevBpsT2qtsMk,695
django_extensions/management/commands/generate_password.py,sha256=if1gl5s3Bds6lpAiffvy34au3hpN0MNq5-QOMd4CNRU,1155
django_extensions/management/commands/generate_secret_key.py,sha256=bisQO7XRV1sOe1GqQ0-mTQZgXviWQrdiLxJJ7qWSYq8,484
django_extensions/management/commands/graph_models.py,sha256=Qf_rgGVEWgBbbWmg3VDqPtZ0xSim28rJJixax4fMGXs,17929
django_extensions/management/commands/list_model_info.py,sha256=IUVNFVeK-P-93CCvjzqLLJD5htir5I4WEpskGuixucc,6532
django_extensions/management/commands/list_signals.py,sha256=j8ow4YtbUf9ujZdQMl0Ce0zXA0oYn3uf8Xi5QI9N6bs,2974
django_extensions/management/commands/mail_debug.py,sha256=3NH_xjx46O5t9vDxPsgEIu5qxYA2hAnz1HUAcyoULuk,3264
django_extensions/management/commands/managestate.py,sha256=2PNf7Q6XxjQUxIvuFJeSM48AVv6eIMfdgdnuh2Rl1NM,7164
django_extensions/management/commands/merge_model_instances.py,sha256=zF9kbfGL3C7D2WypXl3B-07kgjeX1EDO-1PGt1Wy6nY,9777
django_extensions/management/commands/notes.py,sha256=7tW2Pcy1YaMfcnygg1TYZivZE7aIQN9x5iKqGGvSER0,3024
django_extensions/management/commands/print_settings.py,sha256=L6Ros6V4o40cYnng2vhrIuMjLpDj3SiWA0Sfu0RKYSc,2728
django_extensions/management/commands/print_user_for_session.py,sha256=RqTC0xgP2DWBVUvsTEFimSiCkzAemKIBAUKa2w4tamg,2120
django_extensions/management/commands/raise_test_exception.py,sha256=71c2pIi__scpzvKrFGsfaS4TTYeuxulI4I4vlJaldQo,659
django_extensions/management/commands/reset_db.py,sha256=x9RZM5PQ-SyMYmyOr97KEbMVb1BHeQ_M96-Ip41lt2s,8677
django_extensions/management/commands/reset_schema.py,sha256=xA4S2_osQt0x9vS9egYyezsfX-LhYdNruKmS3VTSye8,3132
django_extensions/management/commands/runjob.py,sha256=fl3WV3IZUxZhw6mmbWulCkHcJz0WcdTYeU30pOmUShE,2074
django_extensions/management/commands/runjobs.py,sha256=Idd9aL7SRsk2YwoBlALkeM0OWJnD9p20NTRcBlXxfUE,3542
django_extensions/management/commands/runprofileserver.py,sha256=xxhes9xQo_xT8jktTOzLihR2SZ3t9jiaOTn6FyASNN8,10761
django_extensions/management/commands/runscript.py,sha256=j06vV2-fMyiPSELoijC1fyoXX6D1BG2143523lg8KZg,12975
django_extensions/management/commands/runserver_plus.py,sha256=4pIaQJNlOO1CALbRVGqq9q2uHghw0eOAfo0dFOR9aSI,26690
django_extensions/management/commands/set_default_site.py,sha256=jv9a9EN81_tlGR75Eua-P8b2fo6oIZ_UIgWxfvsuFjM,2915
django_extensions/management/commands/set_fake_emails.py,sha256=LLTNC6QzGIxWwwv517Tu5U3FSVuf01SJfYpC7h3NjWE,4228
django_extensions/management/commands/set_fake_passwords.py,sha256=hcLUiXu5iu6l3ACpObEEIzPJm6NXOQGO182nE60BxRU,1829
django_extensions/management/commands/shell_plus.py,sha256=UKnpROY0nrQ2LVAdmqSucxxZvYG0FfGeAVh721cP3ak,25055
django_extensions/management/commands/show_permissions.py,sha256=sCDPjeUus-Wl8-Imq0oSvsh_xV73g4QblcrMCPTrfq0,2383
django_extensions/management/commands/show_template_tags.py,sha256=gTJtfR2VqjJJ4j5waq4XJ7oYNWeix5CLA-iS_3pgYvE,3939
django_extensions/management/commands/show_urls.py,sha256=C96jaQu-FWWW9hJwplOBV_dnGah-K3HYauC3cfCBKhs,10482
django_extensions/management/commands/sqlcreate.py,sha256=HSpvqRK4iXxx_SUEdnoX5os2RScqhmyNfC1BCYcleh0,4780
django_extensions/management/commands/sqldiff.py,sha256=ScncsaTLd8dZWDkCFRB46ikYYY--a1o9ODRM7VqeiTs,71112
django_extensions/management/commands/sqldsn.py,sha256=MrSb6bMnc3pV1tL4CzPugn_Gu2qa-3n9o6Z_0kABoRY,6632
django_extensions/management/commands/sync_s3.py,sha256=8Yrm0NYL54EK0cnQMjxsu6U1JoPSzN9ZK9tITttEuZA,16905
django_extensions/management/commands/syncdata.py,sha256=nbu-FRVZk2GJiooAkDB0q14O_FS6N-kXrqClLoXmDYg,11091
django_extensions/management/commands/unreferenced_files.py,sha256=R7Ke26ubKUoQPXILJzUQoYn-R1_cFkKjOlfczTbHtag,1801
django_extensions/management/commands/update_permissions.py,sha256=QxQitwADzlTfKPgJhMBngaKVrwbR_KGzVirDQWgmaus,2977
django_extensions/management/commands/validate_templates.py,sha256=HwDA1394LpFgh2lVdUIyGPPXYO5_7VBDiiK51LtKfAE,4064
django_extensions/management/debug_cursor.py,sha256=cUERDVfj6KTjd43oGYhsoBn66yzNtktj1x2Xo70fWLw,4846
django_extensions/management/email_notifications.py,sha256=Ac2mOcehDK6FOKcIeus4YfEPSvJTqFQsnKOoDw4qDT8,5333
django_extensions/management/jobs.py,sha256=Ihd4dVWdEWwTsc25YVmbeYBl2_eWvcOgqC8m1L79pdk,5432
django_extensions/management/modelviz.py,sha256=75JKEWC2QzNhtl0T_cjo15iLQ_jYWEQzQLp_r80dP6c,19546
django_extensions/management/mysql.py,sha256=GrAJHzMGZ7UUkmP6fovKQJSZtqNSSjSY4V-A6tBhbL8,1557
django_extensions/management/notebook_extension.py,sha256=2UzcvcUpaD704dLx4uSVY3rZGN6RQ-L_OVOgjdDYKyI,324
django_extensions/management/shells.py,sha256=xVscuW74iFhwTgzqjrNntmZxCypbSYp7h8uvFEEwJvI,18095
django_extensions/management/signals.py,sha256=yWTcyz8hhcB-PLJKeH2UWOAypCIvaeijKy3CfNJ3ZhU,307
django_extensions/management/technical_response.py,sha256=8WJUUH-ZDpbGrH6Oc0mHTLnIN5vQLOiB0aeMMvENqjs,1782
django_extensions/management/utils.py,sha256=nK9pNrF2esYxIufxh5U9aKLKiZj2CVWK3nIRCmD6OJo,2351
django_extensions/models.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/mongodb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/mongodb/__pycache__/__init__.cpython-313.pyc,,
django_extensions/mongodb/__pycache__/models.cpython-313.pyc,,
django_extensions/mongodb/fields/__init__.py,sha256=nM7ikY-oMKxUBl5jSPiZq8INwjVZQraqWqXmwOl8hSg,9489
django_extensions/mongodb/fields/__pycache__/__init__.cpython-313.pyc,,
django_extensions/mongodb/fields/__pycache__/json.cpython-313.pyc,,
django_extensions/mongodb/fields/json.py,sha256=8JA5_ObbTlENTkVfHxeE_RtMYiYQw_5QSwaPGfPWKDk,2154
django_extensions/mongodb/models.py,sha256=N-ABdyit6Y93diCnWsO0J8C7U1DyPbLrtNRuSGgmwh8,2458
django_extensions/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/settings.py,sha256=9G14bG8M-1ucGFVh7PVXuiMdwaJ-QJ8jV58i4EnTm08,1369
django_extensions/static/django_extensions/css/jquery.autocomplete.css,sha256=3yUz9XJFKdXHv34Xe4QNWjA9ghEr2ieEoQ0KaO3e49Q,740
django_extensions/static/django_extensions/img/indicator.gif,sha256=0-OUTUZJRQ3uZqVcae7O0tgltsoaNJ9yx1_TeArj8AY,1553
django_extensions/static/django_extensions/js/jquery.ajaxQueue.js,sha256=hPrJgQn9AOWP5fixvJ2ty6_ncPDKXGtW3hjZY6omo70,2800
django_extensions/static/django_extensions/js/jquery.autocomplete.js,sha256=5DV9zxN6TgVpbkscqlkoiK5qhEEGdmxV2v7d265QQog,36679
django_extensions/static/django_extensions/js/jquery.bgiframe.js,sha256=ACFjwe9ie5i4LysU-w9gpisHaCzHbS0kEOtIL0UcDM4,1821
django_extensions/templates/django_extensions/graph_models/django2018/digraph.dot,sha256=Y0wcDGN58wjZQTvnytRcHzu3H0gOmnq_Qf4CkWTwrTQ,904
django_extensions/templates/django_extensions/graph_models/django2018/label.dot,sha256=vsKxchMm6DQVTPx8gFxijpHOGNJuqdItN2WEI8mvc14,1875
django_extensions/templates/django_extensions/graph_models/django2018/relation.dot,sha256=6KlECRFCmCmTTOQs5vYEr2sPWdDgiUbgH4ZkWLt_SjE,589
django_extensions/templates/django_extensions/graph_models/django2018style/digraph.dot,sha256=X5gkfd8UNCo8JhlONpe30hxb4ZtomxccBUeC254GlG0,856
django_extensions/templates/django_extensions/graph_models/django2018style/label.dot,sha256=hk1x5SYFEpxwccrqEV8ay80fh7YYi3TY0fWfbCS5820,1983
django_extensions/templates/django_extensions/graph_models/django2018style/relation.dot,sha256=6KlECRFCmCmTTOQs5vYEr2sPWdDgiUbgH4ZkWLt_SjE,589
django_extensions/templates/django_extensions/graph_models/original/digraph.dot,sha256=mwfMx95mrkZofdoGj7E4RVfQilGgSNV45-ZklCoBd0Y,909
django_extensions/templates/django_extensions/graph_models/original/label.dot,sha256=0-UHhFDl-XTkOr1wUAoEBd-xSfnAea922vUX0N1cCeQ,1697
django_extensions/templates/django_extensions/graph_models/original/relation.dot,sha256=Y-wvocs_14QreSILBv9ESWvnF6B3pUcrRrjx_q0oINk,591
django_extensions/templates/django_extensions/widgets/foreignkey_searchinput.html,sha256=8DhLt6B0oUlpVq1gSoPy4uImyJxUueUczwYHraZeKNg,2032
django_extensions/templatetags/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_extensions/templatetags/__pycache__/__init__.cpython-313.pyc,,
django_extensions/templatetags/__pycache__/debugger_tags.cpython-313.pyc,,
django_extensions/templatetags/__pycache__/highlighting.cpython-313.pyc,,
django_extensions/templatetags/__pycache__/indent_text.cpython-313.pyc,,
django_extensions/templatetags/__pycache__/syntax_color.cpython-313.pyc,,
django_extensions/templatetags/__pycache__/widont.cpython-313.pyc,,
django_extensions/templatetags/debugger_tags.py,sha256=I-IjazMNelcBx9tQ41-NA0ERntY04nrj1FFdYssQg9U,596
django_extensions/templatetags/highlighting.py,sha256=F32yOpVmcv3MvlD2Se-U6zTLBDNI3k1KW5nI4HieNgY,3103
django_extensions/templatetags/indent_text.py,sha256=kXmJmcrezyoHyTqsN1jxKljLCiLMMdakYls4E_dn5-c,1750
django_extensions/templatetags/syntax_color.py,sha256=d_zCxFGNr3WXHHwtLwcSKscxFMcrTBWJIrZvoVWPmVg,3204
django_extensions/templatetags/widont.py,sha256=kwqiQYOuCIxXGVYomacPzbZ9bcH2Ba9wEeDMiCWEckE,1957
django_extensions/utils/__init__.py,sha256=Xb0RrRwc1dqCwkASV8I2MSLy14c_FhmE1HeaxxaeO1E,70
django_extensions/utils/__pycache__/__init__.cpython-313.pyc,,
django_extensions/utils/__pycache__/deprecation.cpython-313.pyc,,
django_extensions/utils/__pycache__/dia2django.cpython-313.pyc,,
django_extensions/utils/__pycache__/internal_ips.cpython-313.pyc,,
django_extensions/utils/deprecation.py,sha256=t2fLgdgEj2l2dp_8S581QyDMwA2sg7OhE33AFYYxjts,156
django_extensions/utils/dia2django.py,sha256=YuVbOTHIqYw9QcF1lHVA05EjMZvAwGcuVNaRGj0SfYo,12183
django_extensions/utils/internal_ips.py,sha256=rmqZttBwaTWbPHW3DRGHtcA3HXT8NIpQ0TG7Sar44Ls,1952
django_extensions/validators.py,sha256=mkhSbBOFggSfycePTLf1vkxxgC2tP44JGjUT8vhRMkE,4023
