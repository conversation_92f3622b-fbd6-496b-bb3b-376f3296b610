"""
Development Settings for Monorepo + T3 Stack

Optimized for local development with hot reload support
and frontend integration (CRA + Next.js).
"""

from .base import *  # noqa: F403,F401

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# Development hosts
ALLOWED_HOSTS = [
    "localhost",
    "127.0.0.1",
    "0.0.0.0",
    "backend",  # Docker service name
]

# CORS configuration for frontend development
# Support for CRA (localhost:3000) and Next.js (localhost:3001)
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # CRA development server
    "http://localhost:3001",  # Next.js development server
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "http://localhost:8000",  # Django development server
    "http://127.0.0.1:8000",
]

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = False  # Explicit origins for security

# CSRF configuration synchronized with CORS
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

# Development-specific middleware
MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

# Development database (can override with local settings)
DATABASES["default"].update(  # noqa: F405
    {
        "OPTIONS": {
            "connect_timeout": 10,
            "application_name": "novel_website_dev",
        }
    }
)

# Development cache (Redis with shorter timeout)
CACHES["default"]["TIMEOUT"] = 300  # 5 minutes for development  # noqa: F405

# Development logging (more verbose)
LOGGING["loggers"]["django"]["level"] = "DEBUG"  # noqa: F405
LOGGING["loggers"]["apps"] = {  # noqa: F405
    "handlers": ["console"],
    "level": "DEBUG",
    "propagate": False,
}

# Development email backend
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# Development file uploads
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB

# Django Extensions is already included in base.py THIRD_PARTY_APPS

# Development security settings (relaxed)
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# Development static files (no compression)
STATICFILES_STORAGE = "django.contrib.staticfiles.storage.StaticFilesStorage"

# Development REST Framework settings
REST_FRAMEWORK.update(  # noqa: F405
    {
        "DEFAULT_RENDERER_CLASSES": [
            "rest_framework.renderers.JSONRenderer",
            "rest_framework.renderers.BrowsableAPIRenderer",  # API browser for dev
        ],
    }
)

# Development admin URL (can be customized)
ADMIN_URL = "admin/"
