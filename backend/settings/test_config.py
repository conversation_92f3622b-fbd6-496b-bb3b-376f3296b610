"""
Test settings for Django configuration check
"""

from .base import *  # noqa: F403,F401

# Override database to use SQLite for testing
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    }
}

# Override cache to use dummy cache
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.dummy.DummyCache",
    }
}

# Turn off debug for testing
DEBUG = False

# Simple allowed hosts for testing
ALLOWED_HOSTS = ["testserver", "localhost"]
