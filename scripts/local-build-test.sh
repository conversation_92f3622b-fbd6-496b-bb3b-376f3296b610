#!/bin/bash

# 本地構建測試腳本
# 🎯 目標：測試 Docker BuildKit Cache 是否正常運作
# 🚀 策略：本地構建 + ARM64 + 快取驗證

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查 ECR 存取權限
check_ecr_access() {
    local ecr_image="509399605447.dkr.ecr.ap-northeast-1.amazonaws.com/novel-node-base:20-alpine"
    
    log_info "🔍 檢查 ECR 私有映像存取權限..."
    
    if docker manifest inspect "$ecr_image" &> /dev/null; then
        log_success "✅ ECR 私有映像可存取"
        return 0
    else
        log_warning "⚠️ ECR 私有映像無法存取（需要 AWS 認證）"
        return 1
    fi
}

# 測試 Base Image 構建
test_base_image() {
    log_info "🏗️ 測試 Frontend Base Image 構建..."

    # 檢查 ECR 存取權限
    if ! check_ecr_access; then
        log_warning "跳過本地 Base Image 測試（ECR 依賴無法滿足）"
        log_info "💡 提示：本地測試需要 AWS ECR 認證才能存取私有基礎映像"
        log_info "💡 替代方案：CI 會自動進行完整測試"
        return 0  # 不算失敗，只是跳過
    fi

    local image_tag="novel-frontend-base:local"
    local start_time=$(date +%s)

    if docker build \
        --platform linux/arm64 \
        -t "$image_tag" \
        -f infra/docker/frontend-deps.Dockerfile \
        --progress=plain \
        .; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_success "Base Image 構建成功，耗時: ${duration} 秒"

        # 驗證內容
        log_info "驗證 Base Image 內容..."
        docker run --rm "$image_tag" sh -c "ls -la /workspace/node_modules/.bin | head -5"

    else
        log_error "Base Image 構建失敗"
        return 1
    fi
}

# 測試開發環境構建
test_dev_image() {
    log_info "🚀 測試開發環境構建..."

    # 檢查 ECR 存取權限
    if ! check_ecr_access; then
        log_warning "跳過本地開發環境測試（ECR 依賴無法滿足）"
        return 0  # 不算失敗，只是跳過
    fi

    local image_tag="novel-frontend-dev:local"
    local start_time=$(date +%s)

    if docker build \
        --platform linux/arm64 \
        -t "$image_tag" \
        -f infra/docker/frontend-dev.Dockerfile \
        --progress=plain \
        .; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_success "開發環境構建成功，耗時: ${duration} 秒"

    else
        log_error "開發環境構建失敗"
        return 1
    fi
}

# 測試快取效果
test_cache_effectiveness() {
    log_info "🧪 測試 Docker BuildKit Cache 效果..."

    # 檢查 ECR 存取權限
    if ! check_ecr_access; then
        log_warning "跳過快取測試（ECR 依賴無法滿足）"
        return 0  # 不算失敗，只是跳過
    fi

    # 第二次構建同樣的映像，應該會有快取效果
    log_info "進行第二次構建測試快取..."
    local start_time=$(date +%s)

    if docker build \
        --platform linux/arm64 \
        -t "novel-frontend-base:local-cache-test" \
        -f infra/docker/frontend-deps.Dockerfile \
        --progress=plain \
        . | tee build_output.log; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        # 檢查快取命中
        local cache_hits=$(grep -c "CACHED" build_output.log 2>/dev/null || echo "0")
        # 確保 cache_hits 是單一數字
        cache_hits=$(echo "$cache_hits" | head -1 | tr -d '\n')

        log_success "第二次構建完成，耗時: ${duration} 秒"
        log_info "快取命中次數: $cache_hits"

        if [[ $cache_hits -gt 0 ]]; then
            log_success "✅ Docker BuildKit Cache 正常運作"
        else
            log_warning "⚠️ 快取命中次數較少，可能需要檢查快取配置"
        fi

        rm -f build_output.log

    else
        log_error "快取測試失敗"
        return 1
    fi
}

# 主函數
main() {
    local exit_code=0

    log_info "🚀 開始本地 Docker 構建測試..."
    echo ""

    # 檢查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝"
        return 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker 服務未運行"
        return 1
    fi

    log_success "Docker 環境檢查通過"
    echo ""

    # 測試 Base Image
    if ! test_base_image; then
        exit_code=1
    fi
    echo ""

    # 測試開發環境
    if [[ $exit_code -eq 0 ]] && ! test_dev_image; then
        exit_code=1
    fi
    echo ""

    # 測試快取效果
    if [[ $exit_code -eq 0 ]] && ! test_cache_effectiveness; then
        exit_code=1
    fi
    echo ""

    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 所有本地構建測試通過！"
        log_info "✨ Frontend Base Image 架構運作正常"
        log_info "🔧 Debug 提示："
        log_info "   - 開發環境: docker-compose -f docker-compose.dev.yml up frontend-dev"
        log_info "   - 手動構建: docker build -t my-test -f infra/docker/frontend-tier2.Dockerfile ."
    else
        log_error "💥 本地構建測試失敗！"
    fi

    return $exit_code
}

# 執行主函數
main "$@"
