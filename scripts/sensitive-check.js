#!/usr/bin/env node
/**
 * Sensitive Information Check Script (Node.js version)
 * 檢查代碼中的敏感信息和硬編碼秘密
 * 
 * 這是從 scripts/check_sensitive.sh 遷移的 Node.js 版本
 * 用於 Husky + lint-staged 工作流程
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 定義需要檢查的敏感模式
const SENSITIVE_PATTERNS = [
    /password\s*=\s*['"][^'"]*['"]/i,
    /secret\s*=\s*['"][^'"]*['"]/i,
    /api_key\s*=\s*['"][^'"]*['"]/i,
    /token\s*=\s*['"][^'"]*['"]/i,
    /private_key/i,
    /-----BEGIN.*PRIVATE.*KEY-----/,
    /sk-[a-zA-Z0-9]{32,}/,
    /ghp_[a-zA-Z0-9]{36}/,
    /postgres:\/\/[^:]+:[^@]+@/
];

// 定義要忽略的目錄和文件
const IGNORE_DIRS = [
    '.git',
    'node_modules',
    'venv',
    '__pycache__',
    '.pytest_cache',
    'build',
    'dist',
    '.next',
    'coverage',
    'storybook-static',
    'sessionmanager-bundle',
    'staticfiles'
];

const IGNORE_FILES = [
    '*.log',
    '*.min.js',
    '*.map',
    '*.pyc',
    '*.pyo',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    'sensitive-check.js',
    'check_sensitive.sh',
    '.ci-config.json'  // 配置檔案包含模式定義，非真實敏感信息
];

// 顏色輸出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function shouldIgnoreFile(filePath) {
    const fileName = path.basename(filePath);
    const pathParts = filePath.split(path.sep);
    
    // 檢查是否在忽略目錄中 (使用 pathParts 避免部分匹配)
    for (const ignoreDir of IGNORE_DIRS) {
        if (pathParts.includes(ignoreDir)) {
            return true;
        }
    }
    
    // 檢查檔案名稱模式 (錨定完整匹配)
    for (const ignorePattern of IGNORE_FILES) {
        // 轉義特殊字符並將 * 替換為 .*
        const escapedPattern = ignorePattern
            .replace(/[.+^${}()|[\]\\]/g, '\\$&')
            .replace(/\*/g, '.*');
        const regex = new RegExp(`^${escapedPattern}$`);
        
        if (regex.test(fileName)) {
            return true;
        }
    }
    
    return false;
}

function checkSensitivePatterns(filesToCheck = null) {
    let foundIssues = 0;
    
    log('🔍 Scanning for hardcoded secrets...', 'blue');
    
    // 獲取要檢查的檔案列表
    let files;
    if (filesToCheck && filesToCheck.length > 0) {
        // 如果提供了特定檔案列表（來自 lint-staged）
        files = filesToCheck.filter(file => fs.existsSync(file) && !shouldIgnoreFile(file));
    } else {
        // 檢查所有 Git 追蹤的檔案
        try {
            const gitFiles = execSync('git ls-files', { encoding: 'utf-8' }).trim().split('\n');
            files = gitFiles.filter(file => !shouldIgnoreFile(file));
        } catch (error) {
            log('❌ Error getting Git files: ' + error.message, 'red');
            return 1;
        }
    }
    
    log(`📋 Checking ${files.length} files...`, 'blue');
    
    for (const file of files) {
        if (!fs.existsSync(file)) continue;
        
        try {
            const content = fs.readFileSync(file, 'utf-8');
            const lines = content.split('\n');
            
            for (let lineNum = 0; lineNum < lines.length; lineNum++) {
                const line = lines[lineNum];
                
                // 跳過包含豁免註釋的行
                if (/gitleaks:allow|nosec|noqa|security:ignore/i.test(line)) {
                    continue;
                }
                
                for (const pattern of SENSITIVE_PATTERNS) {
                    if (pattern.test(line)) {
                        log(`⚠️  Found potential sensitive information in ${file}:${lineNum + 1}`, 'yellow');
                        log(`   ${line.trim()}`, 'red');
                        foundIssues++;
                    }
                }
            }
        } catch (error) {
            // 忽略無法讀取的檔案（二進制檔案等）
            continue;
        }
    }
    
    return foundIssues;
}

function checkEnvFiles() {
    log('📋 Checking for .env files in repository...', 'blue');
    
    try {
        const gitFiles = execSync('git ls-files', { encoding: 'utf-8' }).trim().split('\n');
        const problematicEnvs = gitFiles.filter(file => {
            // 檢查檔案是否存在再進行模式匹配
            if (!fs.existsSync(file)) {
                return false;
            }
            // 允許測試環境配置檔案和範例檔案
            return /\.env(\.|$)/.test(file) && !/\.(example|template|sample|test)$/.test(file);
        });
        
        if (problematicEnvs.length > 0) {
            log('⚠️  Found .env files that should not be committed:', 'yellow');
            problematicEnvs.forEach(file => log(`   ${file}`, 'red'));
            return 1;
        }
        
        log('✅ No problematic .env files found in Git', 'green');
        return 0;
    } catch (error) {
        log('❌ Error checking .env files: ' + error.message, 'red');
        return 1;
    }
}

function checkLargeFiles() {
    log('📋 Checking for large files (>5MB) in Git repository...', 'blue');
    
    try {
        const gitFiles = execSync('git ls-files', { encoding: 'utf-8' }).trim().split('\n');
        const largeFiles = [];
        
        for (const file of gitFiles) {
            // 檢查檔案是否存在且可讀取
            if (!fs.existsSync(file)) {
                continue;
            }
            
            try {
                const stats = fs.statSync(file);
                if (stats.size > 5242880) { // 5MB
                    largeFiles.push({ file, size: stats.size });
                }
            } catch (error) {
                // 忽略無法讀取的檔案（權限問題等）
                continue;
            }
        }
        
        if (largeFiles.length > 0) {
            log('⚠️  Found large files in Git repository that might contain sensitive data:', 'yellow');
            largeFiles.forEach(({ file, size }) => {
                const sizeMB = (size / 1024 / 1024).toFixed(2);
                log(`   ${file} (${sizeMB}MB)`, 'red');
            });
            return 1;
        }
        
        log('✅ No suspicious large files found in Git', 'green');
        return 0;
    } catch (error) {
        log('❌ Error checking large files: ' + error.message, 'red');
        return 1;
    }
}

function main() {
    const args = process.argv.slice(2);
    let exitCode = 0;
    
    log('🔐 Starting sensitive information check...', 'blue');
    log(`📂 Working directory: ${process.cwd()}`, 'blue');
    log('');
    
    // 檢查是否為 lint-staged 模式（傳入了具體檔案）
    const filesToCheck = args.length > 0 ? args : null;
    
    if (filesToCheck) {
        log(`📋 Lint-staged mode: checking ${filesToCheck.length} files`, 'blue');
    }
    
    // 執行各項檢查
    const sensitiveIssues = checkSensitivePatterns(filesToCheck);
    if (sensitiveIssues > 0) {
        log('❌ Found potential hardcoded secrets', 'red');
        exitCode = 1;
    } else {
        log('✅ No hardcoded secrets detected', 'green');
    }
    
    log('');
    
    // 只在完整檢查模式下執行這些檢查
    if (!filesToCheck) {
        if (checkEnvFiles() !== 0) {
            log('❌ Found problematic .env files', 'red');
            exitCode = 1;
        }
        
        log('');
        
        if (checkLargeFiles() !== 0) {
            log('❌ Found suspicious large files', 'red');
            exitCode = 1;
        }
        
        log('');
    }
    
    if (exitCode === 0) {
        log('✅ Sensitive information check completed successfully', 'green');
        log('💡 Note: This is a basic check. Consider using specialized tools like:', 'blue');
        log('   - gitleaks', 'blue');
        log('   - truffleHog', 'blue');
        log('   - git-secrets', 'blue');
        log('   - Doppler CLI for secret management', 'blue');
    } else {
        log('⚠️  Sensitive information check found potential issues', 'yellow');
        log('📝 Please review the findings above and:', 'blue');
        log('   1. Remove any hardcoded secrets', 'blue');
        log('   2. Use environment variables or secret management tools', 'blue');
        log('   3. Consider using Doppler for secret management', 'blue');
        log('   4. Add sensitive patterns to .gitignore', 'blue');
    }
    
    process.exit(exitCode);
}

// 如果直接執行此腳本
if (require.main === module) {
    main();
}

module.exports = {
    checkSensitivePatterns,
    checkEnvFiles,
    checkLargeFiles
};