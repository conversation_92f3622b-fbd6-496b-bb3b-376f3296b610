#!/bin/bash
# Python linting script for lint-staged
# 統一管理 Python 虛擬環境啟用和工具執行

set -e

# 切換到 backend 目錄
cd backend

# 啟用虛擬環境（如果存在）
if [ -f venv/bin/activate ]; then
    source venv/bin/activate
fi

# 取得檔案列表
FILES="$@"

if [ -z "$FILES" ]; then
    echo "⚠️ No Python files to check"
    exit 0
fi

echo "🐍 Running Python linting tools for files: $FILES"

# 執行 black 格式檢查
echo "🔍 Running black --check --diff..."
python -m black --check --diff $FILES

# 執行 isort 排序檢查
echo "🔍 Running isort --check-only --diff..."
python -m isort --check-only --diff $FILES

# 執行 flake8 代碼質量檢查
echo "🔍 Running flake8..."
python -m flake8 --extend-ignore=E402,E203 $FILES

echo "✅ All Python linting checks passed!"