#!/bin/bash

# Monorepo CI Deployment Simulation Test v2
# 全新設計：基於 Monorepo 架構的智能緩存部署模擬
# 目標：30-60 秒完成緩存命中後的部署測試

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢測 Docker Compose 命令
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
else
    log_error "Docker Compose 未安裝或不可用"
    exit 1
fi

# 配置變數
COMPOSE_FILE="infra/docker/docker-compose.ci.yml"
COMPOSE_PROJECT="novel-ci-test-v3"
TIMEOUT=180  # 3 分鐘超時（相比舊版的 5 分鐘大幅縮短）
HEALTH_CHECK_RETRIES=20  # 減少重試次數
HEALTH_CHECK_INTERVAL=5  # 縮短檢查間隔

# 清理函數
cleanup() {
    log_info "🧹 清理測試環境..."
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" down -v --remove-orphans 2>/dev/null || true
    log_success "清理完成"
}

# 設置清理陷阱
trap cleanup EXIT

# 檢查前置條件
check_prerequisites() {
    log_info "🔍 檢查前置條件..."

    # 檢查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝"
        return 1
    fi

    # 檢查 Docker 服務
    if ! docker info &> /dev/null; then
        log_error "Docker 服務未運行"
        return 1
    fi

    # 檢查配置文件
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose 配置文件不存在: $COMPOSE_FILE"
        return 1
    fi

    # 檢查 Monorepo 結構
    if [[ ! -d "backend" ]]; then
        log_error "Backend 目錄不存在"
        return 1
    fi

    if [[ ! -d "apps/web-next" && ! -d "apps/web" ]]; then
        log_error "Frontend 目錄不存在 (apps/web-next 或 apps/web)"
        return 1
    fi

    # 優先檢查 Next.js 15 版本
    if [[ -d "apps/web-next" ]]; then
        log_info "✅ 檢測到 Next.js 15 App Router 版本 (apps/web-next)"
    elif [[ -d "apps/web" ]]; then
        log_warning "⚠️  檢測到舊版 CRA 版本 (apps/web) - 建議遷移至 apps/web-next"
    fi

    log_success "前置條件檢查通過"
    return 0
}

# 啟動服務
start_services() {
    log_info "🚀 啟動 Monorepo CI 部署模擬環境 v2..."

    # 清理可能存在的舊容器
    cleanup

    # [FIX] 強化環境變數檢查與設置映像標籤，確保使用預構建映像
    # 檢查關鍵環境變數是否存在
    if [[ -z "$ECR_REGISTRY" || "$ECR_REGISTRY" == "null" ]]; then
        log_warning "ECR_REGISTRY 未設置或為空，使用預設值"
        export ECR_REGISTRY="novel-web-ecr"
    fi

    if [[ -z "$BACKEND_IMAGE_TAG" || "$BACKEND_IMAGE_TAG" == "null" ]]; then
        log_warning "BACKEND_IMAGE_TAG 未設置或為空，使用 latest"
        export BACKEND_IMAGE_TAG="latest"
    fi

    if [[ -z "$FRONTEND_IMAGE_TAG" || "$FRONTEND_IMAGE_TAG" == "null" ]]; then
        log_warning "FRONTEND_IMAGE_TAG 未設置或為空，使用 latest"
        export FRONTEND_IMAGE_TAG="latest"
    fi

    log_info "使用映像配置："
    log_info "  - 後端映像: ${ECR_REGISTRY}/novel-web-backend:${BACKEND_IMAGE_TAG}"
    log_info "  - 前端映像: ${ECR_REGISTRY}/novel-web-frontend:${FRONTEND_IMAGE_TAG}"

    # [FIX] 添加環境變數驗證日誌，便於 CI 除錯
    log_info "環境變數驗證："
    log_info "  - ECR_REGISTRY: ${ECR_REGISTRY}"
    log_info "  - BACKEND_IMAGE_TAG: ${BACKEND_IMAGE_TAG}"
    log_info "  - FRONTEND_IMAGE_TAG: ${FRONTEND_IMAGE_TAG}"

    # [FIX] 在部署模擬前，先從 ECR 拉取預構建映像
    log_info "從 ECR 拉取預構建映像..."
    local backend_image="${ECR_REGISTRY}/novel-web-backend:${BACKEND_IMAGE_TAG}"
    local frontend_image="${ECR_REGISTRY}/novel-web-frontend:${FRONTEND_IMAGE_TAG}"

    # 驗證關鍵環境變數存在
    if [[ -z "$ECR_REGISTRY" ]]; then
        log_error "ECR_REGISTRY 未設置，無法使用預構建映像"
        return 1
    fi

    # [根本修復] 強制刪除可能存在的損壞容器和映像
    log_info "🧹 清理可能損壞的容器和映像..."
    
    # 停止並移除可能的殭屍容器
    docker ps -aq --filter "name=novel-*-ci-v*" | xargs -r docker rm -f 2>/dev/null || true
    
    # 清理可能損壞的映像標籤
    docker images --filter "reference=${ECR_REGISTRY}/novel-web-*" --format "{{.Repository}}:{{.Tag}}" | \
    grep -E "(none|<none>)" | xargs -r docker rmi -f 2>/dev/null || true
    
    log_success "容器和映像清理完成"

    # 檢查 ECR 映像存在性
    log_info "🗂️ 檢查 ECR 映像存在性..."

    # 檢查後端映像
    local backend_exists=false
    if aws ecr describe-images --repository-name novel-web-backend --image-ids imageTag="${BACKEND_IMAGE_TAG}" >/dev/null 2>&1; then
        log_success "✅ 後端映像 ${BACKEND_IMAGE_TAG} 存在於 ECR"
        backend_exists=true
    else
        log_warning "❗ 後端映像 ${BACKEND_IMAGE_TAG} 不存在，將使用 ${FALLBACK_BACKEND_TAG:-latest}"
    fi

    # 檢查前端映像
    local frontend_exists=false
    if aws ecr describe-images --repository-name novel-web-frontend --image-ids imageTag="${FRONTEND_IMAGE_TAG}" >/dev/null 2>&1; then
        log_success "✅ 前端映像 ${FRONTEND_IMAGE_TAG} 存在於 ECR"
        frontend_exists=true
    else
        log_warning "❗ 前端映像 ${FRONTEND_IMAGE_TAG} 不存在，將使用 ${FALLBACK_FRONTEND_TAG:-latest}"
    fi

    # 拉取後端映像（智能選擇標籤 + 重試機制）
    local backend_available=false
    if [[ "$backend_exists" == "true" ]]; then
        log_info "拉取後端映像: $backend_image"
        
        # [根本修復] 3 次重試機制
        local pull_retries=3
        local pull_success=false
        
        for i in $(seq 1 $pull_retries); do
            log_info "嘗試拉取後端映像 (第 $i 次)..."
            if docker pull "$backend_image"; then
                log_success "後端映像拉取成功 (第 $i 次嘗試)"
                backend_available=true
                pull_success=true
                break
            else
                log_warning "第 $i 次拉取失敗，等待 5 秒後重試..."
                sleep 5
            fi
        done
        
        if [[ "$pull_success" == "false" ]]; then
            log_warning "無法拉取後端映像: $backend_image (已重試 $pull_retries 次)"
        fi
    fi

    # 如果指定標籤不可用，嘗試 fallback
    if [[ "$backend_available" == "false" ]]; then
        log_warning "嘗試使用 ${FALLBACK_BACKEND_TAG:-latest} 標籤作為後備..."
        local backend_latest="${ECR_REGISTRY}/novel-web-backend:${FALLBACK_BACKEND_TAG:-latest}"
        if docker pull "$backend_latest"; then
            log_success "成功拉取後端映像後備版本: $backend_latest"
            docker tag "$backend_latest" "$backend_image"
            log_info "已將 ${FALLBACK_BACKEND_TAG:-latest} 標籤重新標記為: $backend_image"
            backend_available=true
        else
            log_warning "後端映像不可用，跳過後端測試"
        fi
    fi

    # 拉取前端映像（智能選擇標籤）
    local frontend_available=false
    if [[ "$frontend_exists" == "true" ]]; then
        log_info "拉取前端映像: $frontend_image"
        if docker pull "$frontend_image"; then
            log_success "前端映像拉取成功"
            frontend_available=true
        else
            log_warning "無法拉取前端映像: $frontend_image"
        fi
    fi

    # 如果指定標籤不可用，嘗試 fallback
    if [[ "$frontend_available" == "false" ]]; then
        log_warning "嘗試使用 ${FALLBACK_FRONTEND_TAG:-latest} 標籤作為後備..."
        local frontend_latest="${ECR_REGISTRY}/novel-web-frontend:${FALLBACK_FRONTEND_TAG:-latest}"
        if docker pull "$frontend_latest"; then
            log_success "成功拉取前端映像後備版本: $frontend_latest"
            docker tag "$frontend_latest" "$frontend_image"
            log_info "已將 ${FALLBACK_FRONTEND_TAG:-latest} 標籤重新標記為: $frontend_image"
            frontend_available=true
        else
            log_warning "前端映像不可用，跳過前端測試"
        fi
    fi

    # [FIX] 改進映像可用性檢查 - 容錯機制
    if [[ "$backend_available" == false && "$frontend_available" == false ]]; then
        log_warning "後端和前端映像都不可用，嘗試使用本地構建或跳過部署模擬"

        # 檢查是否可以跳過部署模擬（在某些 CI 情況下是合理的）
        if [[ "${SKIP_DEPLOYMENT_SIM:-false}" == "true" ]]; then
            log_warning "SKIP_DEPLOYMENT_SIM=true，跳過部署模擬測試"
            log_success "部署模擬測試已跳過（配置允許）"
            return 0
        fi

        log_error "無法進行部署模擬，請檢查映像構建狀態"
        log_error "如需跳過此測試，請設置環境變數 SKIP_DEPLOYMENT_SIM=true"
        return 1
    fi

    if [[ "$backend_available" == true ]]; then
        log_success "後端映像已準備就緒"
    fi
    if [[ "$frontend_available" == true ]]; then
        log_success "前端映像已準備就緒"
    fi

    log_success "所有必需映像已準備就緒"

    # [FIX] 啟動服務（使用預構建映像，--no-build 確保不會觸發構建）
    log_info "啟動服務（使用預構建映像，避免重複構建）..."
    log_info "執行指令: $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE -p $COMPOSE_PROJECT up -d --no-build"

    if ! $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" up -d --no-build; then
        log_error "服務啟動失敗。正在導出所有服務的日誌..."
        # 自動打印所有服務的日誌，以便快速診斷
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" logs --tail="100"

        # [FIX] 添加更詳細的容器狀態診斷
        log_error "正在檢查容器狀態..."
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" ps -a

        # [FIX] 檢查後端容器日誌 (使用正確的服務名稱)
        log_error "=== 後端容器日誌 (backend-ci) ==="
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" logs backend-ci || echo "無法獲取後端容器日誌"

        # [FIX] 檢查資料庫容器日誌
        log_error "=== 資料庫容器日誌 (postgres-ci) ==="
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" logs postgres-ci || echo "無法獲取資料庫容器日誌"

        # [FIX] 檢查 Redis 容器日誌
        log_error "=== Redis 容器日誌 (redis-ci) ==="
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" logs redis-ci || echo "無法獲取 Redis 容器日誌"

        # [FIX] 檢查前端容器日誌 (如果存在)
        log_error "=== 前端容器日誌 (frontend-ci) ==="
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" logs frontend-ci || echo "無法獲取前端容器日誌"

        log_error "日誌導出完畢。"
        return 1
    fi

    log_success "服務啟動命令執行完成"
    return 0
}

# 等待服務啟動
wait_for_services() {
    log_info "⏳ 等待服務啟動..."

    # 等待 PostgreSQL 和 Redis 健康檢查
    local db_services=("postgres-ci" "redis-ci")
    local retry_count=0

    while [[ $retry_count -lt $HEALTH_CHECK_RETRIES ]]; do
        local all_healthy=true

        for service in "${db_services[@]}"; do
            local health_status
            health_status=$($DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" ps -q "$service" | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "unknown")

            if [[ "$health_status" != "healthy" ]]; then
                log_info "服務 $service 狀態: $health_status"
                all_healthy=false
                break
            fi
        done

        if [[ "$all_healthy" == true ]]; then
            log_success "資料庫服務健康檢查通過"
            break
        fi

        retry_count=$((retry_count + 1))
        log_info "等待資料庫服務健康... ($retry_count/$HEALTH_CHECK_RETRIES)"
        sleep $HEALTH_CHECK_INTERVAL
    done

    if [[ $retry_count -ge $HEALTH_CHECK_RETRIES ]]; then
        log_error "資料庫服務健康檢查超時"
        return 1
    fi

    # 等待後端服務啟動（通過 API 檢查）
    log_info "等待後端服務啟動..."
    retry_count=0
    while [[ $retry_count -lt 15 ]]; do
        if curl -f -s "http://localhost:8001/api/v1/health/" > /dev/null 2>&1; then
            log_success "後端服務啟動成功"
            return 0
        fi
        retry_count=$((retry_count + 1))
        log_info "等待後端服務啟動... ($retry_count/15)"
        sleep 3
    done

    log_error "後端服務啟動超時"
    return 1
}

# 測試後端 API
test_backend_api() {
    log_info "🧪 測試後端 API..."

    local backend_url="http://localhost:8001"
    local api_endpoints=(
        "/api/v1/health/"
        "/api/v1/"
    )

    # 等待後端服務完全啟動
    local retry_count=0
    while [[ $retry_count -lt 15 ]]; do
        if curl -f -s "$backend_url/api/v1/health/" > /dev/null 2>&1; then
            break
        fi
        retry_count=$((retry_count + 1))
        log_info "等待後端 API 啟動... ($retry_count/15)"
        sleep 3
    done

    # 測試各個端點
    for endpoint in "${api_endpoints[@]}"; do
        log_info "測試端點: $endpoint"

        local response_code
        response_code=$(curl -s -o /dev/null -w "%{http_code}" "$backend_url$endpoint" || echo "000")

        if [[ "$response_code" =~ ^[23] ]]; then
            log_success "端點 $endpoint 響應正常 (HTTP $response_code)"
        else
            log_error "端點 $endpoint 響應異常 (HTTP $response_code)"
            return 1
        fi
    done

    return 0
}

# 主函數
main() {
    local exit_code=0
    local start_time=$(date +%s)

    log_info "🚀 開始 Monorepo CI 部署模擬測試 v2..."
    echo ""

    # 檢查前置條件
    if ! check_prerequisites; then
        log_error "前置條件檢查失敗"
        return 1
    fi

    echo ""

    # 啟動服務
    if ! start_services; then
        log_error "服務啟動失敗"
        exit_code=1
    fi

    echo ""

    # 等待服務健康
    if [[ $exit_code -eq 0 ]] && ! wait_for_services; then
        log_error "服務健康檢查失敗"
        exit_code=1
    fi

    echo ""

    # 測試後端 API
    if [[ $exit_code -eq 0 ]] && ! test_backend_api; then
        log_error "後端 API 測試失敗"
        exit_code=1
    fi

    echo ""

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 Monorepo CI 部署模擬測試 v2 全部通過！"
        log_success "⏱️ 執行時間: ${duration} 秒"
        log_info "✨ 部署安全網防線三：Monorepo 部署模擬測試 - 正常"

        if [[ $duration -le 60 ]]; then
            log_success "🚀 性能目標達成：執行時間 ≤ 60 秒"
        elif [[ $duration -le 120 ]]; then
            log_warning "⚡ 性能可接受：執行時間 ≤ 120 秒"
        else
            log_warning "🐌 性能需優化：執行時間 > 120 秒"
        fi
    else
        log_error "💥 Monorepo CI 部署模擬測試 v2 失敗！"
        log_error "⏱️ 執行時間: ${duration} 秒"
        log_error "🚨 部署安全網防線三：Monorepo 部署模擬測試 - 異常"
    fi

    return $exit_code
}

# 執行主函數
main "$@"
