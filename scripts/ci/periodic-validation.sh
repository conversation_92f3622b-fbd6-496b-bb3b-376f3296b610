#!/bin/bash
# 定期驗證腳本 - 檢查配置一致性和系統健康度
# 建議每週運行一次以確保配置與專案結構同步

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[VALIDATION]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[VALIDATION]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[VALIDATION]${NC} $1"
}

log_error() {
    echo -e "${RED}[VALIDATION]${NC} $1"
}

# 獲取專案根目錄
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
CONFIG_FILE="$PROJECT_ROOT/.ci-config.json"

# 驗證配置檔案有效性
validate_config_file() {
    log_info "📋 Validating CI configuration file..."
    
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "❌ Configuration file not found: $CONFIG_FILE"
        return 1
    fi
    
    # 檢查 JSON 語法
    if ! jq empty "$CONFIG_FILE" 2>/dev/null; then
        log_error "❌ Invalid JSON syntax in configuration file"
        return 1
    fi
    
    # 檢查必要欄位
    local required_fields=(
        ".branches.fullCheckBranches"
        ".legacyPathScanner.excludeFiles"
        ".sensitiveCheck.allowedEnvFiles"
        ".dockerVerification.enabled"
    )
    
    for field in "${required_fields[@]}"; do
        if ! jq -e "$field" "$CONFIG_FILE" >/dev/null 2>&1; then
            log_error "❌ Missing required field: $field"
            return 1
        fi
    done
    
    log_success "✅ Configuration file is valid"
    return 0
}

# 檢查排除規則的有效性
validate_exclude_rules() {
    log_info "🔍 Validating exclude rules against project structure..."
    
    local issues=0
    local exclude_files
    
    exclude_files=$(jq -r '.legacyPathScanner.excludeFiles[]' "$CONFIG_FILE" 2>/dev/null || echo "")
    
    while IFS= read -r exclude_path; do
        [[ -z "$exclude_path" ]] && continue
        
        # 跳過通配符和已知的移除目錄
        case "$exclude_path" in
            *\** | apps/web-sunset/* | frontend/* | archive/legacy/*)
                continue
                ;;
        esac
        
        # 檢查路徑是否存在
        if [[ "$exclude_path" == */ ]]; then
            # 目錄路徑
            if [ -d "$exclude_path" ]; then
                log_warning "⚠️ Excluded directory still exists: $exclude_path"
                log_info "   Consider if this should still be excluded"
                issues=$((issues + 1))
            fi
        else
            # 檔案路徑
            if [ -f "$exclude_path" ]; then
                log_warning "⚠️ Excluded file still exists: $exclude_path"
                log_info "   Consider if this should still be excluded"
                issues=$((issues + 1))
            fi
        fi
    done <<< "$exclude_files"
    
    if [ $issues -eq 0 ]; then
        log_success "✅ Exclude rules validation passed"
    else
        log_warning "⚠️ Found $issues potential issues with exclude rules"
    fi
    
    return 0
}

# 檢查 Monorepo 結構變化
check_monorepo_structure() {
    log_info "🏗️ Checking monorepo structure changes..."
    
    # 檢查是否有新的應用程式或包
    local new_apps=()
    local new_packages=()
    
    # 掃描 apps/ 目錄
    if [ -d "apps" ]; then
        while IFS= read -r -d '' app_dir; do
            app_name=$(basename "$app_dir")
            case "$app_name" in
                web-next) continue ;; # 已知的應用程式
                web-sunset) continue ;; # 已移除的應用程式
                *)
                    new_apps+=("$app_name")
                    ;;
            esac
        done < <(find apps -maxdepth 1 -type d -print0 2>/dev/null)
    fi
    
    # 掃描 packages/ 目錄
    if [ -d "packages" ]; then
        while IFS= read -r -d '' pkg_dir; do
            pkg_name=$(basename "$pkg_dir")
            case "$pkg_name" in
                tailwind-config|typescript-config|api|ui) continue ;; # 已知的包
                *)
                    new_packages+=("$pkg_name")
                    ;;
            esac
        done < <(find packages -maxdepth 1 -type d -print0 2>/dev/null)
    fi
    
    # 報告新發現的結構
    if [ ${#new_apps[@]} -gt 0 ]; then
        log_warning "⚠️ New applications detected:"
        for app in "${new_apps[@]}"; do
            log_info "   - apps/$app"
        done
        log_info "   Consider updating CI configuration if needed"
    fi
    
    if [ ${#new_packages[@]} -gt 0 ]; then
        log_warning "⚠️ New packages detected:"
        for pkg in "${new_packages[@]}"; do
            log_info "   - packages/$pkg"
        done
        log_info "   Consider updating CI configuration if needed"
    fi
    
    if [ ${#new_apps[@]} -eq 0 ] && [ ${#new_packages[@]} -eq 0 ]; then
        log_success "✅ No unexpected structure changes detected"
    fi
    
    return 0
}

# 審查 Bypass 使用情況
audit_bypass_usage() {
    log_info "📊 Auditing bypass usage in the last 30 days..."
    
    local bypass_count=0
    local recent_bypasses=()
    
    # 檢查 git log 中的 bypass 關鍵字
    if git log --since="30 days ago" --grep="bypass\|skip\|emergency" --oneline >/dev/null 2>&1; then
        while IFS= read -r commit; do
            recent_bypasses+=("$commit")
            bypass_count=$((bypass_count + 1))
        done < <(git log --since="30 days ago" --grep="bypass\|skip\|emergency" --oneline 2>/dev/null || true)
    fi
    
    # 檢查 bypass 日誌檔案
    if [ -f ".ci-bypass.log" ]; then
        local log_entries
        log_entries=$(wc -l < .ci-bypass.log 2>/dev/null || echo "0")
        log_info "📄 Found $log_entries entries in bypass log file"
        
        if [ "$log_entries" -gt 0 ]; then
            log_info "Recent bypass log entries:"
            tail -5 .ci-bypass.log 2>/dev/null || true
        fi
    fi
    
    # 報告結果
    if [ $bypass_count -eq 0 ]; then
        log_success "✅ No bypass usage found in recent commits"
    elif [ $bypass_count -le 2 ]; then
        log_warning "⚠️ Found $bypass_count bypass-related commits (acceptable)"
        for commit in "${recent_bypasses[@]}"; do
            log_info "   $commit"
        done
    else
        log_warning "⚠️ Found $bypass_count bypass-related commits (review recommended)"
        log_info "Recent bypass commits:"
        for commit in "${recent_bypasses[@]:0:5}"; do
            log_info "   $commit"
        done
        if [ $bypass_count -gt 5 ]; then
            log_info "   ... and $((bypass_count - 5)) more"
        fi
        log_info "Consider reviewing bypass usage patterns"
    fi
    
    return 0
}

# 更新遺留模式檢查
update_legacy_patterns() {
    log_info "🔄 Checking for potential new legacy patterns..."
    
    # 搜尋可能的新遺留模式
    local potential_patterns=()
    
    # 檢查是否有新的 novel.* 模式
    if git ls-files | xargs grep -l "novel\." 2>/dev/null | grep -v ".ci-config.json" | head -5; then
        log_info "Found files containing 'novel.' patterns:"
        git ls-files | xargs grep -l "novel\." 2>/dev/null | grep -v ".ci-config.json" | head -5 | while read -r file; do
            log_info "   $file"
            # 顯示具體的匹配行
            grep -n "novel\." "$file" 2>/dev/null | head -3 | while read -r line; do
                log_info "     $line"
            done
        done
    else
        log_success "✅ No unexpected novel.* patterns found"
    fi
    
    return 0
}

# 生成驗證報告
generate_validation_report() {
    log_info "📊 Generating periodic validation report..."
    
    local report_file="periodic-validation-report.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local commit_sha=$(git rev-parse HEAD)
    local branch=$(git rev-parse --abbrev-ref HEAD)
    
    # 收集統計信息
    local total_files
    local total_apps
    local total_packages
    
    total_files=$(git ls-files | wc -l)
    total_apps=$(find apps -maxdepth 1 -type d 2>/dev/null | wc -l)
    total_packages=$(find packages -maxdepth 1 -type d 2>/dev/null | wc -l)
    
    cat > "$report_file" << EOF
{
  "timestamp": "$timestamp",
  "commit": "$commit_sha",
  "branch": "$branch",
  "validation_type": "periodic",
  "project_stats": {
    "total_files": $total_files,
    "total_apps": $total_apps,
    "total_packages": $total_packages
  },
  "checks_performed": [
    "config_file_validation",
    "exclude_rules_validation", 
    "monorepo_structure_check",
    "bypass_usage_audit",
    "legacy_patterns_update"
  ],
  "next_validation_due": "$(date -u -d '+7 days' +"%Y-%m-%dT%H:%M:%SZ")",
  "recommendations": [
    "Review exclude rules if structure changes detected",
    "Monitor bypass usage patterns",
    "Update CI configuration for new apps/packages"
  ]
}
EOF
    
    log_success "✅ Validation report generated: $report_file"
    
    return 0
}

# 主函數
main() {
    log_info "🚀 Starting Periodic Validation"
    log_info "📅 Timestamp: $(date -u +"%Y-%m-%d %H:%M:%S UTC")"
    log_info "📂 Project Root: $PROJECT_ROOT"
    echo ""
    
    local exit_code=0
    
    # 驗證配置檔案
    if ! validate_config_file; then
        log_error "Configuration validation failed"
        exit_code=1
    fi
    
    echo ""
    
    # 驗證排除規則
    if ! validate_exclude_rules; then
        log_error "Exclude rules validation failed"
        exit_code=1
    fi
    
    echo ""
    
    # 檢查 Monorepo 結構
    if ! check_monorepo_structure; then
        log_error "Monorepo structure check failed"
        exit_code=1
    fi
    
    echo ""
    
    # 審查 Bypass 使用
    if ! audit_bypass_usage; then
        log_error "Bypass usage audit failed"
        exit_code=1
    fi
    
    echo ""
    
    # 更新遺留模式
    if ! update_legacy_patterns; then
        log_error "Legacy patterns update failed"
        exit_code=1
    fi
    
    echo ""
    
    # 生成報告
    generate_validation_report
    
    echo ""
    
    if [ $exit_code -eq 0 ]; then
        log_success "🎉 Periodic validation completed successfully!"
        log_info "💡 Next validation recommended in 7 days"
    else
        log_error "💥 Periodic validation detected issues!"
        log_error "📝 Please review the findings above"
    fi
    
    return $exit_code
}

# 執行主函數
main "$@"