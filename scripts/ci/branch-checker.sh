#!/bin/bash
# 分支檢查工具 - 根據 .ci-config.json 判斷分支檢查策略

set -e

# 獲取專案根目錄
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
CONFIG_FILE="$PROJECT_ROOT/.ci-config.json"

# 檢查 jq 是否可用
if ! command -v jq &> /dev/null; then
    echo "❌ 錯誤：需要安裝 jq 來解析配置檔案"
    exit 1
fi

# 獲取當前分支
get_current_branch() {
    git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown"
}

# 檢查分支類型
check_branch_type() {
    local branch="$1"
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        echo "⚠️ 配置檔案不存在，使用預設規則"
        # 預設：main/master 為完整檢查，其他為快速檢查
        case "$branch" in
            main|master|production|staging)
                echo "full"
                ;;
            docs/*|readme/*)
                echo "skip"
                ;;
            *)
                echo "quick"
                ;;
        esac
        return
    fi
    
    # 從配置檔案讀取分支規則
    local full_branches
    local quick_branches  
    local skip_branches
    
    full_branches=$(jq -r '.branches.fullCheckBranches[]' "$CONFIG_FILE" 2>/dev/null || echo "")
    quick_branches=$(jq -r '.branches.quickCheckBranches[]' "$CONFIG_FILE" 2>/dev/null || echo "")
    skip_branches=$(jq -r '.branches.skipCheckBranches[]' "$CONFIG_FILE" 2>/dev/null || echo "")
    
    # 檢查完整檢查分支
    while IFS= read -r pattern; do
        [[ -z "$pattern" ]] && continue
        if [[ "$branch" == $pattern ]]; then
            echo "full"
            return
        fi
    done <<< "$full_branches"
    
    # 檢查跳過檢查分支
    while IFS= read -r pattern; do
        [[ -z "$pattern" ]] && continue
        if [[ "$branch" == $pattern ]]; then
            echo "skip"
            return
        fi
    done <<< "$skip_branches"
    
    # 檢查快速檢查分支
    while IFS= read -r pattern; do
        [[ -z "$pattern" ]] && continue
        if [[ "$branch" == $pattern ]]; then
            echo "quick"
            return
        fi
    done <<< "$quick_branches"
    
    # 預設為快速檢查
    echo "quick"
}

# 主函數
main() {
    local command="$1"
    local branch="$2"
    
    case "$command" in
        "get-branch")
            get_current_branch
            ;;
        "check-type")
            if [[ -z "$branch" ]]; then
                branch=$(get_current_branch)
            fi
            check_branch_type "$branch"
            ;;
        "is-full")
            if [[ -z "$branch" ]]; then
                branch=$(get_current_branch)
            fi
            if [[ "$(check_branch_type "$branch")" == "full" ]]; then
                exit 0
            else
                exit 1
            fi
            ;;
        "is-quick")
            if [[ -z "$branch" ]]; then
                branch=$(get_current_branch)
            fi
            if [[ "$(check_branch_type "$branch")" == "quick" ]]; then
                exit 0
            else
                exit 1
            fi
            ;;
        "is-skip")
            if [[ -z "$branch" ]]; then
                branch=$(get_current_branch)
            fi
            if [[ "$(check_branch_type "$branch")" == "skip" ]]; then
                exit 0
            else
                exit 1
            fi
            ;;
        *)
            echo "用法: $0 {get-branch|check-type|is-full|is-quick|is-skip} [branch-name]"
            echo ""
            echo "指令說明："
            echo "  get-branch    取得當前分支名稱"
            echo "  check-type    檢查分支檢查類型 (full/quick/skip)"
            echo "  is-full       檢查是否為完整檢查分支 (exit 0=是)"
            echo "  is-quick      檢查是否為快速檢查分支 (exit 0=是)"
            echo "  is-skip       檢查是否為跳過檢查分支 (exit 0=是)"
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"