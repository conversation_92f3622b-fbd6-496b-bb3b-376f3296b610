#!/bin/bash
# CI Security Check - 第二層安全防線
# 確保即使本地跳過檢查，CI 也能捕獲安全問題

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[CI-SECURITY]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[CI-SECURITY]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[CI-SECURITY]${NC} $1"
}

log_error() {
    echo -e "${RED}[CI-SECURITY]${NC} $1"
}

# 檢查是否在 CI 環境中
check_ci_environment() {
    if [ -z "${CI:-}" ] && [ -z "${GITHUB_ACTIONS:-}" ]; then
        log_warning "⚠️ 不在 CI 環境中運行，某些檢查可能被跳過"
        return 1
    fi
    return 0
}

# 檢查敏感信息（強制執行，不允許跳過）
run_sensitive_check() {
    log_info "🔍 Running mandatory sensitive information check..."
    
    # 忽略本地的 SKIP_SENSITIVE_CHECK 環境變數
    unset SKIP_SENSITIVE_CHECK
    
    if node scripts/sensitive-check.js; then
        log_success "✅ Sensitive information check passed"
        return 0
    else
        log_error "❌ Sensitive information check failed"
        log_error "🚨 CRITICAL: This is a CI security check and cannot be bypassed"
        return 1
    fi
}

# 檢查 bypass 使用情況
check_bypass_usage() {
    log_info "📋 Checking for bypass usage in this commit..."
    
    local bypass_found=false
    local bypass_files=()
    
    # 檢查 commit message 中是否提到 bypass
    if git log -1 --pretty=format:"%B" | grep -iE "(skip|bypass|emergency)" >/dev/null 2>&1; then
        log_warning "⚠️ Commit message mentions bypass/skip/emergency"
        bypass_found=true
    fi
    
    # 檢查是否有 bypass 日誌檔案
    if [ -f ".ci-bypass.log" ]; then
        log_warning "⚠️ Found bypass log file (.ci-bypass.log)"
        echo "📄 Recent bypass entries:"
        tail -10 .ci-bypass.log || true
        bypass_found=true
    fi
    
    # 檢查 git diff 中是否有新的跳過邏輯
    if git diff --name-only HEAD~1 HEAD | xargs -I {} grep -l "SKIP_" {} 2>/dev/null | grep -v ".ci-bypass.log" || true; then
        log_warning "⚠️ Found new skip logic in modified files"
        bypass_found=true
    fi
    
    if [ "$bypass_found" = true ]; then
        log_warning "🔍 Bypass usage detected - requiring additional scrutiny"
        
        # 在 main 分支上如果有 bypass 使用，則失敗
        local current_branch
        current_branch=$(git rev-parse --abbrev-ref HEAD)
        if [[ "$current_branch" =~ ^(main|master|production|staging)$ ]]; then
            log_error "❌ Bypass usage is not allowed on protected branch: $current_branch"
            return 1
        fi
        
        # 為 PR 添加標籤（如果在 GitHub Actions 中）
        if [ -n "${GITHUB_ACTIONS:-}" ] && [ -n "${GITHUB_TOKEN:-}" ]; then
            log_info "🏷️ Adding security review label to PR..."
            # 這裡可以添加 GitHub API 調用來標記 PR
        fi
        
        log_warning "⚠️ This PR requires additional security review due to bypass usage"
    else
        log_success "✅ No bypass usage detected"
    fi
    
    return 0
}

# 檢查遺留路徑（第二層掃描）
run_legacy_path_check() {
    log_info "🔍 Running secondary legacy path scan..."
    
    if ./scripts/ci/legacy-path-scanner.sh; then
        log_success "✅ Legacy path scan passed"
        return 0
    else
        log_error "❌ Legacy path scan failed"
        log_error "🚨 This indicates potential missed cleanup"
        return 1
    fi
}

# 驗證 Docker 配置（語法檢查）
verify_docker_configs() {
    log_info "🐳 Verifying Docker configurations..."
    
    local dockerfiles=(
        "infra/docker/frontend-deps.Dockerfile"
        "infra/docker/frontend-tier2.Dockerfile"
    )
    
    local errors=0
    
    for dockerfile in "${dockerfiles[@]}"; do
        if [ -f "$dockerfile" ]; then
            log_info "Checking: $dockerfile"
            if docker buildx build --check --platform linux/arm64 -f "$dockerfile" . >/dev/null 2>&1; then
                log_success "✅ $dockerfile syntax valid"
            else
                log_error "❌ $dockerfile syntax error"
                errors=$((errors + 1))
            fi
        fi
    done
    
    return $errors
}

# 生成安全報告
generate_security_report() {
    log_info "📊 Generating security check report..."
    
    local report_file="ci-security-report.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local commit_sha=$(git rev-parse HEAD)
    local branch=$(git rev-parse --abbrev-ref HEAD)
    
    cat > "$report_file" << EOF
{
  "timestamp": "$timestamp",
  "commit": "$commit_sha",
  "branch": "$branch",
  "ci_environment": "${CI:-false}",
  "github_actions": "${GITHUB_ACTIONS:-false}",
  "checks": {
    "sensitive_check": "completed",
    "bypass_usage": "completed", 
    "legacy_path": "completed",
    "docker_syntax": "completed"
  },
  "bypass_detected": $([ -f ".ci-bypass.log" ] && echo "true" || echo "false"),
  "security_level": "$([ "$branch" = "main" ] && echo "strict" || echo "standard")"
}
EOF
    
    log_success "✅ Security report generated: $report_file"
    
    # 上傳報告到 CI artifacts（如果在 GitHub Actions 中）
    if [ -n "${GITHUB_ACTIONS:-}" ]; then
        echo "security-report-path=$report_file" >> "$GITHUB_OUTPUT" 2>/dev/null || true
    fi
}

# 主函數
main() {
    log_info "🚀 Starting CI Security Check (Second Layer Defense)"
    log_info "📅 Timestamp: $(date -u +"%Y-%m-%d %H:%M:%S UTC")"
    echo ""
    
    local exit_code=0
    
    # 檢查 CI 環境
    if check_ci_environment; then
        log_info "✅ Running in CI environment"
    else
        log_warning "⚠️ Not in CI environment - some checks may be limited"
    fi
    
    echo ""
    
    # 執行敏感信息檢查
    if ! run_sensitive_check; then
        log_error "Sensitive information check failed"
        exit_code=1
    fi
    
    echo ""
    
    # 檢查 bypass 使用情況
    if ! check_bypass_usage; then
        log_error "Bypass usage check failed"
        exit_code=1
    fi
    
    echo ""
    
    # 執行遺留路徑檢查
    if ! run_legacy_path_check; then
        log_error "Legacy path check failed"
        exit_code=1
    fi
    
    echo ""
    
    # 驗證 Docker 配置
    if ! verify_docker_configs; then
        log_error "Docker configuration verification failed"
        exit_code=1
    fi
    
    echo ""
    
    # 生成安全報告
    generate_security_report
    
    echo ""
    
    if [ $exit_code -eq 0 ]; then
        log_success "🎉 All CI security checks passed!"
        log_info "🔒 Second layer defense: SECURE"
    else
        log_error "💥 CI security checks failed!"
        log_error "🚨 Second layer defense: BREACH DETECTED"
        
        if [ -n "${GITHUB_ACTIONS:-}" ]; then
            echo "::error title=Security Check Failed::CI security validation detected potential issues"
        fi
    fi
    
    return $exit_code
}

# 執行主函數
main "$@"