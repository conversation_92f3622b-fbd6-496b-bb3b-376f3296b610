#!/bin/bash

# 快速 Docker 驗證腳本（用於 feature 分支）
# 🎯 目標：快速驗證 Dockerfile 語法和基本構建能力
# ⚡ 策略：語法檢查 + dry-run 構建

set -euo pipefail

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查 Docker 是否可用
check_docker() {
    log_info "🐳 檢查 Docker 環境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝或不可用"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon 未運行"
        return 1
    fi
    
    log_success "Docker 環境正常"
}

# 驗證 Dockerfile 語法
validate_dockerfiles() {
    log_info "📝 驗證 Dockerfile 語法..."
    
    # 檢查是否有 ECR 存取權限
    local has_ecr_access=false
    if check_ecr_access; then
        has_ecr_access=true
    fi
    
    local dockerfiles=(
        "infra/docker/backend.Dockerfile"  # 不依賴 ECR，可以安全檢查
    )
    
    # 只有在有 ECR 存取權限時才檢查需要 ECR 的 Dockerfile
    if [[ "$has_ecr_access" == "true" ]]; then
        dockerfiles+=("infra/docker/frontend-deps.Dockerfile")
    else
        log_warning "⚠️ 跳過需要 ECR 認證的 Dockerfile 語法檢查"
    fi
    
    local errors=0
    
    for dockerfile in "${dockerfiles[@]}"; do
        if [[ -f "$dockerfile" ]]; then
            log_info "檢查: $dockerfile"
            
            # 使用 docker buildx build --check 進行語法檢查
            if docker buildx build \
                --check \
                --platform linux/arm64 \
                -f "$dockerfile" \
                . > /tmp/dockerfile_check.log 2>&1; then
                log_success "✅ $dockerfile 語法正確"
            else
                log_error "❌ $dockerfile 語法錯誤"
                errors=$((errors + 1))
            fi
        else
            log_warning "⚠️ $dockerfile 不存在，跳過檢查"
        fi
    done
    
    return $errors
}

# 檢查必要的依賴文件
check_build_dependencies() {
    log_info "📋 檢查構建依賴文件..."
    
    local required_files=(
        "package.json"
        "pnpm-lock.yaml"
        "apps/web-next/package.json"
        "packages/tailwind-config/package.json"
        "packages/typescript-config/package.json"
    )
    
    local missing_files=0
    
    for file in "${required_files[@]}"; do
        if [[ -f "$file" ]]; then
            log_success "✅ $file 存在"
        else
            log_error "❌ $file 缺失"
            missing_files=$((missing_files + 1))
        fi
    done
    
    return $missing_files
}

# 檢查 ECR 存取權限
check_ecr_access() {
    local ecr_image="509399605447.dkr.ecr.ap-northeast-1.amazonaws.com/novel-node-base:20-alpine"
    
    if docker manifest inspect "$ecr_image" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# 快速構建測試（檢查基本語法和初始層）
quick_build_test() {
    log_info "⚡ 快速構建測試（基本語法檢查）..."
    
    # 檢查是否有 ECR 存取權限
    if ! check_ecr_access; then
        log_warning "⚠️ ECR 私有映像無法存取，跳過構建測試"
        log_info "💡 完整測試將在 CI 中進行（有 ECR 認證）"
        return 0  # 不算失敗，只是跳過
    fi
    
    # 使用 --check 進行語法檢查而不實際構建
    if docker buildx build \
        --check \
        --platform linux/arm64 \
        -f infra/docker/frontend-deps.Dockerfile \
        . > /tmp/quick_build.log 2>&1; then
        
        log_success "✅ Dockerfile 語法檢查通過"
        
        return 0
    else
        log_error "❌ Dockerfile 語法檢查失敗"
        log_error "構建日誌："
        cat /tmp/quick_build.log
        return 1
    fi
}

# 主函數
main() {
    local exit_code=0
    
    log_info "⚡ 開始快速 Docker 驗證..."
    echo ""
    
    # 檢查 Docker 環境
    if ! check_docker; then
        exit_code=1
    fi
    
    echo ""
    
    # 檢查構建依賴
    if ! check_build_dependencies; then
        log_error "構建依賴檢查失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 驗證 Dockerfile 語法
    if ! validate_dockerfiles; then
        log_error "Dockerfile 語法驗證失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 快速構建測試
    if ! quick_build_test; then
        log_error "快速構建測試失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 清理臨時文件
    rm -f /tmp/quick_build.log
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 快速 Docker 驗證完成！"
        log_info "💡 注意：這是快速驗證，完整驗證將在 main 分支和 CI 中進行"
    else
        log_error "💥 快速 Docker 驗證失敗！"
        log_error "請修復上述問題後重試"
    fi
    
    return $exit_code
}

# 執行主函數
main "$@"