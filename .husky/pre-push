echo "🚀 Running pre-push checks..."

# 🧹 清理構建產物但保留依賴
echo "🧹 Cleaning build artifacts..."
pnpm run clean

# 🏗️ 完整 Turbo Build 檢查
echo "🏗️ Running Turbo build pipeline..."
if ! pnpm run build; then
    echo "❌ Build failed. Please fix build errors before pushing."
    exit 1
fi

# 🛡️ TypeScript 型別檢查
echo "🛡️ Running TypeScript type check..."
if ! pnpm run type-check; then
    echo "❌ TypeScript type check failed. Please fix type errors before pushing."
    exit 1
fi

# 🔒 Lockfile 同步驗證 (pre-push 關鍵檢查)
echo "🔒 Checking lockfile synchronization..."
if [ -f "pnpm-lock.yaml" ]; then
    # 使用 --frozen-lockfile --reporter=silent 進行靜默檢查，捕獲錯誤信息
    temp_output=$(mktemp)
    if ! pnpm install --frozen-lockfile --reporter=silent 2>"$temp_output"; then
        echo "❌ Lockfile 不一致"
        echo "📋 錯誤詳情："
        cat "$temp_output"
        echo ""
        echo "💡 請執行 'pnpm install' 更新 lockfile 並重新提交"
        echo "📋 這是推送前的最後檢查，確保依賴一致性"
        rm -f "$temp_output"
        exit 1
    fi
    rm -f "$temp_output"
    echo "✅ Lockfile 同步驗證通過"
else
    echo "⚠️  pnpm-lock.yaml 不存在，跳過 lockfile 檢查"
fi

# 🐳 Docker 建置驗證（智慧分支檢查 + 環境變數控制）
branch="$(git rev-parse --abbrev-ref HEAD)"
branch_checker="./scripts/ci/branch-checker.sh"

# 檢查分支類型
if [ -f "$branch_checker" ]; then
    check_type=$("$branch_checker" check-type "$branch")
else
    # 備用邏輯
    case "$branch" in
        main|master|production|staging) check_type="full" ;;
        docs/*|readme/*) check_type="skip" ;;
        *) check_type="quick" ;;
    esac
fi

echo "📋 Branch: $branch (check type: $check_type)"

# 檢查環境變數跳過控制
if [ "$SKIP_DOCKER_VERIFY" = "true" ] || [ "$SKIP_DOCKER_VERIFY" = "1" ]; then
    echo "⚠️  SKIP_DOCKER_VERIFY detected - Skipping Docker verification"
    echo "🔒 SECURITY WARNING: Docker verification has been bypassed"
    echo "📝 This should only be used in emergency situations"
    echo "💡 Ensure manual review and CI verification before merging"
    
    # 記錄到日誌檔案
    {
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Docker verification bypassed"
        echo "  Branch: $branch"
        echo "  User: $(whoami)"
        echo "  Environment: ${CI:-local}"
        echo "  PWD: $(pwd)"
        echo "---"
    } >> .ci-bypass.log
    
    # 要求明確確認（除非在 CI 環境中）
    if [ -z "$CI" ] && [ -z "$GITHUB_ACTIONS" ]; then
        echo ""
        echo "🔐 Manual confirmation required for local bypass:"
        echo "   Type 'BYPASS_DOCKER_VERIFY' to continue:"
        read -r confirmation
        if [ "$confirmation" != "BYPASS_DOCKER_VERIFY" ]; then
            echo "❌ Docker verification bypass cancelled"
            exit 1
        fi
        echo "✅ Docker verification bypass confirmed"
    fi
    
elif [ "$check_type" = "skip" ]; then
    echo "⏭️  Skipping Docker verification for documentation branch: $branch"
    
elif [ "$check_type" = "full" ]; then
    echo "🔒 Running full Docker verification on $check_type branch: $branch"
    if ! pnpm run docker-verify; then
        echo "❌ Docker build verification failed. Please check Docker configuration."
        echo "💡 To bypass in emergency, set SKIP_DOCKER_VERIFY=true (requires manual confirmation)"
        exit 1
    fi
    
elif [ "$check_type" = "quick" ]; then
    echo "⚡ Running quick Docker verification on $check_type branch: $branch"
    if ! pnpm run docker-verify-quick; then
        echo "❌ Quick Docker verification failed. Please check Docker configuration."
        echo "💡 Note: This is a lightweight check. Full verification will run on protected branches and CI."
        echo "💡 To bypass in emergency, set SKIP_DOCKER_VERIFY=true (requires manual confirmation)"
        exit 1
    fi
    
else
    echo "❓ Unknown check type: $check_type, falling back to quick verification"
    if ! pnpm run docker-verify-quick; then
        echo "❌ Docker verification failed. Please check Docker configuration."
        exit 1
    fi
fi

echo "✅ Pre-push checks completed successfully!"
echo "🚀 Ready to push to remote repository."