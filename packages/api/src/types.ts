/**
 * 共享 API 類型定義
 * 與 Django backend 保持一致
 */

// 基礎類型
export interface BaseModel {
  id: string | number;
  created_at: string;
  updated_at: string;
}

// 分類類型
export interface Category {
  id: number;
  name: string;
  slug: string;
}

// 標籤類型
export interface Tag {
  id: number;
  name: string;
  slug: string;
}

// 小說類型 (與 Django serializer 對應)
export interface Novel extends BaseModel {
  title: string;
  author: string;
  description: string;
  category: Category | null;
  views: number;
  favorites: number;
  status: 'ongoing' | 'completed';
  source: string;
  source_id: string;
  source_url: string;
  cover?: string;
  tags?: Tag[];
  chapters?: Chapter[];
}

// 章節類型
export interface Chapter extends BaseModel {
  novel: number; // Novel ID
  title: string;
  content: string;
  chapter_number: number;
  views: number;
  is_vip: boolean;
  source_url: string;
}

// API 響應類型
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// 查詢參數類型
export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  status?: string;
  ordering?: string;
}

// API 錯誤類型
export interface APIError {
  detail: string;
  code?: string;
  field_errors?: Record<string, string[]>;
}

// Slug 生成工具類型
export interface SlugMapping {
  id: number;
  slug: string;
  title: string;
}
