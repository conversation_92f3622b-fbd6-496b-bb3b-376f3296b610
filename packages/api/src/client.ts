/**
 * 統一 API 客戶端
 * 支援 Next.js 15 App Router 的 SSR/ISR
 */

import type {
  Novel,
  Chapter,
  PaginatedResponse,
  QueryParams,
  APIError,
  SlugMapping
} from './types';

// API 配置
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  timeout: 30000,
  defaultRevalidate: 60, // 60 秒 ISR
};

// 錯誤處理類
export class APIClientError extends Error {
  constructor(
    message: string,
    public status: number,
    public data?: APIError
  ) {
    super(message);
    this.name = 'APIClientError';
  }
}

// Slug 生成工具
export function generateSlug(title: string, id?: number): string {
  const baseSlug = title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格轉連字符
    .replace(/-+/g, '-') // 多個連字符合併
    .trim();
  
  // 添加 ID 確保唯一性
  return id ? `${baseSlug}-${id}` : baseSlug;
}

// 從 slug 提取 ID
export function extractIdFromSlug(slug: string): number | null {
  const match = slug.match(/-(\d+)$/);
  return match ? parseInt(match[1], 10) : null;
}

// 基礎 fetch 包裝器
async function apiFetch<T>(
  endpoint: string,
  options: RequestInit & {
    revalidate?: number;
    tags?: string[];
  } = {}
): Promise<T> {
  const { revalidate = API_CONFIG.defaultRevalidate, tags = [], ...fetchOptions } = options;
  
  const url = `${API_CONFIG.baseURL}${endpoint}`;
  
  try {
    const response = await fetch(url, {
      ...fetchOptions,
      next: {
        revalidate,
        tags,
      },
      headers: {
        'Content-Type': 'application/json',
        ...fetchOptions.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new APIClientError(
        `API request failed: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof APIClientError) {
      throw error;
    }
    throw new APIClientError(
      `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      0
    );
  }
}

/**
 * 小說 API 客戶端
 */
export const novelsAPI = {
  /**
   * 獲取小說列表
   */
  async getList(params: QueryParams = {}): Promise<PaginatedResponse<Novel>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    
    const query = searchParams.toString();
    const endpoint = `/novels/${query ? `?${query}` : ''}`;
    
    return apiFetch<PaginatedResponse<Novel>>(endpoint, {
      tags: ['novels', 'novels-list'],
    });
  },

  /**
   * 獲取小說詳情 (by ID)
   */
  async getDetail(id: number): Promise<Novel> {
    return apiFetch<Novel>(`/novels/${id}/`, {
      tags: ['novels', `novel-${id}`],
    });
  },

  /**
   * 通過 slug 獲取小說詳情
   */
  async getDetailBySlug(slug: string): Promise<Novel | null> {
    // 嘗試從 slug 提取 ID
    const id = extractIdFromSlug(slug);
    if (id) {
      try {
        return await this.getDetail(id);
      } catch (error) {
        if (error instanceof APIClientError && error.status === 404) {
          return null;
        }
        throw error;
      }
    }

    // 如果 slug 是純數字，直接嘗試作為 ID
    if (/^\d+$/.test(slug)) {
      try {
        return await this.getDetail(parseInt(slug, 10));
      } catch (error) {
        if (error instanceof APIClientError && error.status === 404) {
          return null;
        }
        throw error;
      }
    }

    // 否則需要搜索匹配的小說
    try {
      const response = await this.getList({ search: slug.replace(/-/g, ' ') });
      const novel = response.results.find(n => 
        generateSlug(n.title, Number(n.id)) === slug
      );
      return novel || null;
    } catch (error) {
      console.error('Error searching novel by slug:', error);
      return null;
    }
  },

  /**
   * 獲取小說的章節列表
   */
  async getChapters(novelId: number, params: QueryParams = {}): Promise<PaginatedResponse<Chapter>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    
    const query = searchParams.toString();
    const endpoint = `/novels/${novelId}/chapters/${query ? `?${query}` : ''}`;
    
    return apiFetch<PaginatedResponse<Chapter>>(endpoint, {
      tags: ['chapters', `novel-${novelId}-chapters`],
    });
  },
};

/**
 * 章節 API 客戶端
 */
export const chaptersAPI = {
  /**
   * 獲取章節內容
   */
  async getContent(id: number): Promise<Chapter> {
    return apiFetch<Chapter>(`/chapters/${id}/`, {
      tags: ['chapters', `chapter-${id}`],
    });
  },
};

/**
 * 健康檢查 API
 */
export const healthAPI = {
  async check(): Promise<{ status: string; timestamp: string }> {
    return apiFetch('/health/', {
      revalidate: 0, // 不快取健康檢查
    });
  },
};
