{"extends": "@novelwebsite/typescript-config/base.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}