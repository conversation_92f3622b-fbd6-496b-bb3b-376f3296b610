{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "ESNext", "moduleResolution": "bundler", "outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "types": ["node", "jest"]}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/__tests__/**"]}