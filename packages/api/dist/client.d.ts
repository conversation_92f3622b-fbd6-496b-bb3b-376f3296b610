/**
 * 統一 API 客戶端
 * 支援 Next.js 15 App Router 的 SSR/ISR
 */
import type { Novel, Chapter, PaginatedResponse, QueryParams, APIError } from './types';
export declare class APIClientError extends Error {
    status: number;
    data?: APIError | undefined;
    constructor(message: string, status: number, data?: APIError | undefined);
}
export declare function generateSlug(title: string, id?: number): string;
export declare function extractIdFromSlug(slug: string): number | null;
/**
 * 小說 API 客戶端
 */
export declare const novelsAPI: {
    /**
     * 獲取小說列表
     */
    getList(params?: QueryParams): Promise<PaginatedResponse<Novel>>;
    /**
     * 獲取小說詳情 (by ID)
     */
    getDetail(id: number): Promise<Novel>;
    /**
     * 通過 slug 獲取小說詳情
     */
    getDetailBySlug(slug: string): Promise<Novel | null>;
    /**
     * 獲取小說的章節列表
     */
    getChapters(novelId: number, params?: QueryParams): Promise<PaginatedResponse<Chapter>>;
    /**
     * 通過 slug 獲取小說 ID (映射函數)
     */
    getNovelIdBySlug(slug: string): Promise<number | null>;
};
/**
 * 章節導航資訊
 */
export interface ChapterNavigation {
    current: Chapter;
    previous: Chapter | null;
    next: Chapter | null;
    novel: Novel;
}
/**
 * 章節 API 客戶端
 */
export declare const chaptersAPI: {
    /**
     * 獲取章節內容
     */
    getContent(id: number): Promise<Chapter>;
    /**
     * 獲取章節導航資訊 (包含上一章/下一章)
     */
    getChapterNavigation(chapterId: number): Promise<ChapterNavigation>;
};
/**
 * 健康檢查 API
 */
export declare const healthAPI: {
    check(): Promise<{
        status: string;
        timestamp: string;
    }>;
};
//# sourceMappingURL=client.d.ts.map