/**
 * @novelwebsite/api - 共享 API 客戶端
 *
 * 提供統一的 API 調用接口，支援 Next.js 15 App Router 的 SSR/ISR
 */
export type { BaseModel, Category, Tag, Novel, Chapter, PaginatedResponse, QueryParams, APIError, SlugMapping, } from './types';
export type { ChapterNavigation, } from './client';
export { novelsAPI, chaptersAPI, healthAPI, APIClientError, generateSlug, extractIdFromSlug, } from './client';
import { generateSlug, extractIdFromSlug } from './client';
export declare const utils: {
    generateSlug: typeof generateSlug;
    extractIdFromSlug: typeof extractIdFromSlug;
};
declare const _default: {
    novels: {
        getList(params?: import("./types").QueryParams): Promise<import("./types").PaginatedResponse<import("./types").Novel>>;
        getDetail(id: number): Promise<import("./types").Novel>;
        getDetailBySlug(slug: string): Promise<import("./types").Novel | null>;
        getChapters(novelId: number, params?: import("./types").QueryParams): Promise<import("./types").PaginatedResponse<import("./types").Chapter>>;
        getNovelIdBySlug(slug: string): Promise<number | null>;
    };
    chapters: {
        getContent(id: number): Promise<import("./types").Chapter>;
        getChapterNavigation(chapterId: number): Promise<import("./client").ChapterNavigation>;
    };
    health: {
        check(): Promise<{
            status: string;
            timestamp: string;
        }>;
    };
    utils: {
        generateSlug: typeof generateSlug;
        extractIdFromSlug: typeof extractIdFromSlug;
    };
};
export default _default;
//# sourceMappingURL=index.d.ts.map