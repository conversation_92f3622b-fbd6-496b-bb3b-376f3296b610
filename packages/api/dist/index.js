/**
 * @novelwebsite/api - 共享 API 客戶端
 *
 * 提供統一的 API 調用接口，支援 Next.js 15 App Router 的 SSR/ISR
 */
// 導出 API 客戶端
export { novelsAPI, chaptersAPI, healthAPI, APIClientError, generateSlug, extractIdFromSlug, } from './client';
// 導出常用工具函數
import { generateSlug, extractIdFromSlug, novelsAPI, chaptersAPI, healthAPI } from './client';
export const utils = {
    generateSlug,
    extractIdFromSlug,
};
// 預設導出主要 API
export default {
    novels: novelsAPI,
    chapters: chaptersAPI,
    health: healthAPI,
    utils,
};
//# sourceMappingURL=index.js.map