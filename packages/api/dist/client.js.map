{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAWH,SAAS;AACT,MAAM,UAAU,GAAG;IACjB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,8BAA8B;IAC1E,OAAO,EAAE,KAAK;IACd,iBAAiB,EAAE,EAAE,EAAE,WAAW;IAClC,OAAO,EAAE,CAAC,EAAE,OAAO;IACnB,UAAU,EAAE,IAAI,EAAE,cAAc;CACjC,CAAC;AAEF,QAAQ;AACR,MAAM,OAAO,cAAe,SAAQ,KAAK;IACvC,YACE,OAAe,EACR,MAAc,EACd,IAAe;QAEtB,KAAK,CAAC,OAAO,CAAC,CAAC;QAHR,WAAM,GAAN,MAAM,CAAQ;QACd,SAAI,GAAJ,IAAI,CAAW;QAGtB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AAED,YAAY;AACZ,MAAM,UAAU,YAAY,CAAC,KAAa,EAAE,EAAW;IACrD,MAAM,QAAQ,GAAG,KAAK;SACnB,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC,WAAW;SACjD,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC,wBAAwB;SAC9D,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,SAAS;SAC9B,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,UAAU;SAC9B,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,cAAc;SACtC,IAAI,EAAE,CAAC;IAEV,cAAc;IACd,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;AAC7C,CAAC;AAED,eAAe;AACf,MAAM,UAAU,iBAAiB,CAAC,IAAY;IAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACpC,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC/C,CAAC;AAED,gBAAgB;AAChB,SAAS,KAAK,CAAC,EAAU;IACvB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD,CAAC;AAED,WAAW;AACX,KAAK,UAAU,SAAS,CACtB,SAA2B,EAC3B,aAAqB,UAAU,CAAC,OAAO,EACvC,YAAoB,UAAU,CAAC,UAAU;IAEzC,IAAI,SAAS,GAAiB,IAAI,CAAC;IAEnC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;QACvD,IAAI,CAAC;YACH,OAAO,MAAM,SAAS,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,GAAG,KAAc,CAAC;YAE3B,mBAAmB;YACnB,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;gBAC3B,MAAM;YACR,CAAC;YAED,yBAAyB;YACzB,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;gBACjF,MAAM;YACR,CAAC;YAED,kBAAkB;YAClB,MAAM,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAED,MAAM,SAAS,IAAI,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AACnE,CAAC;AAED,eAAe;AACf,KAAK,UAAU,QAAQ,CACrB,QAAgB,EAChB,UAII,EAAE;IAEN,MAAM,EACJ,UAAU,GAAG,UAAU,CAAC,iBAAiB,EACzC,IAAI,GAAG,EAAE,EACT,WAAW,GAAG,IAAI,EAClB,GAAG,YAAY,EAChB,GAAG,OAAO,CAAC;IAEZ,MAAM,GAAG,GAAG,GAAG,UAAU,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC;IAE/C,MAAM,cAAc,GAAG,KAAK,IAAgB,EAAE;QAC5C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;YAChC,GAAG,YAAY;YACf,gDAAgD;YAChD,IAAI,EAAE;gBACJ,UAAU;gBACV,IAAI;aACL;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,YAAY,CAAC,OAAO;aACxB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1D,MAAM,IAAI,cAAc,CACtB,uBAAuB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,EAC/D,QAAQ,CAAC,MAAM,EACf,SAAS,CACV,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC,CAAC;IAEF,IAAI,CAAC;QACH,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,MAAM,SAAS,CAAC,cAAc,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,cAAc,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,cAAc,EAAE,CAAC;YACpC,MAAM,KAAK,CAAC;QACd,CAAC;QACD,MAAM,IAAI,cAAc,CACtB,kBAAkB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAC5E,CAAC,CACF,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG;IACvB;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,SAAsB,EAAE;QACpC,MAAM,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;QAC3C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBAC1C,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAEvD,OAAO,QAAQ,CAA2B,QAAQ,EAAE;YAClD,IAAI,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC;SAChC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,EAAU;QACxB,OAAO,QAAQ,CAAQ,WAAW,EAAE,GAAG,EAAE;YACvC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,CAAC;SAChC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,IAAY;QAChC,iBAAiB;QACjB,MAAM,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC;gBACH,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC5D,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC5D,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,cAAc;QACd,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YACzE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACtC,YAAY,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAC7C,CAAC;YACF,OAAO,KAAK,IAAI,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,SAAsB,EAAE;QACzD,MAAM,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;QAC3C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBAC1C,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,WAAW,OAAO,aAAa,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAE3E,OAAO,QAAQ,CAA6B,QAAQ,EAAE;YACpD,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,OAAO,WAAW,CAAC;SAChD,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,IAAY;QACjC,iBAAiB;QACjB,MAAM,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,EAAE,EAAE,CAAC;YACP,aAAa;YACb,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBACzB,OAAO,EAAE,CAAC;YACZ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC5D,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACrC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBAChC,OAAO,SAAS,CAAC;YACnB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC5D,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,UAAU;QACV,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YACpF,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACtC,YAAY,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAC7C,CAAC;YACF,OAAO,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAC;AAYF;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,EAAU;QACzB,OAAO,QAAQ,CAAU,aAAa,EAAE,GAAG,EAAE;YAC3C,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,EAAE,CAAC;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,SAAiB;QAC1C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACjD,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAEvD,YAAY;QACZ,MAAM,gBAAgB,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE;YAClE,KAAK,EAAE,IAAI,EAAE,oBAAoB;YACjC,QAAQ,EAAE,gBAAgB;SAC3B,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,gBAAgB,CAAC,OAAO,CAAC;QAC7C,MAAM,YAAY,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC;QAE5E,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;YACjE,IAAI,EAAE,YAAY,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;YAClF,KAAK;SACN,CAAC;IACJ,CAAC;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG;IACvB,KAAK,CAAC,KAAK;QACT,OAAO,QAAQ,CAAC,UAAU,EAAE;YAC1B,UAAU,EAAE,CAAC,EAAE,UAAU;SAC1B,CAAC,CAAC;IACL,CAAC;CACF,CAAC"}