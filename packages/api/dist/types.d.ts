/**
 * 共享 API 類型定義
 * 與 Django backend 保持一致
 */
export interface BaseModel {
    id: string | number;
    created_at: string;
    updated_at: string;
}
export interface Category {
    id: number;
    name: string;
    slug: string;
}
export interface Tag {
    id: number;
    name: string;
    slug: string;
}
export interface Novel extends BaseModel {
    title: string;
    author: string;
    description: string;
    category: Category | null;
    views: number;
    favorites: number;
    status: 'ongoing' | 'completed';
    source: string;
    source_id: string;
    source_url: string;
    cover?: string;
    tags?: Tag[];
    chapters?: Chapter[];
}
export interface Chapter extends BaseModel {
    novel: number;
    title: string;
    content: string;
    chapter_number: number;
    views: number;
    is_vip: boolean;
    source_url: string;
}
export interface PaginatedResponse<T> {
    count: number;
    next: string | null;
    previous: string | null;
    results: T[];
}
export interface QueryParams {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    status?: string;
    ordering?: string;
}
export interface APIError {
    detail: string;
    code?: string;
    field_errors?: Record<string, string[]>;
}
export interface SlugMapping {
    id: number;
    slug: string;
    title: string;
}
//# sourceMappingURL=types.d.ts.map