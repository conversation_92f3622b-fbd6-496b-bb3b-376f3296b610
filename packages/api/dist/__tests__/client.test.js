/**
 * API 客戶端測試
 */
import { generateSlug, extractIdFromSlug } from '../client';
describe('API Client Utils', () => {
    describe('generateSlug', () => {
        it('should generate slug from Chinese title', () => {
            expect(generateSlug('瘟仙', 1)).toBe('瘟仙-1');
        });
        it('should handle special characters', () => {
            expect(generateSlug('修真：從撿垃圾開始！', 123)).toBe('修真從撿垃圾開始-123');
        });
        it('should handle spaces and punctuation', () => {
            expect(generateSlug('我的 修仙 人生...', 456)).toBe('我的-修仙-人生-456');
        });
        it('should handle empty title', () => {
            expect(generateSlug('', 1)).toBe('-1');
        });
        it('should work without ID', () => {
            expect(generateSlug('測試小說')).toBe('測試小說');
        });
    });
    describe('extractIdFromSlug', () => {
        it('should extract ID from valid slug', () => {
            expect(extractIdFromSlug('瘟仙-1')).toBe(1);
            expect(extractIdFromSlug('修真從撿垃圾開始-123')).toBe(123);
        });
        it('should return null for invalid slug', () => {
            expect(extractIdFromSlug('瘟仙')).toBe(null);
            expect(extractIdFromSlug('invalid-slug')).toBe(null);
            expect(extractIdFromSlug('')).toBe(null);
        });
        it('should handle edge cases', () => {
            expect(extractIdFromSlug('title-with-numbers-123-456')).toBe(456);
            expect(extractIdFromSlug('title-0')).toBe(0);
        });
    });
});
//# sourceMappingURL=client.test.js.map