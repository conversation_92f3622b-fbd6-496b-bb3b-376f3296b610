/**
 * 統一 API 客戶端
 * 支援 Next.js 15 App Router 的 SSR/ISR
 */
// API 配置
const API_CONFIG = {
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
    timeout: 30000,
    defaultRevalidate: 60, // 60 秒 ISR
    retries: 3, // 重試次數
    retryDelay: 1000, // 基礎重試延遲 (毫秒)
};
// 錯誤處理類
export class APIClientError extends Error {
    constructor(message, status, data) {
        super(message);
        this.status = status;
        this.data = data;
        this.name = 'APIClientError';
    }
}
// Slug 生成工具
export function generateSlug(title, id) {
    const baseSlug = title
        .replace(/[：！？。，、；""''（）【】《》〈〉]/g, '') // 移除中文標點符號
        .replace(/[^\u4e00-\u9fa5\w\s-]/g, '') // 保留中文字符、英文字母、數字、空格、連字符
        .replace(/\s+/g, '-') // 空格轉連字符
        .replace(/-+/g, '-') // 多個連字符合併
        .replace(/^-+|-+$/g, '') // 移除開頭和結尾的連字符
        .trim();
    // 添加 ID 確保唯一性
    return id ? `${baseSlug}-${id}` : baseSlug;
}
// 從 slug 提取 ID
export function extractIdFromSlug(slug) {
    const match = slug.match(/-(\d+)$/);
    return match ? parseInt(match[1], 10) : null;
}
// 延遲函數 (用於重試機制)
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
// 指數退避重試機制
async function withRetry(operation, maxRetries = API_CONFIG.retries, baseDelay = API_CONFIG.retryDelay) {
    let lastError = null;
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await operation();
        }
        catch (error) {
            lastError = error;
            // 如果是最後一次嘗試，直接拋出錯誤
            if (attempt === maxRetries) {
                break;
            }
            // 如果是 4xx 錯誤 (客戶端錯誤)，不重試
            if (error instanceof APIClientError && error.status >= 400 && error.status < 500) {
                break;
            }
            // 指數退避：每次重試延遲時間翻倍
            const delayTime = baseDelay * Math.pow(2, attempt);
            await delay(delayTime);
        }
    }
    throw lastError || new Error('Unknown error in retry mechanism');
}
// 基礎 fetch 包裝器
async function apiFetch(endpoint, options = {}) {
    const { revalidate = API_CONFIG.defaultRevalidate, tags = [], enableRetry = true, ...fetchOptions } = options;
    const url = `${API_CONFIG.baseURL}${endpoint}`;
    const fetchOperation = async () => {
        const response = await fetch(url, {
            ...fetchOptions,
            // @ts-ignore - Next.js specific fetch extension
            next: {
                revalidate,
                tags,
            },
            headers: {
                'Content-Type': 'application/json',
                ...fetchOptions.headers,
            },
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new APIClientError(`API request failed: ${response.status} ${response.statusText}`, response.status, errorData);
        }
        return await response.json();
    };
    try {
        if (enableRetry) {
            return await withRetry(fetchOperation);
        }
        else {
            return await fetchOperation();
        }
    }
    catch (error) {
        if (error instanceof APIClientError) {
            throw error;
        }
        throw new APIClientError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`, 0);
    }
}
/**
 * 小說 API 客戶端
 */
export const novelsAPI = {
    /**
     * 獲取小說列表
     */
    async getList(params = {}) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                searchParams.append(key, String(value));
            }
        });
        const query = searchParams.toString();
        const endpoint = `/novels/${query ? `?${query}` : ''}`;
        return apiFetch(endpoint, {
            tags: ['novels', 'novels-list'],
        });
    },
    /**
     * 獲取小說詳情 (by ID)
     */
    async getDetail(id) {
        return apiFetch(`/novels/${id}/`, {
            tags: ['novels', `novel-${id}`],
        });
    },
    /**
     * 通過 slug 獲取小說詳情
     */
    async getDetailBySlug(slug) {
        // 嘗試從 slug 提取 ID
        const id = extractIdFromSlug(slug);
        if (id) {
            try {
                return await this.getDetail(id);
            }
            catch (error) {
                if (error instanceof APIClientError && error.status === 404) {
                    return null;
                }
                throw error;
            }
        }
        // 如果 slug 是純數字，直接嘗試作為 ID
        if (/^\d+$/.test(slug)) {
            try {
                return await this.getDetail(parseInt(slug, 10));
            }
            catch (error) {
                if (error instanceof APIClientError && error.status === 404) {
                    return null;
                }
                throw error;
            }
        }
        // 否則需要搜索匹配的小說
        try {
            const response = await this.getList({ search: slug.replace(/-/g, ' ') });
            const novel = response.results.find(n => generateSlug(n.title, Number(n.id)) === slug);
            return novel || null;
        }
        catch (error) {
            console.error('Error searching novel by slug:', error);
            return null;
        }
    },
    /**
     * 獲取小說的章節列表
     */
    async getChapters(novelId, params = {}) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                searchParams.append(key, String(value));
            }
        });
        const query = searchParams.toString();
        const endpoint = `/novels/${novelId}/chapters/${query ? `?${query}` : ''}`;
        return apiFetch(endpoint, {
            tags: ['chapters', `novel-${novelId}-chapters`],
        });
    },
    /**
     * 通過 slug 獲取小說 ID (映射函數)
     */
    async getNovelIdBySlug(slug) {
        // 嘗試從 slug 提取 ID
        const id = extractIdFromSlug(slug);
        if (id) {
            // 驗證 ID 是否存在
            try {
                await this.getDetail(id);
                return id;
            }
            catch (error) {
                if (error instanceof APIClientError && error.status === 404) {
                    return null;
                }
                throw error;
            }
        }
        // 如果 slug 是純數字，直接嘗試作為 ID
        if (/^\d+$/.test(slug)) {
            const numericId = parseInt(slug, 10);
            try {
                await this.getDetail(numericId);
                return numericId;
            }
            catch (error) {
                if (error instanceof APIClientError && error.status === 404) {
                    return null;
                }
                throw error;
            }
        }
        // 搜索匹配的小說
        try {
            const response = await this.getList({ search: slug.replace(/-/g, ' '), limit: 50 });
            const novel = response.results.find(n => generateSlug(n.title, Number(n.id)) === slug);
            return novel ? Number(novel.id) : null;
        }
        catch (error) {
            console.error('Error searching novel by slug:', error);
            return null;
        }
    },
};
/**
 * 章節 API 客戶端
 */
export const chaptersAPI = {
    /**
     * 獲取章節內容
     */
    async getContent(id) {
        return apiFetch(`/chapters/${id}/`, {
            tags: ['chapters', `chapter-${id}`],
        });
    },
    /**
     * 獲取章節導航資訊 (包含上一章/下一章)
     */
    async getChapterNavigation(chapterId) {
        const chapter = await this.getContent(chapterId);
        const novel = await novelsAPI.getDetail(chapter.novel);
        // 獲取小說的所有章節
        const chaptersResponse = await novelsAPI.getChapters(chapter.novel, {
            limit: 1000, // 假設單本小說不會超過 1000 章
            ordering: 'chapter_number'
        });
        const allChapters = chaptersResponse.results;
        const currentIndex = allChapters.findIndex(c => Number(c.id) === chapterId);
        return {
            current: chapter,
            previous: currentIndex > 0 ? allChapters[currentIndex - 1] : null,
            next: currentIndex < allChapters.length - 1 ? allChapters[currentIndex + 1] : null,
            novel,
        };
    },
};
/**
 * 健康檢查 API
 */
export const healthAPI = {
    async check() {
        return apiFetch('/health/', {
            revalidate: 0, // 不快取健康檢查
        });
    },
};
//# sourceMappingURL=client.js.map