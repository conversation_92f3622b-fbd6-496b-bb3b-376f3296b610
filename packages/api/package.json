{"name": "@novelwebsite/api", "version": "0.1.0", "private": true, "description": "Shared API client for NovelWebsite monorepo", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "keywords": ["api", "client", "monorepo", "shared"], "dependencies": {}, "devDependencies": {"typescript": "^5.0.0"}, "peerDependencies": {"typescript": ">=4.9.0"}}