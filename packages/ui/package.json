{"name": "@novelwebsite/ui", "version": "0.1.0", "private": true, "description": "Shared UI components for NovelWebsite monorepo", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["ui", "components", "react", "tailwind", "monorepo", "shared"], "dependencies": {"clsx": "^2.1.0"}, "devDependencies": {"typescript": "^5.0.0", "jest": "^29.7.0", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "ts-jest": "^29.1.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "typescript": ">=4.9.0"}}