import React from 'react'
import clsx from 'clsx'
import type { NovelCardProps } from '../types'

/**
 * NovelCard 組件
 * 
 * 顯示小說卡片，包含封面、標題、作者、狀態等資訊
 * 支援自定義點擊行為和路由
 */
const NovelCard: React.FC<NovelCardProps> = ({
  novel,
  href,
  onClick,
  className,
  children,
}) => {
  // 狀態樣式配置
  const statusClasses = {
    completed: 'bg-green-100 text-green-800',
    ongoing: 'bg-blue-100 text-blue-800',
  }

  // 基礎卡片內容
  const cardContent = (
    <div className="p-4">
      <div className="flex gap-4">
        {/* 小說封面 */}
        <img
          src={novel.cover || '/default-cover.jpg'}
          alt={novel.title}
          className="w-16 h-20 object-cover rounded flex-shrink-0"
          loading="lazy"
        />
        
        {/* 小說資訊 */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 truncate mb-1">
            {novel.title}
          </h3>
          
          <p className="text-sm text-gray-600 mb-2">
            作者：{novel.author}
          </p>
          
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span className={clsx(
              'px-2 py-1 rounded',
              statusClasses[novel.status]
            )}>
              {novel.status === 'completed' ? '已完結' : '連載中'}
            </span>
            
            <span>👁️ {novel.views.toLocaleString()}</span>
            
            {novel.favorites > 0 && (
              <span>❤️ {novel.favorites.toLocaleString()}</span>
            )}
          </div>
          
          {/* 分類標籤 */}
          {novel.category && (
            <div className="mt-1">
              <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                {novel.category.name}
              </span>
            </div>
          )}
        </div>
      </div>
      
      {/* 小說簡介 */}
      {novel.description && (
        <p className="text-sm text-gray-600 mt-3 line-clamp-2">
          {novel.description}
        </p>
      )}
      
      {/* 自定義內容 */}
      {children}
    </div>
  )

  // 基礎樣式
  const baseClasses = clsx(
    'bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow',
    className
  )

  // 如果有 href，渲染為連結
  if (href) {
    return (
      <a
        href={href}
        className={clsx(baseClasses, 'block')}
        onClick={onClick}
      >
        {cardContent}
      </a>
    )
  }

  // 如果有 onClick，渲染為按鈕
  if (onClick) {
    return (
      <button
        type="button"
        className={clsx(baseClasses, 'block w-full text-left')}
        onClick={onClick}
      >
        {cardContent}
      </button>
    )
  }

  // 否則渲染為普通 div
  return (
    <div className={baseClasses}>
      {cardContent}
    </div>
  )
}

// 設定 displayName 方便 DevTools 調試
NovelCard.displayName = 'NovelCard'

export default NovelCard
