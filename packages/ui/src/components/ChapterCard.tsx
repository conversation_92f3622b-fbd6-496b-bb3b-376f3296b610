import React from 'react'
import clsx from 'clsx'
import type { ChapterCardProps } from '../types'

/**
 * ChapterCard 組件
 * 
 * 顯示章節卡片，包含章節號、標題、更新時間、VIP 狀態等資訊
 * 支援自定義點擊行為和路由
 */
const ChapterCard: React.FC<ChapterCardProps> = ({
  chapter,
  href,
  onClick,
  className,
  children,
}) => {
  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      })
    } catch {
      return dateString
    }
  }

  // 基礎卡片內容
  const cardContent = (
    <div className="p-3">
      <div className="flex justify-between items-start">
        {/* 章節資訊 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-gray-900">
              第 {chapter.chapter_number} 章
            </span>
            
            {chapter.is_vip && (
              <span className="inline-flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">
                👑 VIP
              </span>
            )}
          </div>
          
          <h4 className="text-gray-900 font-medium truncate mb-2">
            {chapter.title}
          </h4>
          
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span>👁️ {chapter.views.toLocaleString()}</span>
            <span>{formatDate(chapter.updated_at)}</span>
          </div>
        </div>

        {/* 右側圖示 */}
        <div className="flex-shrink-0 ml-3">
          <svg
            className="w-5 h-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
      </div>
      
      {/* 自定義內容 */}
      {children}
    </div>
  )

  // 基礎樣式
  const baseClasses = clsx(
    'block border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors',
    chapter.is_vip && 'border-yellow-200 bg-yellow-50/30',
    className
  )

  // 如果有 href，渲染為連結
  if (href) {
    return (
      <a
        href={href}
        className={baseClasses}
        onClick={onClick}
      >
        {cardContent}
      </a>
    )
  }

  // 如果有 onClick，渲染為按鈕
  if (onClick) {
    return (
      <button
        type="button"
        className={clsx(baseClasses, 'w-full text-left')}
        onClick={onClick}
      >
        {cardContent}
      </button>
    )
  }

  // 否則渲染為普通 div
  return (
    <div className={baseClasses}>
      {cardContent}
    </div>
  )
}

// 設定 displayName 方便 DevTools 調試
ChapterCard.displayName = 'ChapterCard'

export default ChapterCard
