import React from 'react'
import clsx from 'clsx'
import type { LayoutProps } from '../types'

/**
 * Layout 組件
 * 
 * 提供一致的頁面布局容器，支援響應式設計和多種配置選項
 * 適用於 SSR 和 CSR 環境
 */
const Layout: React.FC<LayoutProps> = ({
  children,
  header,
  footer,
  sidebar,
  maxWidth = 'lg',
  className,
}) => {
  // 最大寬度配置
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md', 
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full',
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頭部 */}
      {header && (
        <header className="sticky top-0 z-50 bg-white shadow-sm border-b">
          {header}
        </header>
      )}

      {/* 主要內容區域 */}
      <div className="flex flex-1">
        {/* 側邊欄 */}
        {sidebar && (
          <aside className="hidden lg:block w-64 bg-white border-r">
            <div className="sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto">
              {sidebar}
            </div>
          </aside>
        )}

        {/* 主內容 */}
        <main className="flex-1">
          <div className={clsx(
            'w-full mx-auto p-4 sm:p-6 lg:p-8',
            maxWidthClasses[maxWidth],
            className
          )}>
            {children}
          </div>
        </main>
      </div>

      {/* 頁腳 */}
      {footer && (
        <footer className="bg-white border-t">
          {footer}
        </footer>
      )}
    </div>
  )
}

// 設定 displayName 方便 DevTools 調試
Layout.displayName = 'Layout'

export default Layout
