'use client'

import React, { useState } from 'react'
import clsx from 'clsx'
import type { HeaderProps } from '../types'

/**
 * Header 組件
 * 
 * 提供網站頭部導航，支援響應式設計和多種變體
 * 注意：此組件使用 'use client' 因為包含交互狀態
 */
const Header: React.FC<HeaderProps> = ({
  title = '小說閱讀平台',
  logo,
  navigation = [],
  actions,
  variant = 'default',
  className,
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // 變體樣式配置
  const variantClasses = {
    default: 'bg-white border-b border-gray-200',
    transparent: 'bg-transparent',
    solid: 'bg-blue-600 text-white',
  }

  // 文字顏色配置
  const textColorClasses = {
    default: 'text-gray-900',
    transparent: 'text-gray-900',
    solid: 'text-white',
  }

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <header className={clsx(
      'sticky top-0 z-50',
      variantClasses[variant],
      className
    )}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo 和標題 */}
          <div className="flex items-center">
            {logo && (
              <div className="flex-shrink-0 mr-3">
                {logo}
              </div>
            )}
            <div className={clsx(
              'text-xl font-bold',
              textColorClasses[variant]
            )}>
              {title}
              <span className="sr-only">導航列</span>
            </div>
          </div>

          {/* 桌面導航 */}
          <nav className="hidden md:flex space-x-8">
            {navigation.map((item) => (
              <a
                key={item.href}
                href={item.href}
                className={clsx(
                  'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  item.active
                    ? variant === 'solid' 
                      ? 'bg-blue-700 text-white' 
                      : 'bg-blue-100 text-blue-700'
                    : variant === 'solid'
                      ? 'text-blue-100 hover:text-white hover:bg-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100',
                )}
                {...(item.external && { 
                  target: '_blank', 
                  rel: 'noopener noreferrer' 
                })}
              >
                {item.label}
              </a>
            ))}
          </nav>

          {/* 右側操作區域 */}
          <div className="flex items-center space-x-4">
            {actions && (
              <div className="hidden md:flex items-center space-x-2">
                {actions}
              </div>
            )}

            {/* 移動端菜單按鈕 */}
            <button
              type="button"
              className={clsx(
                'md:hidden p-2 rounded-md',
                variant === 'solid'
                  ? 'text-blue-100 hover:text-white hover:bg-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              )}
              onClick={toggleMobileMenu}
              aria-expanded={isMobileMenuOpen}
              aria-label="開啟主選單"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isMobileMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* 移動端菜單 */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 border-t border-gray-200">
              {navigation.map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  className={clsx(
                    'block px-3 py-2 rounded-md text-base font-medium transition-colors',
                    item.active
                      ? variant === 'solid'
                        ? 'bg-blue-700 text-white'
                        : 'bg-blue-100 text-blue-700'
                      : variant === 'solid'
                        ? 'text-blue-100 hover:text-white hover:bg-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                  {...(item.external && { 
                    target: '_blank', 
                    rel: 'noopener noreferrer' 
                  })}
                >
                  {item.label}
                </a>
              ))}
              
              {actions && (
                <div className="pt-4 border-t border-gray-200">
                  {actions}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

// 設定 displayName 方便 DevTools 調試
Header.displayName = 'Header'

export default Header
