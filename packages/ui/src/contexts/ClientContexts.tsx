'use client'

import React from 'react'
import { AuthProvider } from './AuthContext'
import { SettingsProvider } from './SettingsContext'

/**
 * ClientContexts - 客戶端 Context 提供者
 * 
 * 將所有需要客戶端狀態的 Context 組合在一起
 * 確保 SSR 安全，避免 hydration mismatch
 */

interface ClientContextsProps {
  children: React.ReactNode
}

export const ClientContexts: React.FC<ClientContextsProps> = ({ children }) => {
  return (
    <AuthProvider>
      <SettingsProvider>
        {children}
      </SettingsProvider>
    </AuthProvider>
  )
}

// 設定 displayName 方便 DevTools 調試
ClientContexts.displayName = 'ClientContexts'

// 重新導出 Context 和 Hooks
export { AuthProvider, useAuth } from './AuthContext'
export { SettingsProvider, useSettings } from './SettingsContext'
