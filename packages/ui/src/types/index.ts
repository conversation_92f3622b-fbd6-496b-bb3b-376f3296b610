/**
 * 共享 UI 組件類型定義
 */

import { ReactNode } from 'react'

// 基礎組件 Props
export interface BaseComponentProps {
  className?: string
  children?: ReactNode
}

// 載入狀態類型
export interface LoadingProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg'
  color?: 'primary' | 'secondary' | 'white'
  text?: string
}

// 小說卡片 Props
export interface NovelCardProps extends BaseComponentProps {
  novel: {
    id: string | number
    title: string
    author: string
    description?: string
    cover?: string
    status: 'ongoing' | 'completed'
    views: number
    favorites: number
    category?: {
      id: number
      name: string
    } | null
  }
  href?: string
  onClick?: () => void
}

// 章節卡片 Props
export interface ChapterCardProps extends BaseComponentProps {
  chapter: {
    id: string | number
    title: string
    chapter_number: number
    updated_at: string
    views: number
    is_vip: boolean
  }
  href?: string
  onClick?: () => void
}

// 頁面布局 Props
export interface LayoutProps extends BaseComponentProps {
  header?: ReactNode
  footer?: ReactNode
  sidebar?: ReactNode
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
}

// 頭部導航 Props
export interface HeaderProps extends BaseComponentProps {
  title?: string
  logo?: ReactNode
  navigation?: NavigationItem[]
  actions?: ReactNode
  variant?: 'default' | 'transparent' | 'solid'
}

export interface NavigationItem {
  label: string
  href: string
  active?: boolean
  external?: boolean
}

// 錯誤邊界 Props
export interface ErrorBoundaryProps extends BaseComponentProps {
  error: Error & { digest?: string }
  onRetry?: () => void
  title?: string
  description?: string
  showDetails?: boolean
}

// 404 頁面 Props
export interface NotFoundProps extends BaseComponentProps {
  title?: string
  description?: string
  suggestions?: string[]
  actions?: ReactNode
}

// Context 相關類型
export interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  loading: boolean
}

export interface User {
  id: string
  username: string
  email: string
  avatar?: string
}

export interface LoginCredentials {
  username: string
  password: string
}

// 設定 Context 類型
export interface SettingsContextType {
  theme: 'light' | 'dark' | 'sepia' | 'night'
  fontSize: 'sm' | 'md' | 'lg' | 'xl'
  fontFamily: 'sans' | 'serif' | 'mono'
  lineHeight: 'tight' | 'normal' | 'relaxed'
  updateSettings: (settings: Partial<SettingsContextType>) => void
  resetSettings: () => void
}

// 工具類型
export type Variant = 'primary' | 'secondary' | 'success' | 'warning' | 'error'
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
export type Color = 'gray' | 'blue' | 'green' | 'yellow' | 'red' | 'purple'
