{"version": 3, "file": "SettingsContext.js", "sourceRoot": "", "sources": ["../../src/contexts/SettingsContext.tsx"], "names": [], "mappings": "AAAA,YAAY,CAAA;;AAEZ,OAAc,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAA;AAG1F;;;;;GAKG;AAEH,OAAO;AACP,MAAM,gBAAgB,GAAkE;IACtF,KAAK,EAAE,OAAO;IACd,QAAQ,EAAE,IAAI;IACd,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,QAAQ;CACrB,CAAA;AAED,MAAM,eAAe,GAAG,aAAa,CAA6B,IAAI,CAAC,CAAA;AAEvE,WAAW;AACX,MAAM,CAAC,MAAM,WAAW,GAAG,GAAG,EAAE;IAC9B,MAAM,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,CAAA;IAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAA;IACvE,CAAC;IACD,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA;AAMD,MAAM,CAAC,MAAM,gBAAgB,GAAoC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;IAChF,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAA;IAC1D,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;IAE/C,8BAA8B;IAC9B,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,uBAAuB,GAAG,GAAG,EAAE;YACnC,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAA;gBACnE,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;oBAChD,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,cAAc,EAAE,CAAC,CAAC,CAAA;gBACvD,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAA;gBAClE,YAAY,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAA;YAClD,CAAC;oBAAS,CAAC;gBACT,WAAW,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;QACH,CAAC,CAAA;QAED,uBAAuB,EAAE,CAAA;IAC3B,CAAC,EAAE,EAAE,CAAC,CAAA;IAEN,qBAAqB;IACrB,MAAM,qBAAqB,GAAG,WAAW,CAAC,CAAC,WAA4B,EAAE,EAAE;QACzE,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAA;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAA;QAClE,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAA;IAEN,SAAS;IACT,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,OAAiC,EAAE,EAAE;QACvE,WAAW,CAAC,IAAI,CAAC,EAAE;YACjB,MAAM,WAAW,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,EAAE,CAAA;YAE3C,gBAAgB;YAChB,IAAI,QAAQ,EAAE,CAAC;gBACb,qBAAqB,CAAC,WAAW,CAAC,CAAA;YACpC,CAAC;YAED,OAAO,WAAW,CAAA;QACpB,CAAC,CAAC,CAAA;IACJ,CAAC,EAAE,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC,CAAA;IAErC,SAAS;IACT,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;QACrC,WAAW,CAAC,gBAAgB,CAAC,CAAA;QAE7B,gBAAgB;QAChB,IAAI,QAAQ,EAAE,CAAC;YACb,qBAAqB,CAAC,gBAAgB,CAAC,CAAA;QACzC,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC,CAAA;IAErC,MAAM,KAAK,GAAwB;QACjC,GAAG,QAAQ;QACX,cAAc;QACd,aAAa;KACd,CAAA;IAED,OAAO,CACL,KAAC,eAAe,CAAC,QAAQ,IAAC,KAAK,EAAE,KAAK,YACnC,QAAQ,GACgB,CAC5B,CAAA;AACH,CAAC,CAAA;AAED,gCAAgC;AAChC,gBAAgB,CAAC,WAAW,GAAG,kBAAkB,CAAA"}