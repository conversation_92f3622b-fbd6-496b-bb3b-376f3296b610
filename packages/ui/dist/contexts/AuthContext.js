'use client';
import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useState, useCallback, useEffect } from 'react';
/**
 * AuthContext - 認證狀態管理
 *
 * 注意：此 Context 使用 'use client' 因為需要訪問 localStorage
 * 在 SSR 環境中安全使用
 */
const AuthContext = createContext(null);
// 自定義 Hook
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    // 從 localStorage 載入用戶資料 (僅在客戶端)
    useEffect(() => {
        const loadUserFromStorage = () => {
            try {
                const savedUser = localStorage.getItem('novelwebsite_user');
                if (savedUser) {
                    const userData = JSON.parse(savedUser);
                    setUser(userData);
                }
            }
            catch (error) {
                console.error('Failed to load user from localStorage:', error);
                localStorage.removeItem('novelwebsite_user');
            }
            finally {
                setLoading(false);
            }
        };
        loadUserFromStorage();
    }, []);
    // 登入函數
    const login = useCallback(async (credentials) => {
        setLoading(true);
        try {
            // 這裡應該調用實際的 API
            // 暫時使用模擬登入
            const mockUser = {
                id: '1',
                username: credentials.username,
                email: `${credentials.username}@example.com`,
                avatar: undefined,
            };
            setUser(mockUser);
            localStorage.setItem('novelwebsite_user', JSON.stringify(mockUser));
        }
        catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
        finally {
            setLoading(false);
        }
    }, []);
    // 登出函數
    const logout = useCallback(() => {
        setUser(null);
        localStorage.removeItem('novelwebsite_user');
    }, []);
    // 計算認證狀態
    const isAuthenticated = user !== null;
    const value = {
        user,
        isAuthenticated,
        login,
        logout,
        loading,
    };
    return (_jsx(AuthContext.Provider, { value: value, children: children }));
};
// 設定 displayName 方便 DevTools 調試
AuthProvider.displayName = 'AuthProvider';
//# sourceMappingURL=AuthContext.js.map