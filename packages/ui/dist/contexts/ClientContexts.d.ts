import React from 'react';
/**
 * ClientContexts - 客戶端 Context 提供者
 *
 * 將所有需要客戶端狀態的 Context 組合在一起
 * 確保 SSR 安全，避免 hydration mismatch
 */
interface ClientContextsProps {
    children: React.ReactNode;
}
export declare const ClientContexts: React.FC<ClientContextsProps>;
export { AuthProvider, useAuth } from './AuthContext';
export { SettingsProvider, useSettings } from './SettingsContext';
//# sourceMappingURL=ClientContexts.d.ts.map