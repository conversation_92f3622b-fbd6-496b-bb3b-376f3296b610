'use client';
import { jsx as _jsx } from "react/jsx-runtime";
import { AuthProvider } from './AuthContext';
import { SettingsProvider } from './SettingsContext';
export const ClientContexts = ({ children }) => {
    return (_jsx(AuthProvider, { children: _jsx(SettingsProvider, { children: children }) }));
};
// 設定 displayName 方便 DevTools 調試
ClientContexts.displayName = 'ClientContexts';
// 重新導出 Context 和 Hooks
export { AuthProvider, useAuth } from './AuthContext';
export { SettingsProvider, useSettings } from './SettingsContext';
//# sourceMappingURL=ClientContexts.js.map