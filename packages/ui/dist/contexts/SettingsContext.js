'use client';
import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useState, useCallback, useEffect } from 'react';
/**
 * SettingsContext - 閱讀器設定管理
 *
 * 注意：此 Context 使用 'use client' 因為需要訪問 localStorage
 * 在 SSR 環境中安全使用，避免 hydration mismatch
 */
// 預設設定
const DEFAULT_SETTINGS = {
    theme: 'light',
    fontSize: 'md',
    fontFamily: 'sans',
    lineHeight: 'normal',
};
const SettingsContext = createContext(null);
// 自定義 Hook
export const useSettings = () => {
    const context = useContext(SettingsContext);
    if (!context) {
        throw new Error('useSettings must be used within a SettingsProvider');
    }
    return context;
};
export const SettingsProvider = ({ children }) => {
    const [settings, setSettings] = useState(DEFAULT_SETTINGS);
    const [isLoaded, setIsLoaded] = useState(false);
    // 從 localStorage 載入設定 (僅在客戶端)
    useEffect(() => {
        const loadSettingsFromStorage = () => {
            try {
                const savedSettings = localStorage.getItem('novelwebsite_settings');
                if (savedSettings) {
                    const parsedSettings = JSON.parse(savedSettings);
                    setSettings(prev => ({ ...prev, ...parsedSettings }));
                }
            }
            catch (error) {
                console.error('Failed to load settings from localStorage:', error);
                localStorage.removeItem('novelwebsite_settings');
            }
            finally {
                setIsLoaded(true);
            }
        };
        loadSettingsFromStorage();
    }, []);
    // 保存設定到 localStorage
    const saveSettingsToStorage = useCallback((newSettings) => {
        try {
            localStorage.setItem('novelwebsite_settings', JSON.stringify(newSettings));
        }
        catch (error) {
            console.error('Failed to save settings to localStorage:', error);
        }
    }, []);
    // 更新設定函數
    const updateSettings = useCallback((updates) => {
        setSettings(prev => {
            const newSettings = { ...prev, ...updates };
            // 只在客戶端且已載入後才保存
            if (isLoaded) {
                saveSettingsToStorage(newSettings);
            }
            return newSettings;
        });
    }, [isLoaded, saveSettingsToStorage]);
    // 重置設定函數
    const resetSettings = useCallback(() => {
        setSettings(DEFAULT_SETTINGS);
        // 只在客戶端且已載入後才保存
        if (isLoaded) {
            saveSettingsToStorage(DEFAULT_SETTINGS);
        }
    }, [isLoaded, saveSettingsToStorage]);
    const value = {
        ...settings,
        updateSettings,
        resetSettings,
    };
    return (_jsx(SettingsContext.Provider, { value: value, children: children }));
};
// 設定 displayName 方便 DevTools 調試
SettingsProvider.displayName = 'SettingsProvider';
//# sourceMappingURL=SettingsContext.js.map