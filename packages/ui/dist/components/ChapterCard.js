import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import clsx from 'clsx';
/**
 * ChapterCard 組件
 *
 * 顯示章節卡片，包含章節號、標題、更新時間、VIP 狀態等資訊
 * 支援自定義點擊行為和路由
 */
const ChapterCard = ({ chapter, href, onClick, className, children, }) => {
    // 格式化日期
    const formatDate = (dateString) => {
        try {
            return new Date(dateString).toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
            });
        }
        catch {
            return dateString;
        }
    };
    // 基礎卡片內容
    const cardContent = (_jsxs("div", { className: "p-3", children: [_jsxs("div", { className: "flex justify-between items-start", children: [_jsxs("div", { className: "flex-1 min-w-0", children: [_jsxs("div", { className: "flex items-center gap-2 mb-1", children: [_jsxs("span", { className: "font-medium text-gray-900", children: ["\u7B2C ", chapter.chapter_number, " \u7AE0"] }), chapter.is_vip && (_jsx("span", { className: "inline-flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded", children: "\uD83D\uDC51 VIP" }))] }), _jsx("h4", { className: "text-gray-900 font-medium truncate mb-2", children: chapter.title }), _jsxs("div", { className: "flex items-center gap-4 text-sm text-gray-500", children: [_jsxs("span", { children: ["\uD83D\uDC41\uFE0F ", chapter.views.toLocaleString()] }), _jsx("span", { children: formatDate(chapter.updated_at) })] })] }), _jsx("div", { className: "flex-shrink-0 ml-3", children: _jsx("svg", { className: "w-5 h-5 text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M9 5l7 7-7 7" }) }) })] }), children] }));
    // 基礎樣式
    const baseClasses = clsx('block border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors', chapter.is_vip && 'border-yellow-200 bg-yellow-50/30', className);
    // 如果有 href，渲染為連結
    if (href) {
        return (_jsx("a", { href: href, className: baseClasses, onClick: onClick, children: cardContent }));
    }
    // 如果有 onClick，渲染為按鈕
    if (onClick) {
        return (_jsx("button", { type: "button", className: clsx(baseClasses, 'w-full text-left'), onClick: onClick, children: cardContent }));
    }
    // 否則渲染為普通 div
    return (_jsx("div", { className: baseClasses, children: cardContent }));
};
// 設定 displayName 方便 DevTools 調試
ChapterCard.displayName = 'ChapterCard';
export default ChapterCard;
//# sourceMappingURL=ChapterCard.js.map