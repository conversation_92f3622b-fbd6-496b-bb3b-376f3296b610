import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import clsx from 'clsx';
/**
 * LoadingSpinner 組件
 *
 * 提供一致的載入狀態指示器，支援多種尺寸和顏色主題
 */
const LoadingSpinner = ({ size = 'md', color = 'primary', text = '載入中...', className, children, }) => {
    // 尺寸配置
    const sizeClasses = {
        sm: 'h-4 w-4',
        md: 'h-8 w-8',
        lg: 'h-12 w-12',
    };
    // 顏色配置
    const colorClasses = {
        primary: 'border-blue-500',
        secondary: 'border-gray-500',
        white: 'border-white',
    };
    // 文字尺寸配置
    const textSizeClasses = {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
    };
    // 文字顏色配置
    const textColorClasses = {
        primary: 'text-gray-600',
        secondary: 'text-gray-500',
        white: 'text-white',
    };
    return (_jsxs("div", { className: clsx('flex items-center justify-center', className), role: "status", "aria-label": text, "aria-live": "polite", children: [_jsx("div", { className: clsx('animate-spin rounded-full border-b-2 border-t-2', sizeClasses[size], colorClasses[color]), "aria-hidden": "true" }), (text || children) && (_jsx("div", { className: clsx('ml-3', textSizeClasses[size], textColorClasses[color]), children: children || text }))] }));
};
// 設定 displayName 方便 DevTools 調試
LoadingSpinner.displayName = 'LoadingSpinner';
export default LoadingSpinner;
//# sourceMappingURL=LoadingSpinner.js.map