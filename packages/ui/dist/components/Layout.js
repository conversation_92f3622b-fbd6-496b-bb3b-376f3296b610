import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import clsx from 'clsx';
/**
 * Layout 組件
 *
 * 提供一致的頁面布局容器，支援響應式設計和多種配置選項
 * 適用於 SSR 和 CSR 環境
 */
const Layout = ({ children, header, footer, sidebar, maxWidth = 'lg', className, }) => {
    // 最大寬度配置
    const maxWidthClasses = {
        sm: 'max-w-sm',
        md: 'max-w-md',
        lg: 'max-w-4xl',
        xl: 'max-w-6xl',
        '2xl': 'max-w-7xl',
        full: 'max-w-full',
    };
    return (_jsxs("div", { className: "min-h-screen bg-gray-50", children: [header && (_jsx("header", { className: "sticky top-0 z-50 bg-white shadow-sm border-b", children: header })), _jsxs("div", { className: "flex flex-1", children: [sidebar && (_jsx("aside", { className: "hidden lg:block w-64 bg-white border-r", children: _jsx("div", { className: "sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto", children: sidebar }) })), _jsx("main", { className: "flex-1", children: _jsx("div", { className: clsx('w-full mx-auto p-4 sm:p-6 lg:p-8', maxWidthClasses[maxWidth], className), children: children }) })] }), footer && (_jsx("footer", { className: "bg-white border-t", children: footer }))] }));
};
// 設定 displayName 方便 DevTools 調試
Layout.displayName = 'Layout';
export default Layout;
//# sourceMappingURL=Layout.js.map