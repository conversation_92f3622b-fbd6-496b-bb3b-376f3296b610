'use client';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import clsx from 'clsx';
/**
 * Header 組件
 *
 * 提供網站頭部導航，支援響應式設計和多種變體
 * 注意：此組件使用 'use client' 因為包含交互狀態
 */
const Header = ({ title = '小說閱讀平台', logo, navigation = [], actions, variant = 'default', className, }) => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    // 變體樣式配置
    const variantClasses = {
        default: 'bg-white border-b border-gray-200',
        transparent: 'bg-transparent',
        solid: 'bg-blue-600 text-white',
    };
    // 文字顏色配置
    const textColorClasses = {
        default: 'text-gray-900',
        transparent: 'text-gray-900',
        solid: 'text-white',
    };
    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };
    return (_jsx("header", { className: clsx('sticky top-0 z-50', variantClasses[variant], className), children: _jsxs("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [_jsxs("div", { className: "flex justify-between items-center h-16", children: [_jsxs("div", { className: "flex items-center", children: [logo && (_jsx("div", { className: "flex-shrink-0 mr-3", children: logo })), _jsx("h1", { className: clsx('text-xl font-bold', textColorClasses[variant]), children: title })] }), _jsx("nav", { className: "hidden md:flex space-x-8", children: navigation.map((item) => (_jsx("a", { href: item.href, className: clsx('px-3 py-2 rounded-md text-sm font-medium transition-colors', item.active
                                    ? variant === 'solid'
                                        ? 'bg-blue-700 text-white'
                                        : 'bg-blue-100 text-blue-700'
                                    : variant === 'solid'
                                        ? 'text-blue-100 hover:text-white hover:bg-blue-700'
                                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'), ...(item.external && {
                                    target: '_blank',
                                    rel: 'noopener noreferrer'
                                }), children: item.label }, item.href))) }), _jsxs("div", { className: "flex items-center space-x-4", children: [actions && (_jsx("div", { className: "hidden md:flex items-center space-x-2", children: actions })), _jsx("button", { type: "button", className: clsx('md:hidden p-2 rounded-md', variant === 'solid'
                                        ? 'text-blue-100 hover:text-white hover:bg-blue-700'
                                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'), onClick: toggleMobileMenu, "aria-expanded": isMobileMenuOpen, "aria-label": "\u958B\u555F\u4E3B\u9078\u55AE", children: _jsx("svg", { className: "h-6 w-6", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: isMobileMenuOpen ? (_jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" })) : (_jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M4 6h16M4 12h16M4 18h16" })) }) })] })] }), isMobileMenuOpen && (_jsx("div", { className: "md:hidden", children: _jsxs("div", { className: "px-2 pt-2 pb-3 space-y-1 border-t border-gray-200", children: [navigation.map((item) => (_jsx("a", { href: item.href, className: clsx('block px-3 py-2 rounded-md text-base font-medium transition-colors', item.active
                                    ? variant === 'solid'
                                        ? 'bg-blue-700 text-white'
                                        : 'bg-blue-100 text-blue-700'
                                    : variant === 'solid'
                                        ? 'text-blue-100 hover:text-white hover:bg-blue-700'
                                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'), onClick: () => setIsMobileMenuOpen(false), ...(item.external && {
                                    target: '_blank',
                                    rel: 'noopener noreferrer'
                                }), children: item.label }, item.href))), actions && (_jsx("div", { className: "pt-4 border-t border-gray-200", children: actions }))] }) }))] }) }));
};
// 設定 displayName 方便 DevTools 調試
Header.displayName = 'Header';
export default Header;
//# sourceMappingURL=Header.js.map