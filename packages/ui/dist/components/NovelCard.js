import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import clsx from 'clsx';
/**
 * NovelCard 組件
 *
 * 顯示小說卡片，包含封面、標題、作者、狀態等資訊
 * 支援自定義點擊行為和路由
 */
const NovelCard = ({ novel, href, onClick, className, children, }) => {
    // 狀態樣式配置
    const statusClasses = {
        completed: 'bg-green-100 text-green-800',
        ongoing: 'bg-blue-100 text-blue-800',
    };
    // 基礎卡片內容
    const cardContent = (_jsxs("div", { className: "p-4", children: [_jsxs("div", { className: "flex gap-4", children: [_jsx("img", { src: novel.cover || '/default-cover.jpg', alt: novel.title, className: "w-16 h-20 object-cover rounded flex-shrink-0", loading: "lazy" }), _jsxs("div", { className: "flex-1 min-w-0", children: [_jsx("h3", { className: "font-semibold text-gray-900 truncate mb-1", children: novel.title }), _jsxs("p", { className: "text-sm text-gray-600 mb-2", children: ["\u4F5C\u8005\uFF1A", novel.author] }), _jsxs("div", { className: "flex items-center gap-2 text-xs text-gray-500", children: [_jsx("span", { className: clsx('px-2 py-1 rounded', statusClasses[novel.status]), children: novel.status === 'completed' ? '已完結' : '連載中' }), _jsxs("span", { children: ["\uD83D\uDC41\uFE0F ", novel.views.toLocaleString()] }), novel.favorites > 0 && (_jsxs("span", { children: ["\u2764\uFE0F ", novel.favorites.toLocaleString()] }))] }), novel.category && (_jsx("div", { className: "mt-1", children: _jsx("span", { className: "inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded", children: novel.category.name }) }))] })] }), novel.description && (_jsx("p", { className: "text-sm text-gray-600 mt-3 line-clamp-2", children: novel.description })), children] }));
    // 基礎樣式
    const baseClasses = clsx('bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow', className);
    // 如果有 href，渲染為連結
    if (href) {
        return (_jsx("a", { href: href, className: clsx(baseClasses, 'block'), onClick: onClick, children: cardContent }));
    }
    // 如果有 onClick，渲染為按鈕
    if (onClick) {
        return (_jsx("button", { type: "button", className: clsx(baseClasses, 'block w-full text-left'), onClick: onClick, children: cardContent }));
    }
    // 否則渲染為普通 div
    return (_jsx("div", { className: baseClasses, children: cardContent }));
};
// 設定 displayName 方便 DevTools 調試
NovelCard.displayName = 'NovelCard';
export default NovelCard;
//# sourceMappingURL=NovelCard.js.map